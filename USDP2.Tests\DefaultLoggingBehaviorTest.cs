using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.IO;

namespace USDP2.Tests
{
    /// <summary>
    /// Tests to verify the default logging behavior of USDP2.
    /// This test demonstrates exactly what happens when logging is used without any initialization.
    /// </summary>
    [TestClass]
    public class DefaultLoggingBehaviorTest
    {
        [TestMethod]
        public void TestDefaultLoggingBehavior()
        {
            // Capture console output to see what happens by default
            var originalOut = Console.Out;
            var stringWriter = new StringWriter();
            Console.SetOut(stringWriter);

            try
            {
                // Reset UsdpLogger to ensure we're testing default behavior
                UsdpLogger.Dispose();

                // Test 1: Call UsdpLogger.Log without any initialization
                Console.WriteLine("=== Testing UsdpLogger.Log (no initialization) ===");
                UsdpLogger.Log("TestEvent", new { Message = "This is a test", Value = 42 });

                // Test 2: Call Diagnostics.Log (backward compatibility)
                Console.WriteLine("=== Testing Diagnostics.Log (backward compatibility) ===");
                Diagnostics.Log("DiagnosticsTestEvent", new { Message = "This is a diagnostics test", Value = 123 });

                // Test 3: Test different log levels
                Console.WriteLine("=== Testing different log levels ===");
                UsdpLogger.Log("InfoEvent", new { Type = "Information" });
                UsdpLogger.Log("WarningTimeout", new { Type = "Warning" });
                UsdpLogger.Log("ErrorFailed", new { Type = "Error" });
                UsdpLogger.Log("DebugTrace", new { Type = "Debug" });

                // Test 4: Test different categories
                Console.WriteLine("=== Testing different categories ===");
                UsdpLogger.Log("BloomFilter.ServiceAdded", new { ServiceId = "test" });
                UsdpLogger.Log("TlsConnectionEstablished", new { Host = "example.com" });
                UsdpLogger.Log("NetworkPacketReceived", new { Size = 1024 });

                // Get the captured output
                var output = stringWriter.ToString();

                Console.SetOut(originalOut);
                Console.WriteLine("Captured output:");
                Console.WriteLine(output);

                // With Auto mode (default), we expect NO console output for class libraries
                // This is the correct behavior - logs go to system destinations (Event Log, files)
                var hasOnlyTestOutput = output.Contains("=== Testing") &&
                                       !output.Contains("USDP Event:") &&
                                       !output.Contains("TestEvent") &&
                                       !output.Contains("DiagnosticsTestEvent");

                Assert.IsTrue(hasOnlyTestOutput,
                    "Expected no USDP logging output to console in Auto mode (class library behavior). " +
                    "Logs should go to system destinations (Event Log on Windows, files on Unix).");

                // Verify the logging calls don't throw exceptions
                Assert.IsTrue(output.Contains("=== Testing"), "Test framework output should be present");
            }
            finally
            {
                Console.SetOut(originalOut);
            }
        }

        [TestMethod]
        public void TestDefaultLoggingConfiguration()
        {
            // Test the default configuration values
            var config = UsdpConfiguration.Instance;

            Console.WriteLine($"Default LogMode: {config.LogMode}");
            Console.WriteLine($"Default MinimumLogLevel: {config.MinimumLogLevel}");
            Console.WriteLine($"Default EnableLoggingMetrics: {config.EnableLoggingMetrics}");
            Console.WriteLine($"Default EnableStructuredLogging: {config.EnableStructuredLogging}");
            Console.WriteLine($"Default EnableConsoleFallback: {config.EnableConsoleFallback}");
            Console.WriteLine($"Default CustomLogFilePath: {config.CustomLogFilePath ?? "null"}");
            Console.WriteLine($"Default EventLogSourceName: {config.EventLogSourceName}");
            Console.WriteLine($"Default EventLogName: {config.EventLogName}");

            // Verify the new defaults (Auto mode for class libraries)
            Assert.AreEqual(UsdpConfiguration.LoggingMode.Auto, config.LogMode);
            Assert.AreEqual(Microsoft.Extensions.Logging.LogLevel.Information, config.MinimumLogLevel);
            Assert.AreEqual(true, config.EnableLoggingMetrics);
            Assert.AreEqual(true, config.EnableStructuredLogging);
            Assert.AreEqual(false, config.EnableConsoleFallback);
            Assert.AreEqual(null, config.CustomLogFilePath);
            Assert.AreEqual("USDP2", config.EventLogSourceName);
            Assert.AreEqual("Application", config.EventLogName);
        }

        [TestMethod]
        public void TestUsdpLoggerDefaults()
        {
            // Test UsdpLogger static defaults
            Console.WriteLine($"UsdpLogger.EnableMetrics: {UsdpLogger.EnableMetrics}");
            Console.WriteLine($"UsdpLogger.MinimumLogLevel: {UsdpLogger.MinimumLogLevel}");
            Console.WriteLine($"UsdpLogger.EnableConsoleFallback: {UsdpLogger.EnableConsoleFallback}");

            // Verify UsdpLogger defaults
            Assert.AreEqual(true, UsdpLogger.EnableMetrics);
            Assert.AreEqual(Microsoft.Extensions.Logging.LogLevel.Information, UsdpLogger.MinimumLogLevel);
            Assert.AreEqual(false, UsdpLogger.EnableConsoleFallback);
        }

        [TestMethod]
        public void TestWhatHappensWithNoInitialization()
        {
            // Reset to ensure clean state
            UsdpLogger.Dispose();

            // Capture console output
            var originalOut = Console.Out;
            var stringWriter = new StringWriter();
            Console.SetOut(stringWriter);

            try
            {
                // Call logging without any explicit initialization
                UsdpLogger.Log("TestWithoutInit", new { Message = "Testing without initialization" });

                var output = stringWriter.ToString();
                Console.SetOut(originalOut);

                Console.WriteLine("Output when logging without initialization:");
                Console.WriteLine(output);

                // With Auto mode, there should be NO console output (logs go to system destinations)
                // But the logging call should not throw exceptions
                Assert.IsTrue(output.Length == 0 || !output.Contains("TestWithoutInit"),
                    "Expected no console output in Auto mode - logs should go to system destinations");

                // Verify that UsdpLogger is now initialized (the call should have triggered auto-initialization)
                // We can't directly test this without exposing internal state, but we can verify no exceptions occurred
                Assert.IsTrue(true, "Logging without initialization should work without throwing exceptions");
            }
            finally
            {
                Console.SetOut(originalOut);
            }
        }

        [TestMethod]
        public void TestConsoleLoggingWhenExplicitlyConfigured()
        {
            // Test that Console mode can be configured without throwing exceptions
            var originalLogMode = UsdpConfiguration.Instance.LogMode;

            try
            {
                // Set Console mode explicitly
                UsdpConfiguration.Instance.LogMode = UsdpConfiguration.LoggingMode.Console;

                // Reset UsdpLogger to pick up the new configuration
                UsdpLogger.Dispose();

                // Verify that Console mode can be used without exceptions
                try
                {
                    UsdpLogger.Log("ConsoleTestEvent", new { Message = "This should work in console mode" });
                    Assert.IsTrue(true, "Console mode should work without throwing exceptions");
                }
                catch (Exception ex)
                {
                    Assert.Fail($"Console mode logging failed: {ex.Message}");
                }

                // Verify the configuration was applied
                Assert.AreEqual(UsdpConfiguration.LoggingMode.Console, UsdpConfiguration.Instance.LogMode);

                Console.WriteLine("Console mode configuration test passed");
            }
            finally
            {
                // Restore original configuration
                UsdpConfiguration.Instance.LogMode = originalLogMode;
                UsdpLogger.Dispose(); // Reset to pick up original config
            }
        }

        [TestMethod]
        public void TestPlatformSpecificBehavior()
        {
            // Test that the platform-specific logging provider works
            var config = UsdpConfiguration.Instance;

            Console.WriteLine($"Current platform: {Environment.OSVersion.Platform}");
            Console.WriteLine($"Is Windows: {System.Runtime.InteropServices.RuntimeInformation.IsOSPlatform(System.Runtime.InteropServices.OSPlatform.Windows)}");
            Console.WriteLine($"Is Linux: {System.Runtime.InteropServices.RuntimeInformation.IsOSPlatform(System.Runtime.InteropServices.OSPlatform.Linux)}");
            Console.WriteLine($"Is macOS: {System.Runtime.InteropServices.RuntimeInformation.IsOSPlatform(System.Runtime.InteropServices.OSPlatform.OSX)}");

            // Verify that Auto mode creates a logger factory without throwing exceptions
            UsdpLogger.Dispose();

            try
            {
                UsdpLogger.Log("PlatformTest", new { Platform = Environment.OSVersion.Platform.ToString() });
                Assert.IsTrue(true, "Platform-specific logging should work without exceptions");
            }
            catch (Exception ex)
            {
                Assert.Fail($"Platform-specific logging failed: {ex.Message}");
            }
        }
    }
}
