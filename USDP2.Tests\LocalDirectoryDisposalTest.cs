using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Threading.Tasks;
using USDP2.Tests.Mocks;

namespace USDP2.Tests
{
    /// <summary>
    /// Tests for the proper async disposal pattern implementation in LocalDirectory.
    /// 
    /// These tests verify that LocalDirectory follows .NET best practices for disposal:
    /// - Thread-safe disposal
    /// - Proper exception handling during disposal
    /// - Correct resource cleanup ordering
    /// - Multiple disposal attempts are safe
    /// - Both sync and async disposal work correctly
    /// </summary>
    [TestClass]
    public class LocalDirectoryDisposalTest
    {
        [TestMethod]
        public async Task TestAsyncDisposalPattern()
        {
            // Arrange
            var mockSender = new MockNetworkSender();
            var mockReceiver = new MockNetworkReceiver();
            var directory = new LocalDirectory(mockSender, mockReceiver, "*********", 12345);

            // Act & Assert - Async disposal should work without exceptions
            await directory.DisposeAsync();

            // Verify that the object is properly disposed
            Assert.ThrowsException<ObjectDisposedException>(() => directory.GetCachedAdvertisements());
        }

        [TestMethod]
        public void TestSyncDisposalPattern()
        {
            // Arrange
            var mockSender = new MockNetworkSender();
            var mockReceiver = new MockNetworkReceiver();
            var directory = new LocalDirectory(mockSender, mockReceiver, "*********", 12345);

            // Act & Assert - Sync disposal should work without exceptions
            directory.Dispose();

            // Verify that the object is properly disposed
            Assert.ThrowsException<ObjectDisposedException>(() => directory.GetCachedAdvertisements());
        }

        [TestMethod]
        public async Task TestMultipleAsyncDisposalIsSafe()
        {
            // Arrange
            var mockSender = new MockNetworkSender();
            var mockReceiver = new MockNetworkReceiver();
            var directory = new LocalDirectory(mockSender, mockReceiver, "*********", 12345);

            // Act - Multiple async disposals should be safe
            await directory.DisposeAsync();
            await directory.DisposeAsync(); // Second disposal should be safe
            await directory.DisposeAsync(); // Third disposal should be safe

            // Assert - Object should still be disposed
            Assert.ThrowsException<ObjectDisposedException>(() => directory.GetCachedAdvertisements());
        }

        [TestMethod]
        public void TestMultipleSyncDisposalIsSafe()
        {
            // Arrange
            var mockSender = new MockNetworkSender();
            var mockReceiver = new MockNetworkReceiver();
            var directory = new LocalDirectory(mockSender, mockReceiver, "*********", 12345);

            // Act - Multiple sync disposals should be safe
            directory.Dispose();
            directory.Dispose(); // Second disposal should be safe
            directory.Dispose(); // Third disposal should be safe

            // Assert - Object should still be disposed
            Assert.ThrowsException<ObjectDisposedException>(() => directory.GetCachedAdvertisements());
        }

        [TestMethod]
        public async Task TestMixedDisposalIsSafe()
        {
            // Arrange
            var mockSender = new MockNetworkSender();
            var mockReceiver = new MockNetworkReceiver();
            var directory = new LocalDirectory(mockSender, mockReceiver, "*********", 12345);

            // Act - Mixed async and sync disposal should be safe
            await directory.DisposeAsync();
            directory.Dispose(); // Should be safe after async disposal

            // Assert - Object should still be disposed
            Assert.ThrowsException<ObjectDisposedException>(() => directory.GetCachedAdvertisements());
        }

        [TestMethod]
        public async Task TestDisposalWithAsyncDisposableComponents()
        {
            // Arrange
            var mockAsyncSender = new MockAsyncNetworkSender();
            var mockAsyncReceiver = new MockAsyncNetworkReceiver();
            var directory = new LocalDirectory(mockAsyncSender, mockAsyncReceiver, "*********", 12345);

            // Act
            await directory.DisposeAsync();

            // Assert
            Assert.IsTrue(mockAsyncSender.WasAsyncDisposed, "Async sender should have been disposed asynchronously");
            Assert.IsTrue(mockAsyncReceiver.WasAsyncDisposed, "Async receiver should have been disposed asynchronously");
            Assert.ThrowsException<ObjectDisposedException>(() => directory.GetCachedAdvertisements());
        }

        [TestMethod]
        public async Task TestDisposalExceptionHandling()
        {
            // Arrange
            var faultySender = new FaultyNetworkSender();
            var faultyReceiver = new FaultyNetworkReceiver();
            var directory = new LocalDirectory(faultySender, faultyReceiver, "*********", 12345);

            // Act & Assert - Disposal should handle exceptions gracefully
            var exception = await Assert.ThrowsExceptionAsync<AggregateException>(async () =>
            {
                await directory.DisposeAsync();
            });

            // Verify that we got the expected aggregate exception with inner exceptions
            Assert.IsTrue(exception.InnerExceptions.Count > 0, "Should have collected disposal exceptions");
            
            // Even with exceptions, the object should be marked as disposed
            Assert.ThrowsException<ObjectDisposedException>(() => directory.GetCachedAdvertisements());
        }

        [TestMethod]
        public async Task TestUsingStatementWithAsyncDisposable()
        {
            // Arrange & Act
            MockNetworkSender? capturedSender = null;
            MockNetworkReceiver? capturedReceiver = null;

            await using (var directory = new LocalDirectory(
                new MockNetworkSender(), 
                new MockNetworkReceiver(), 
                "*********", 
                12345))
            {
                // Capture references to verify disposal
                capturedSender = (MockNetworkSender)directory.GetType()
                    .GetField("_multicastSender", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)
                    ?.GetValue(directory) as MockNetworkSender;
                
                capturedReceiver = (MockNetworkReceiver)directory.GetType()
                    .GetField("_multicastReceiver", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)
                    ?.GetValue(directory) as MockNetworkReceiver;

                // Use the directory
                var ads = directory.GetCachedAdvertisements();
                Assert.IsNotNull(ads);
            }
            // Directory should be automatically disposed here

            // Assert - Verify disposal occurred
            Assert.IsTrue(capturedSender?.WasDisposed ?? false, "Sender should have been disposed");
            Assert.IsTrue(capturedReceiver?.WasDisposed ?? false, "Receiver should have been disposed");
        }

        [TestMethod]
        public void TestFinalizerDoesNotThrow()
        {
            // Arrange - Create directory and let it go out of scope without disposal
            CreateDirectoryAndLetItGoOutOfScope();

            // Act - Force garbage collection to trigger finalizer
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();

            // Assert - If we get here without exceptions, the finalizer worked correctly
            Assert.IsTrue(true, "Finalizer should not throw exceptions");
        }

        private static void CreateDirectoryAndLetItGoOutOfScope()
        {
            // Create a directory that will be finalized
            var directory = new LocalDirectory(
                new MockNetworkSender(), 
                new MockNetworkReceiver(), 
                "*********", 
                12345);
            
            // Use it briefly
            var ads = directory.GetCachedAdvertisements();
            
            // Let it go out of scope without explicit disposal
            // This will test the finalizer path
        }

        [TestMethod]
        public async Task TestThreadSafeDisposal()
        {
            // Arrange
            var mockSender = new MockNetworkSender();
            var mockReceiver = new MockNetworkReceiver();
            var directory = new LocalDirectory(mockSender, mockReceiver, "*********", 12345);

            // Act - Attempt concurrent disposal from multiple threads
            var tasks = new Task[10];
            for (int i = 0; i < 5; i++)
            {
                tasks[i] = Task.Run(async () => await directory.DisposeAsync());
            }
            for (int i = 5; i < 10; i++)
            {
                tasks[i] = Task.Run(() => directory.Dispose());
            }

            // Wait for all disposal attempts to complete
            await Task.WhenAll(tasks);

            // Assert - Object should be disposed and no exceptions should have occurred
            Assert.ThrowsException<ObjectDisposedException>(() => directory.GetCachedAdvertisements());
        }
    }
}
