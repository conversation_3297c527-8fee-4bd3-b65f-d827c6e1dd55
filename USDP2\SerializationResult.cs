using System;

namespace USDP2
{
    /// <summary>
    /// Represents the result of a serialization or deserialization operation.
    /// </summary>
    /// <typeparam name="T">The type of the result value.</typeparam>
    public class SerializationResult<T>
    {
        /// <summary>
        /// Gets a value indicating whether the operation was successful.
        /// </summary>
        public bool IsSuccess { get; }

        /// <summary>
        /// Gets the result value if the operation was successful.
        /// </summary>
        public T? Value { get; }

        /// <summary>
        /// Gets the error message if the operation failed.
        /// </summary>
        public string? ErrorMessage { get; }

        /// <summary>
        /// Gets the exception that caused the failure, if any.
        /// </summary>
        public Exception? Exception { get; }

        /// <summary>
        /// Gets the error code that categorizes the failure.
        /// </summary>
        public SerializationErrorCode ErrorCode { get; }

        /// <summary>
        /// Gets the error type that categorizes the failure.
        /// </summary>
        public SerializationErrorType ErrorType { get; }

        /// <summary>
        /// Initializes a new instance of the <see cref="SerializationResult{T}"/> class for a successful operation.
        /// </summary>
        /// <param name="value">The result value.</param>
        private SerializationResult(T value)
        {
            IsSuccess = true;
            Value = value;
            ErrorMessage = null;
            Exception = null;
            ErrorCode = SerializationErrorCode.None;
            ErrorType = SerializationErrorType.None;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="SerializationResult{T}"/> class for a failed operation.
        /// </summary>
        /// <param name="errorMessage">The error message.</param>
        /// <param name="errorType">The error type.</param>
        /// <param name="exception">The exception that caused the failure, if any.</param>
        private SerializationResult(string errorMessage, SerializationErrorType errorType, Exception? exception = null)
        {
            IsSuccess = false;
            Value = default;
            ErrorMessage = errorMessage;
            Exception = exception;
            ErrorCode = SerializationErrorCode.Unknown; // Default for backward compatibility
            ErrorType = errorType;
        }

        /// <summary>
        /// Creates a successful result with the specified value.
        /// </summary>
        /// <param name="value">The result value.</param>
        /// <returns>A successful <see cref="SerializationResult{T}"/>.</returns>
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Design", "CA1000:Do not declare static members on generic types", Justification = "Factory methods provide better API ergonomics and follow established .NET patterns like Task<T>.FromResult")]
        public static SerializationResult<T> Success(T value)
        {
            return new SerializationResult<T>(value);
        }

        /// <summary>
        /// Creates a failed result with the specified error message and type.
        /// </summary>
        /// <param name="errorMessage">The error message.</param>
        /// <param name="errorType">The error type.</param>
        /// <param name="exception">The exception that caused the failure, if any.</param>
        /// <returns>A failed <see cref="SerializationResult{T}"/>.</returns>
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Design", "CA1000:Do not declare static members on generic types", Justification = "Factory methods provide better API ergonomics and follow established .NET patterns like Task<T>.FromResult")]
        public static SerializationResult<T> Failure(string errorMessage, SerializationErrorType errorType, Exception? exception = null)
        {
            return new SerializationResult<T>(errorMessage, errorType, exception);
        }

        /// <summary>
        /// Creates a failed result for a null input.
        /// </summary>
        /// <returns>A failed <see cref="SerializationResult{T}"/>.</returns>
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Design", "CA1000:Do not declare static members on generic types", Justification = "Factory methods provide better API ergonomics and follow established .NET patterns like Task<T>.FromResult")]
        public static SerializationResult<T> NullInput()
        {
            return Failure("Input is null or empty", SerializationErrorType.NullInput);
        }

        /// <summary>
        /// Creates a failed result for an invalid format.
        /// </summary>
        /// <param name="errorMessage">The error message.</param>
        /// <param name="exception">The exception that caused the failure, if any.</param>
        /// <returns>A failed <see cref="SerializationResult{T}"/>.</returns>
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Design", "CA1000:Do not declare static members on generic types", Justification = "Factory methods provide better API ergonomics and follow established .NET patterns like Task<T>.FromResult")]
        public static SerializationResult<T> InvalidFormat(string errorMessage, Exception? exception = null)
        {
            return Failure(errorMessage, SerializationErrorType.DeserializationError, exception);
        }

        /// <summary>
        /// Creates a failed result for a missing required field.
        /// </summary>
        /// <param name="fieldName">The name of the missing field.</param>
        /// <returns>A failed <see cref="SerializationResult{T}"/>.</returns>
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Design", "CA1000:Do not declare static members on generic types", Justification = "Factory methods provide better API ergonomics and follow established .NET patterns like Task<T>.FromResult")]
        public static SerializationResult<T> MissingRequiredField(string fieldName)
        {
            return Failure($"Missing required field: {fieldName}", SerializationErrorType.DeserializationError);
        }

        /// <summary>
        /// Creates a failed result for an invalid field value.
        /// </summary>
        /// <param name="fieldName">The name of the invalid field.</param>
        /// <param name="errorMessage">The error message.</param>
        /// <returns>A failed <see cref="SerializationResult{T}"/>.</returns>
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Design", "CA1000:Do not declare static members on generic types", Justification = "Factory methods provide better API ergonomics and follow established .NET patterns like Task<T>.FromResult")]
        public static SerializationResult<T> InvalidFieldValue(string fieldName, string errorMessage)
        {
            return Failure($"Invalid value for field {fieldName}: {errorMessage}", SerializationErrorType.DeserializationError);
        }

        /// <summary>
        /// Creates a failed result for an unknown error.
        /// </summary>
        /// <param name="errorMessage">The error message.</param>
        /// <param name="exception">The exception that caused the failure, if any.</param>
        /// <returns>A failed <see cref="SerializationResult{T}"/>.</returns>
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Design", "CA1000:Do not declare static members on generic types", Justification = "Factory methods provide better API ergonomics and follow established .NET patterns like Task<T>.FromResult")]
        public static SerializationResult<T> UnknownError(string errorMessage, Exception? exception = null)
        {
            return Failure(errorMessage, SerializationErrorType.UnexpectedError, exception);
        }
    }

    /// <summary>
    /// Represents the error codes for serialization and deserialization operations.
    /// </summary>
    public enum SerializationErrorCode
    {
        /// <summary>
        /// No error.
        /// </summary>
        None = 0,

        /// <summary>
        /// Input is null or empty.
        /// </summary>
        NullInput = 1,

        /// <summary>
        /// Input has an invalid format.
        /// </summary>
        InvalidFormat = 2,

        /// <summary>
        /// A required field is missing.
        /// </summary>
        MissingRequiredField = 3,

        /// <summary>
        /// A field has an invalid value.
        /// </summary>
        InvalidFieldValue = 4,

        /// <summary>
        /// An unknown error occurred.
        /// </summary>
        Unknown = 999
    }

    /// <summary>
    /// Represents the types of serialization errors for categorization.
    /// </summary>
    public enum SerializationErrorType
    {
        /// <summary>
        /// No error occurred.
        /// </summary>
        None = 0,

        /// <summary>
        /// Input validation error (null, empty, or invalid input).
        /// </summary>
        NullInput = 1,

        /// <summary>
        /// Serialization process error.
        /// </summary>
        SerializationError = 2,

        /// <summary>
        /// Deserialization process error.
        /// </summary>
        DeserializationError = 3,

        /// <summary>
        /// Unexpected error during operation.
        /// </summary>
        UnexpectedError = 4
    }
}