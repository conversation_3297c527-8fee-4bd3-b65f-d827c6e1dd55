using System;
using System.Collections.Concurrent;
using System.Diagnostics;
using System.IO;
using System.Text.Json;
using Microsoft.Extensions.Logging;

namespace USDP2
{
    /// <summary>
    /// Provides structured logging and basic metrics for USDP protocol operations.
    ///
    /// <para><strong>DEPRECATED:</strong> This class is maintained for backward compatibility.</para>
    /// <para><strong>RECOMMENDED:</strong> Use <see cref="UsdpLogger"/> for new code.</para>
    ///
    /// <para>
    /// This class now serves as a compatibility layer over the new Microsoft.Extensions.Logging-based
    /// UsdpLogger system. All logging calls are forwarded to UsdpLogger, which provides:
    /// - Better performance and structured logging
    /// - Integration with .NET logging ecosystem
    /// - Configurable log providers (Console, Debug, File, etc.)
    /// - Automatic log level detection
    /// - Enhanced metrics collection
    /// </para>
    ///
    /// <para>
    /// Migration path:
    /// - Replace Diagnostics.Log() calls with UsdpLogger.Log()
    /// - Use UsdpLogger.GetLogger() for typed loggers
    /// - Configure logging through UsdpConfiguration properties
    /// - Initialize UsdpLogger.Initialize() in application startup
    /// </para>
    /// </summary>
    public static class Diagnostics
    {
        /// <summary>
        /// Defines the logging levels.
        /// </summary>
        public enum LogLevel
        {
            /// <summary>
            /// Debug level logging.
            /// </summary>
            Debug,
            /// <summary>
            /// Information level logging.
            /// </summary>
            Info,
            /// <summary>
            /// Warning level logging.
            /// </summary>
            Warning,
            /// <summary>
            /// Error level logging.
            /// </summary>
            Error
        }
        /// <summary>
        /// Gets or sets the current logging level.
        /// </summary>
        public static LogLevel CurrentLevel { get; set; } = LogLevel.Info;
        /// <summary>
        /// Gets or sets the file path for logging. If null, logging will only be to the console.
        /// </summary>
        public static string? LogFilePath { get; set; }

        // Structured log event  


        /// <summary>
        /// Logs an event with the specified event type and data.
        ///
        /// <para><strong>DEPRECATED:</strong> Use <see cref="UsdpLogger.Log(string, object)"/> instead.</para>
        ///
        /// This method provides backward compatibility by forwarding calls to the new UsdpLogger system.
        /// The new system provides better performance, structured logging, and integration with the
        /// Microsoft.Extensions.Logging ecosystem.
        /// </summary>
        /// <param name="eventType">
        /// The type/category of the event (e.g., "ServiceDiscovered", "TlsError").
        /// Used to determine the appropriate log level and logger category.
        /// </param>
        /// <param name="data">
        /// The data to log. Can be any object - will be serialized appropriately.
        /// </param>
        /// <remarks>
        /// This method now delegates to UsdpLogger.Log() which provides:
        /// - Automatic log level detection based on event type
        /// - Structured logging with proper categorization
        /// - Better performance and thread safety
        /// - Integration with .NET logging providers
        ///
        /// For new code, use UsdpLogger.Log() directly for better performance and features.
        /// </remarks>
        public static void Log(string eventType, object? data = null)
        {
            // Delegate to the new UsdpLogger system
            UsdpLogger.Log(eventType, data);

            // Also handle legacy LogFilePath behavior for backward compatibility
            if (!string.IsNullOrEmpty(LogFilePath))
            {
                try
                {
                    var logEntry = new
                    {
                        Timestamp = DateTimeOffset.UtcNow,
                        EventType = eventType,
                        Data = data
                    };
                    var serializedLogEntry = JsonSerializer.Serialize(logEntry);
                    File.AppendAllText(LogFilePath, serializedLogEntry + Environment.NewLine);
                }
                catch (Exception ex)
                {
                    // Use UsdpLogger for error logging
                    UsdpLogger.Log("DiagnosticsLegacyFileError", new { Error = ex.Message, EventType = eventType });
                }
            }
        }

        // Metrics
        /// <summary>
        /// The service count.
        /// </summary>
        private static int _serviceCount;
        /// <summary>
        /// Query latencies.
        /// </summary>
        private static readonly ConcurrentQueue<long> _queryLatencies = new();

        /// <summary>
        /// Set service count.
        /// </summary>
        /// <param name="count">The count.</param>
        public static void SetServiceCount(int count) => _serviceCount = count;
        /// <summary>
        /// Get service count.
        /// </summary>
        /// <returns>An <see cref="int"/></returns>
        public static int GetServiceCount() => _serviceCount;

        /// <summary>
        /// Record query latency.
        /// </summary>
        /// <param name="ms">The ms.</param>
        public static void RecordQueryLatency(long ms)
        {
            _queryLatencies.Enqueue(ms);
            // Keep only the last 100 latencies
            while (_queryLatencies.Count > 100 && _queryLatencies.TryDequeue(out _)) { }
        }

        /// <summary>
        /// Get average query latency.
        /// </summary>
        /// <returns>A <see cref="double"/></returns>
        public static double GetAverageQueryLatency()
        {
            if (_queryLatencies.IsEmpty) return 0;
            long sum = 0;
            int count = 0;
            foreach (var ms in _queryLatencies)
            {
                sum += ms;
                count++;
            }
            return count == 0 ? 0 : (double)sum / count;
        }
    }
}