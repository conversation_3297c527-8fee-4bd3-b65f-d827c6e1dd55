# USDP2 - Universal Service Discovery Protocol 2

A modern, secure, and scalable service discovery protocol implementation for .NET 8, featuring advanced TLS configuration management, enterprise-grade security, and multi-platform support.

## 🚀 Key Features

### 🔒 Advanced Security
- **OS-Managed TLS**: Delegates cipher suite and TLS version selection to the operating system for optimal security
- **Graceful TLS Fallback**: Automatic TLS version fallback with configurable security boundaries
- **Multiple Authentication Methods**: PSK, X.509 certificates, and Ed25519 digital signatures
- **Enterprise Key Management**: Support for Windows DPAPI, Azure Key Vault, and HashiCorp Vault
- **Zero Hardcoded Secrets**: All secrets managed through secure backends

### 🌐 Network Protocols
- **Multi-Transport Support**: UDP multicast, TCP, HTTP/HTTPS
- **Protocol Translation**: CoAP to HTTP translation for IoT integration
- **NAT Traversal**: STUN-based public endpoint discovery
- **mDNS Integration**: Seamless integration with multicast DNS

### ⚙️ Configuration Management
- **Centralized Configuration**: All settings managed through `UsdpConfiguration.cs`
- **Environment-Specific Settings**: Different configurations for development, staging, and production
- **Runtime Reconfiguration**: Dynamic configuration updates without restart

### 🔧 Developer Experience
- **Comprehensive Documentation**: Detailed API documentation and examples
- **Extensive Testing**: Unit tests, integration tests, and security validation
- **Modern .NET**: Built on .NET 8 with nullable reference types and latest C# features

## 🛡️ Security Architecture

### TLS Configuration Management

USDP2 implements a three-tier TLS configuration approach:

1. **OS-Managed TLS (Default)** - Leverages operating system cipher suite management
2. **Graceful Fallback** - Automatic TLS version fallback for legacy compatibility
3. **Manual Override** - Advanced configuration for specialized requirements (easily removable)

```csharp
// Production configuration (recommended)
UsdpConfiguration.Instance.UseOSManagedTls = true;              // OS-managed security
UsdpConfiguration.Instance.EnableTlsFallback = true;           // Compatibility fallback
UsdpConfiguration.Instance.AllowTlsDowngrade = false;          // Security boundary
UsdpConfiguration.Instance.FallbackTlsVersions = ["1.3", "1.2"]; // Modern TLS only
```

### Authentication Methods

**Pre-Shared Key (PSK) Authentication:**
```csharp
var configProvider = new ConfigurationProvider(configPath, KeyManagementBackend.WindowsDPAPI);
string psk = await configProvider.GetSecretAsync("AuthPsk");
var pskProvider = new PskAuthenticationProvider(psk);
bool isAuthenticated = await pskProvider.AuthenticateAsync(clientPsk);
```

**Certificate-Based Authentication:**
```csharp
var trustedCerts = LoadTrustedCertificates();
var certProvider = new CertificateAuthenticationProvider(trustedCerts, validateChain: true);
bool isAuthenticated = certProvider.Authenticate(clientCertificateBase64);
```

## 🚀 Quick Start

### 1. Basic Service Discovery

```csharp
using USDP2;

// Configure multicast discovery
var config = UsdpConfiguration.Instance;
config.DefaultMulticastAddress = "***************";
config.DefaultMulticastPort = 5353;

// Create network components
var sender = new UdpNetworkSender();
var receiver = new UdpNetworkReceiver(config.DefaultMulticastPort, true, config.DefaultMulticastAddress);

// Create local directory
var localDirectory = new LocalDirectory(sender, receiver,
    config.DefaultMulticastAddress, config.DefaultMulticastPort);

// Start discovery
await localDirectory.StartAsync(cancellationToken);
```

### 2. Secure HTTPS Communication

```csharp
// Configure secure HTTP sender with OS-managed TLS
UsdpConfiguration.Instance.UseHttps = true;
UsdpConfiguration.Instance.UseOSManagedTls = true;

var httpSender = new HttpNetworkSender();
await httpSender.SendAsync(data, "secure.example.com", 8443);
```

### 3. Service Advertisement

```csharp
var serviceId = new ServiceIdentifier("home/lighting", "bulb1");
var endpoint = new TransportEndpoint
{
    Protocol = "coap+udp",
    Address = "*************",
    Port = 5683,
    Security = "psk-tls1.3"
};

var advertisement = new ServiceAdvertisement
{
    ServiceId = serviceId,
    Endpoints = new[] { endpoint },
    Metadata = new Dictionary<string, string>
    {
        { "type", "lighting" },
        { "location", "living-room" }
    },
    Ttl = TimeSpan.FromMinutes(5)
};

await localDirectory.AdvertiseAsync(advertisement);
```

## 📋 Configuration Options

### Security Configuration

| Setting | Default | Description |
|---------|---------|-------------|
| `DefaultSecurity` | "psk-tls1.3" | Default security protocol |
| `UseHttps` | true | HTTPS preference for HTTP transport |
| `UseOSManagedTls` | true | OS-managed TLS configuration |
| `EnableTlsFallback` | true | Graceful TLS fallback |
| `AllowTlsDowngrade` | false | Allow TLS version downgrade |
| `EnableManualTlsOverride` | false | Enable manual TLS configuration |

### Network Configuration

| Setting | Default | Description |
|---------|---------|-------------|
| `DefaultMulticastAddress` | "***************" | Multicast address for local discovery |
| `DefaultMulticastPort` | 5353 | Multicast port for local discovery |
| `DefaultHttpPort` | 8080 | HTTP port for global discovery |
| `DefaultHttpsPort` | 8443 | HTTPS port for secure global discovery |
| `NetworkTimeout` | 30 seconds | Network operation timeout |

## 🏗️ Architecture

### Core Components

- **LocalDirectory**: Manages local service discovery and advertisement
- **NetworkSenders/Receivers**: Handle different transport protocols (UDP, TCP, HTTP)
- **TlsConfigurationManager**: Manages TLS configuration and fallback mechanisms
- **ConfigurationProvider**: Secure configuration and secret management
- **Authentication Providers**: Handle different authentication methods

### Security Components

- **TlsConfigurationManager**: Core TLS management with OS delegation
- **TlsOverrideProvider**: Manual TLS configuration (removable module)
- **KeyManagementProvider**: Multi-backend key storage
- **CertificateAuthenticationProvider**: X.509 certificate validation
- **PskAuthenticationProvider**: Pre-shared key authentication

## 🔧 Development

### Prerequisites

- .NET 8.0 SDK
- Visual Studio 2022 or VS Code
- Windows, Linux, or macOS

### Building

```bash
dotnet build USDP2.sln
```

### Running Tests

```bash
dotnet test USDP2.Tests/
```

### Security Testing

The project includes comprehensive security testing:

```bash
# Run all tests including security validation
dotnet test --configuration Release

# Run specific security tests
dotnet test --filter "Category=Security"
```

## 📚 Documentation

### Key Documents

- **[Security Implementation Report](USDP2_Security_Implementation_Report.md)**: Comprehensive security architecture and implementation details
- **[Security Improvements](SecurityImprovements.md)**: Security enhancements and best practices
- **[Configuration Analysis](ConfigurationCentralizationAnalysis.md)**: Configuration management and centralization
- **[Test Documentation](USDP2-Test-Documentation.md)**: Testing strategies and validation

### API Documentation

The project generates comprehensive XML documentation. Build the project to generate documentation files:

```bash
dotnet build --configuration Release
```

Documentation files are generated in `bin/Release/net8.0/USDP2.xml`.

## 🌍 Platform Support

### Supported Platforms

- **Windows**: Full support with Windows DPAPI key management
- **Linux**: Full support with OpenSSL TLS implementation
- **macOS**: Full support with Secure Transport TLS implementation
- **Docker**: Container deployment supported

### TLS Implementation by Platform

| Platform | TLS Implementation | Cipher Suite Management | Hardware Acceleration |
|----------|-------------------|-------------------------|----------------------|
| Windows | Schannel | OS-managed | AES-NI, others |
| Linux | OpenSSL | Distribution-maintained | AES-NI, others |
| macOS | Secure Transport | Apple-managed | Hardware optimized |

## 🔐 Security Best Practices

### Production Deployment

1. **Enable OS-Managed TLS**:
   ```csharp
   UsdpConfiguration.Instance.UseOSManagedTls = true;
   ```

2. **Configure Security Boundaries**:
   ```csharp
   UsdpConfiguration.Instance.AllowTlsDowngrade = false;
   UsdpConfiguration.Instance.FallbackTlsVersions = ["1.3", "1.2"];
   ```

3. **Use Enterprise Key Management**:
   ```csharp
   var configProvider = new ConfigurationProvider(
       configPath, KeyManagementBackend.AzureKeyVault,
       vaultUri: "https://your-vault.vault.azure.net/");
   ```

4. **Enable Security Monitoring**:
   ```csharp
   // Monitor TLS fallback events
   Diagnostics.Log("TlsFallback", new {
       Message = "TLS fallback occurred",
       OriginalVersion = "1.3",
       FallbackVersion = "1.2"
   });
   ```

### Security Monitoring

Monitor these key security metrics:

- **TLS Version Distribution**: Track which TLS versions are being used
- **Fallback Frequency**: Monitor how often TLS fallback occurs
- **Authentication Failures**: Track failed authentication attempts
- **Certificate Validation Errors**: Monitor certificate-related issues

## 🤝 Contributing

### Development Guidelines

1. **Security First**: All changes must maintain or improve security posture
2. **Comprehensive Testing**: Include unit tests, integration tests, and security validation
3. **Documentation**: Update relevant documentation for any changes
4. **Code Quality**: Follow established coding standards and use static analysis

### Submitting Changes

1. Fork the repository
2. Create a feature branch
3. Implement changes with tests
4. Update documentation
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE.txt](LICENSE.txt) file for details.

## 🆘 Support

### Getting Help

- **Documentation**: Check the comprehensive documentation in the `/docs` folder
- **Issues**: Report bugs and request features through GitHub Issues
- **Security**: Report security vulnerabilities through responsible disclosure

### Troubleshooting

**Common TLS Issues:**

1. **Certificate Validation Failures**:
   - Check certificate expiration dates
   - Verify certificate chain completeness
   - Ensure trusted root certificates are installed

2. **TLS Fallback Issues**:
   - Review `AllowTlsDowngrade` setting
   - Check `FallbackTlsVersions` configuration
   - Monitor security logs for fallback events

3. **Performance Issues**:
   - Enable hardware acceleration where available
   - Consider connection pooling for high-throughput scenarios
   - Monitor TLS handshake performance

## 🚀 Roadmap

### Upcoming Features

- **Ed25519 Implementation**: Complete Ed25519 signature verification
- **OSCORE Support**: Object Security for Constrained RESTful Environments
- **OAuth2 Integration**: Modern authentication and authorization
- **Quantum-Resistant Algorithms**: Preparation for post-quantum cryptography
- **Advanced Analytics**: ML-based threat detection and automated response

### Performance Improvements

- **Connection Pooling**: Optimize HTTP/HTTPS connection reuse
- **Async Optimization**: Further async/await pattern improvements
- **Memory Management**: Reduce allocations in hot paths
- **Caching**: Intelligent caching for frequently accessed data