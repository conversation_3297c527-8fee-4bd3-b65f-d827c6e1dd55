using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace USDP2.Tests.Mocks
{
    /// <summary>
    /// Mock network sender for testing disposal patterns.
    /// </summary>
    public class MockNetworkSender : INetworkSender, IDisposable
    {
        public List<(byte[] data, string address, int port)> SentMessages { get; } = new();
        public bool WasDisposed { get; private set; }

        public Task SendAsync(byte[] data, string address, int port, CancellationToken cancellationToken = default)
        {
            if (WasDisposed)
                throw new ObjectDisposedException(nameof(MockNetworkSender));

            SentMessages.Add((data, address, port));
            return Task.CompletedTask;
        }

        public void Dispose()
        {
            WasDisposed = true;
        }
    }

    /// <summary>
    /// Mock network receiver for testing disposal patterns.
    /// </summary>
    public class MockNetworkReceiver : INetworkReceiver, IDisposable
    {
        public bool WasDisposed { get; private set; }
        private Func<byte[], string, int, Task>? _messageHandler;

        public Task StartReceivingAsync(Func<byte[], string, int, Task> onMessageReceived, CancellationToken cancellationToken = default)
        {
            if (WasDisposed)
                throw new ObjectDisposedException(nameof(MockNetworkReceiver));

            _messageHandler = onMessageReceived;
            return Task.CompletedTask;
        }

        public async ValueTask DisposeAsync()
        {
            await Task.CompletedTask;
            WasDisposed = true;
        }

        public void Dispose()
        {
            WasDisposed = true;
        }

        // Helper method to simulate receiving a message
        public async Task SimulateMessageReceived(byte[] data, string address, int port)
        {
            if (_messageHandler != null && !WasDisposed)
            {
                await _messageHandler(data, address, port);
            }
        }
    }

    /// <summary>
    /// Mock async network sender for testing async disposal patterns.
    /// </summary>
    public class MockAsyncNetworkSender : INetworkSender, IAsyncDisposable, IDisposable
    {
        public List<(byte[] data, string address, int port)> SentMessages { get; } = new();
        public bool WasAsyncDisposed { get; private set; }
        public bool WasSyncDisposed { get; private set; }

        public Task SendAsync(byte[] data, string address, int port, CancellationToken cancellationToken = default)
        {
            if (WasAsyncDisposed || WasSyncDisposed)
                throw new ObjectDisposedException(nameof(MockAsyncNetworkSender));

            SentMessages.Add((data, address, port));
            return Task.CompletedTask;
        }

        public async ValueTask DisposeAsync()
        {
            await Task.Delay(10); // Simulate async cleanup
            WasAsyncDisposed = true;
        }

        public void Dispose()
        {
            WasSyncDisposed = true;
        }
    }

    /// <summary>
    /// Mock async network receiver for testing async disposal patterns.
    /// </summary>
    public class MockAsyncNetworkReceiver : INetworkReceiver, IAsyncDisposable, IDisposable
    {
        public bool WasAsyncDisposed { get; private set; }
        public bool WasSyncDisposed { get; private set; }
        private Func<byte[], string, int, Task>? _messageHandler;

        public Task StartReceivingAsync(Func<byte[], string, int, Task> onMessageReceived, CancellationToken cancellationToken = default)
        {
            if (WasAsyncDisposed || WasSyncDisposed)
                throw new ObjectDisposedException(nameof(MockAsyncNetworkReceiver));

            _messageHandler = onMessageReceived;
            return Task.CompletedTask;
        }

        public async ValueTask DisposeAsync()
        {
            await Task.Delay(10); // Simulate async cleanup
            WasAsyncDisposed = true;
        }

        public void Dispose()
        {
            WasSyncDisposed = true;
        }

        // Helper method to simulate receiving a message
        public async Task SimulateMessageReceived(byte[] data, string address, int port)
        {
            if (_messageHandler != null && !WasAsyncDisposed && !WasSyncDisposed)
            {
                await _messageHandler(data, address, port);
            }
        }
    }

    /// <summary>
    /// Faulty network sender that throws exceptions during disposal for testing exception handling.
    /// </summary>
    public class FaultyNetworkSender : INetworkSender, IAsyncDisposable, IDisposable
    {
        public Task SendAsync(byte[] data, string address, int port, CancellationToken cancellationToken = default)
        {
            // Always throw an exception to simulate network failure
            throw new InvalidOperationException("Simulated network failure in sender");
        }

        public async ValueTask DisposeAsync()
        {
            await Task.Delay(10);
            throw new InvalidOperationException("Simulated async disposal failure in sender");
        }

        public void Dispose()
        {
            throw new InvalidOperationException("Simulated sync disposal failure in sender");
        }
    }

    /// <summary>
    /// Faulty network receiver that throws exceptions during disposal for testing exception handling.
    /// </summary>
    public class FaultyNetworkReceiver : INetworkReceiver, IAsyncDisposable, IDisposable
    {
        public Task StartReceivingAsync(Func<byte[], string, int, Task> onMessageReceived, CancellationToken cancellationToken = default)
        {
            return Task.CompletedTask;
        }

        public async ValueTask DisposeAsync()
        {
            await Task.Delay(10);
            throw new InvalidOperationException("Simulated async disposal failure in receiver");
        }

        public void Dispose()
        {
            throw new InvalidOperationException("Simulated sync disposal failure in receiver");
        }
    }
}
