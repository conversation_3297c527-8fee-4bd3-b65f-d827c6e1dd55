using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace USDP2
{
    /// <summary>
    /// Monitors and reports on serialization performance.
    /// </summary>
    public static class SerializationPerformanceMonitor
    {
        // Cached JsonSerializerOptions for better performance
        private static readonly Lazy<JsonSerializerOptions> _lazyReportSerializerOptions = new Lazy<JsonSerializerOptions>(() => new JsonSerializerOptions
        {
            WriteIndented = true
        });

        private static readonly ConcurrentDictionary<string, List<long>> _serializationTimes = new();
        private static readonly ConcurrentDictionary<string, List<long>> _deserializationTimes = new();
        private static readonly ConcurrentDictionary<string, long> _serializationCounts = new();
        private static readonly ConcurrentDictionary<string, long> _deserializationCounts = new();
        private static readonly ConcurrentDictionary<string, long> _serializationSizes = new();
        private static readonly ConcurrentDictionary<string, long> _deserializationSizes = new();
        private static readonly ConcurrentDictionary<string, long> _serializationErrors = new();
        private static readonly ConcurrentDictionary<string, long> _deserializationErrors = new();

        /// <summary>
        /// Gets or sets whether performance monitoring is enabled.
        /// </summary>
        public static bool IsEnabled { get; set; }

        /// <summary>
        /// Gets or sets the sampling rate for performance monitoring (1.0 = monitor all operations, 0.1 = monitor 10% of operations).
        /// Values must be between 0.0 and 1.0.
        /// </summary>
        public static double SamplingRate { get; set; } = 1.0;

        /// <summary>
        /// Gets or sets the maximum number of time measurements to keep per type.
        /// </summary>
        public static int MaxMeasurementsPerType { get; set; } = 100;

        /// <summary>
        /// Records a serialization operation.
        /// </summary>
        /// <param name="typeName">The name of the type being serialized.</param>
        /// <param name="elapsedMilliseconds">The time taken in milliseconds.</param>
        /// <param name="sizeInBytes">The size of the serialized data in bytes.</param>
        /// <param name="isError">Whether the operation resulted in an error.</param>
        public static void RecordSerialization(string typeName, long elapsedMilliseconds, long sizeInBytes, bool isError = false)
        {
            if (!IsEnabled) return;

            _serializationCounts.AddOrUpdate(typeName, 1, (_, count) => count + 1);
            _serializationSizes.AddOrUpdate(typeName, sizeInBytes, (_, size) => size + sizeInBytes);

            if (isError)
            {
                _serializationErrors.AddOrUpdate(typeName, 1, (_, count) => count + 1);
            }
            else
            {
                _serializationTimes.AddOrUpdate(
                    typeName,
                    new List<long> { elapsedMilliseconds },
                    (_, times) =>
                    {
                        times.Add(elapsedMilliseconds);
                        // Keep only the last 100 measurements to avoid memory growth
                        if (times.Count > 100)
                        {
                            times.RemoveAt(0);
                        }
                        return times;
                    });
            }
        }

        /// <summary>
        /// Records a deserialization operation.
        /// </summary>
        /// <param name="typeName">The name of the type being deserialized.</param>
        /// <param name="elapsedMilliseconds">The time taken in milliseconds.</param>
        /// <param name="sizeInBytes">The size of the serialized data in bytes.</param>
        /// <param name="isError">Whether the operation resulted in an error.</param>
        public static void RecordDeserialization(string typeName, long elapsedMilliseconds, long sizeInBytes, bool isError = false)
        {
            if (!IsEnabled) return;

            _deserializationCounts.AddOrUpdate(typeName, 1, (_, count) => count + 1);
            _deserializationSizes.AddOrUpdate(typeName, sizeInBytes, (_, size) => size + sizeInBytes);

            if (isError)
            {
                _deserializationErrors.AddOrUpdate(typeName, 1, (_, count) => count + 1);
            }
            else
            {
                _deserializationTimes.AddOrUpdate(
                    typeName,
                    new List<long> { elapsedMilliseconds },
                    (_, times) =>
                    {
                        times.Add(elapsedMilliseconds);
                        // Keep only the last 100 measurements to avoid memory growth
                        if (times.Count > 100)
                        {
                            times.RemoveAt(0);
                        }
                        return times;
                    });
            }
        }

        /// <summary>
        /// Gets a performance report for all serialization operations.
        /// </summary>
        /// <returns>A JSON string containing the performance report.</returns>
        public static string GetPerformanceReport()
        {
            if (!IsEnabled)
            {
                return "Performance monitoring is disabled.";
            }

            var report = new
            {
                Serialization = GetTypeMetrics(_serializationTimes, _serializationCounts, _serializationSizes, _serializationErrors),
                Deserialization = GetTypeMetrics(_deserializationTimes, _deserializationCounts, _deserializationSizes, _deserializationErrors),
                TotalSerializations = _serializationCounts.Values.Sum(),
                TotalDeserializations = _deserializationCounts.Values.Sum(),
                TotalSerializationErrors = _serializationErrors.Values.Sum(),
                TotalDeserializationErrors = _deserializationErrors.Values.Sum(),
                AverageSerializationTime = GetAverageTime(_serializationTimes),
                AverageDeserializationTime = GetAverageTime(_deserializationTimes),
                Timestamp = DateTimeOffset.UtcNow
            };

            return JsonSerializer.Serialize(report, _lazyReportSerializerOptions.Value);
        }

        /// <summary>
        /// Gets a performance report for all serialization operations asynchronously.
        /// </summary>
        /// <returns>A task that represents the asynchronous operation. The value of the TResult parameter contains a JSON string with the performance report.</returns>
        public static async Task<string> GetPerformanceReportAsync()
        {
            if (!IsEnabled)
            {
                return "Performance monitoring is disabled.";
            }

            var report = new
            {
                Serialization = GetTypeMetrics(_serializationTimes, _serializationCounts, _serializationSizes, _serializationErrors),
                Deserialization = GetTypeMetrics(_deserializationTimes, _deserializationCounts, _deserializationSizes, _deserializationErrors),
                TotalSerializations = _serializationCounts.Values.Sum(),
                TotalDeserializations = _deserializationCounts.Values.Sum(),
                TotalSerializationErrors = _serializationErrors.Values.Sum(),
                TotalDeserializationErrors = _deserializationErrors.Values.Sum(),
                AverageSerializationTime = GetAverageTime(_serializationTimes),
                AverageDeserializationTime = GetAverageTime(_deserializationTimes),
                Timestamp = DateTimeOffset.UtcNow
            };

            var memoryStream = new MemoryStream();
            await JsonSerializer.SerializeAsync(
                memoryStream,
                report,
                _lazyReportSerializerOptions.Value);

            memoryStream.Position = 0;
            using var reader = new StreamReader(memoryStream);
            return await reader.ReadToEndAsync();
        }

        /// <summary>
        /// Clears all performance metrics.
        /// </summary>
        public static void ClearMetrics()
        {
            _serializationTimes.Clear();
            _deserializationTimes.Clear();
            _serializationCounts.Clear();
            _deserializationCounts.Clear();
            _serializationSizes.Clear();
            _deserializationSizes.Clear();
            _serializationErrors.Clear();
            _deserializationErrors.Clear();
        }

        /// <summary>
        /// Trims memory usage by removing oldest entries when collections exceed the specified size.
        /// </summary>
        /// <param name="maxEntriesPerType">Maximum number of entries to keep per type. Default is 1000.</param>
        /// <param name="maxTypes">Maximum number of types to track. Default is 100.</param>
        public static void OptimizeMemoryUsage(int maxEntriesPerType = 1000, int maxTypes = 100)
        {
            if (!IsEnabled) return;

            // Trim time measurements for each type
            foreach (var key in _serializationTimes.Keys)
            {
                if (_serializationTimes.TryGetValue(key, out var times) && times.Count > maxEntriesPerType)
                {
                    lock (times)
                    {
                        while (times.Count > maxEntriesPerType)
                        {
                            times.RemoveAt(0); // Remove oldest entries
                        }
                    }
                }
            }

            foreach (var key in _deserializationTimes.Keys)
            {
                if (_deserializationTimes.TryGetValue(key, out var times) && times.Count > maxEntriesPerType)
                {
                    lock (times)
                    {
                        while (times.Count > maxEntriesPerType)
                        {
                            times.RemoveAt(0); // Remove oldest entries
                        }
                    }
                }
            }

            // Limit number of types tracked
            if (_serializationTimes.Count > maxTypes)
            {
                var leastUsedTypes = _serializationCounts
                    .OrderBy(kvp => kvp.Value)
                    .Take(_serializationCounts.Count - maxTypes)
                    .Select(kvp => kvp.Key)
                    .ToList();

                foreach (var type in leastUsedTypes)
                {
                    _serializationTimes.TryRemove(type, out _);
                    _serializationCounts.TryRemove(type, out _);
                    _serializationSizes.TryRemove(type, out _);
                    _serializationErrors.TryRemove(type, out _);
                }
            }

            if (_deserializationTimes.Count > maxTypes)
            {
                var leastUsedTypes = _deserializationCounts
                    .OrderBy(kvp => kvp.Value)
                    .Take(_deserializationCounts.Count - maxTypes)
                    .Select(kvp => kvp.Key)
                    .ToList();

                foreach (var type in leastUsedTypes)
                {
                    _deserializationTimes.TryRemove(type, out _);
                    _deserializationCounts.TryRemove(type, out _);
                    _deserializationSizes.TryRemove(type, out _);
                    _deserializationErrors.TryRemove(type, out _);
                }
            }
        }

        /// <summary>
        /// Creates a performance measurement wrapper for a serialization function.
        /// </summary>
        /// <typeparam name="T">The type being serialized.</typeparam>
        /// <typeparam name="TResult">The result type of the serialization.</typeparam>
        /// <param name="serializeFunc">The serialization function to measure.</param>
        /// <param name="getSizeFunc">A function to get the size of the serialized data.</param>
        /// <returns>A wrapped function that measures performance.</returns>
        public static Func<T, TResult> MeasureSerialization<T, TResult>(
            Func<T, TResult> serializeFunc,
            Func<TResult, long> getSizeFunc)
            where T : class
        {
            return obj =>
            {
                if (!IsEnabled || (SamplingRate < 1.0 && Random.Shared.NextDouble() > SamplingRate))
                {
                    return serializeFunc(obj);
                }

                string typeName = typeof(T).Name;
                var stopwatch = Stopwatch.StartNew();

                try
                {
                    var result = serializeFunc(obj);
                    stopwatch.Stop();

                    long size = getSizeFunc(result);
                    RecordSerialization(typeName, stopwatch.ElapsedMilliseconds, size);

                    return result;
                }
                catch
                {
                    stopwatch.Stop();
                    RecordSerialization(typeName, stopwatch.ElapsedMilliseconds, 0, true);
                    throw;
                }
            };
        }

        /// <summary>
        /// Creates a performance measurement wrapper for a deserialization function.
        /// </summary>
        /// <typeparam name="TInput">The input type for deserialization.</typeparam>
        /// <typeparam name="TResult">The result type of the deserialization.</typeparam>
        /// <param name="deserializeFunc">The deserialization function to measure.</param>
        /// <param name="getSizeFunc">A function to get the size of the input data.</param>
        /// <returns>A wrapped function that measures performance.</returns>
        public static Func<TInput, TResult> MeasureDeserialization<TInput, TResult>(
            Func<TInput, TResult> deserializeFunc,
            Func<TInput, long> getSizeFunc)
            where TResult : class
        {
            return input =>
            {
                if (!IsEnabled || (SamplingRate < 1.0 && Random.Shared.NextDouble() > SamplingRate))
                {
                    return deserializeFunc(input);
                }

                string typeName = typeof(TResult).Name;
                var stopwatch = Stopwatch.StartNew();

                try
                {
                    var result = deserializeFunc(input);
                    stopwatch.Stop();

                    long size = getSizeFunc(input);
                    RecordDeserialization(typeName, stopwatch.ElapsedMilliseconds, size);

                    return result;
                }
                catch
                {
                    stopwatch.Stop();
                    RecordDeserialization(typeName, stopwatch.ElapsedMilliseconds, 0, true);
                    throw;
                }
            };
        }

        private static Dictionary<string, object> GetTypeMetrics(
            ConcurrentDictionary<string, List<long>> times,
            ConcurrentDictionary<string, long> counts,
            ConcurrentDictionary<string, long> sizes,
            ConcurrentDictionary<string, long> errors)
        {
            var metrics = new Dictionary<string, object>();

            foreach (var type in counts.Keys)
            {
                counts.TryGetValue(type, out long count);
                sizes.TryGetValue(type, out long totalSize);
                errors.TryGetValue(type, out long errorCount);
                times.TryGetValue(type, out var timeMeasurements);

                double avgTime = timeMeasurements?.Count > 0
                    ? timeMeasurements.Average()
                    : 0;

                double avgSize = count > 0
                    ? (double)totalSize / count
                    : 0;

                metrics[type] = new
                {
                    Count = count,
                    ErrorCount = errorCount,
                    ErrorRate = count > 0 ? (double)errorCount / count : 0,
                    AverageTimeMs = Math.Round(avgTime, 2),
                    AverageSizeBytes = Math.Round(avgSize, 2),
                    TotalSizeBytes = totalSize
                };
            }

            return metrics;
        }

        private static double GetAverageTime(ConcurrentDictionary<string, List<long>> times)
        {
            var allTimes = times.Values.SelectMany(list => list).ToList();
            return allTimes.Count > 0 ? allTimes.Average() : 0;
        }
    }
}