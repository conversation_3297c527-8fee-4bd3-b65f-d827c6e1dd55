using System.Net;
using System.Net.Sockets;
using System.Threading.Tasks;
using STUN;
using STUN.Client;


namespace USDP2
{
    /// <summary>
    /// The nat traversal helper.
    /// </summary>
    public static class NatTraversalHelper
    {
        /// <summary>
        /// Get public end point asynchronously.
        /// </summary>
        /// <param name="_stunServer">The stun server.</param>
        /// <param name="_stunPort">The stun port.</param>
        /// <returns>A <see cref="Task"/> of type <see cref="IPEndPoint"/></returns>
        public static async Task<IPEndPoint?> GetPublicEndPointAsync(string _stunServer = "stun.l.google.com", int _stunPort = 19302)
        {
            try
            {
                using var udp = new UdpClient(0);
                var addresses = await Dns.GetHostAddressesAsync(_stunServer);
                var serverEP = new IPEndPoint(addresses[0], _stunPort);

                // Updated to pass the required parameters to the constructor
                var localEP = (IPEndPoint)udp.Client.LocalEndPoint!;
                var stunClient = new StunClient5389UDP(serverEP, localEP, null);

                var result = await stunClient.BindingTestAsync();
                if (result?.PublicEndPoint is null) Diagnostics.Log("STUN_NoPublicEndPoint", new { StunServer = _stunServer, StunPort = _stunPort });
                else Diagnostics.Log("STUN_PublicEndPointFound", new { PublicEndpoint = result.PublicEndPoint.ToString(), StunServer = _stunServer, StunPort = _stunPort });
                return result?.PublicEndPoint;
            }
            catch (Exception ex)
            {
                Diagnostics.Log("STUN_Error", new { Error = ex.Message, StunServer = _stunServer, StunPort = _stunPort });
                return null;
            }

        }
    }
    //Example usage:
    /*
       public static async Task Main(string[] args)
       {
           var publicEndpoint = await NatTraversalHelper.GetPublicEndPointAsync("stun.l.google.com", 19302);
           if (publicEndpoint != null)
           {
               Console.WriteLine($"Public endpoint: {publicEndpoint.Address}:{publicEndpoint.Port}");
           }
       }
    */
}