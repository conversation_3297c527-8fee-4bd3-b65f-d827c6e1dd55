using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Text.RegularExpressions;

namespace USDP2
{
    /// <summary>
    /// Represents the severity level of a configuration validation issue.
    /// </summary>
    public enum ValidationSeverity
    {
        /// <summary>Information - Configuration is valid but may have recommendations.</summary>
        Info,
        /// <summary>Warning - Configuration may cause issues but won't prevent startup.</summary>
        Warning,
        /// <summary>Error - Configuration is invalid and will prevent proper operation.</summary>
        Error,
        /// <summary>Critical - Configuration is severely invalid and will cause immediate failure.</summary>
        Critical
    }

    /// <summary>
    /// Represents a configuration validation result.
    /// </summary>
    public class ValidationResult
    {
        public ValidationSeverity Severity { get; set; }
        public string PropertyName { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public object? CurrentValue { get; set; }
        public string? RecommendedAction { get; set; }

        public bool IsValid => Severity == ValidationSeverity.Info;
        public bool IsError => Severity == ValidationSeverity.Error || Severity == ValidationSeverity.Critical;
    }

    /// <summary>
    /// Comprehensive configuration validation for USDP2 startup.
    /// 
    /// This class provides thorough validation of all UsdpConfiguration properties to:
    /// - Prevent runtime failures due to invalid configuration
    /// - Provide clear error messages for misconfigurations
    /// - Improve system reliability and startup diagnostics
    /// - Offer recommendations for optimal configuration
    /// 
    /// Validation covers:
    /// - Network addresses and port ranges
    /// - Timeout values and ranges
    /// - File paths and permissions
    /// - Security settings and protocols
    /// - Buffer sizes and limits
    /// - Cross-property dependencies and conflicts
    /// </summary>
    public static partial class ValidateConfiguration
    {
        /// <summary>
        /// Validates the entire USDP configuration and returns all validation results.
        /// </summary>
        /// <param name="config">The configuration to validate. If null, uses UsdpConfiguration.Instance.</param>
        /// <returns>List of validation results, ordered by severity (Critical first).</returns>
        public static List<ValidationResult> ValidateAll(UsdpConfiguration? config = null)
        {
            config ??= UsdpConfiguration.Instance;
            var results = new List<ValidationResult>();

            // Validate all configuration categories
            results.AddRange(ValidateNetworkConfiguration(config));
            results.AddRange(ValidateTimeoutConfiguration(config));
            results.AddRange(ValidateSecurityConfiguration(config));
            results.AddRange(ValidateFilePathConfiguration(config));
            results.AddRange(ValidateBufferConfiguration(config));
            results.AddRange(ValidateServiceConfiguration(config));
            results.AddRange(ValidateLoggingConfiguration(config));
            results.AddRange(ValidateCircuitBreakerConfiguration(config));
            results.AddRange(ValidateCrossDependencies(config));

            // Sort by severity (Critical first, then Error, Warning, Info)
            return results.OrderBy(r => r.Severity).ToList();
        }

        /// <summary>
        /// Validates configuration and throws an exception if critical errors are found.
        /// </summary>
        /// <param name="config">The configuration to validate.</param>
        /// <exception cref="InvalidOperationException">Thrown when critical configuration errors are found.</exception>
        public static void ValidateAndThrow(UsdpConfiguration? config = null)
        {
            var results = ValidateAll(config);
            var criticalErrors = results.Where(r => r.Severity == ValidationSeverity.Critical).ToList();
            var errors = results.Where(r => r.Severity == ValidationSeverity.Error).ToList();

            if (criticalErrors.Count > 0 || errors.Count > 0)
            {
                var errorMessages = criticalErrors.Concat(errors)
                    .Select(r => $"{r.Severity}: {r.PropertyName} - {r.Message}")
                    .ToList();

                throw new InvalidOperationException(
                    $"Configuration validation failed with {errorMessages.Count} error(s):\n" +
                    string.Join("\n", errorMessages));
            }
        }

        /// <summary>
        /// Logs all validation results using the USDP logger.
        /// </summary>
        /// <param name="results">Validation results to log.</param>
        public static void LogValidationResults(List<ValidationResult> results)
        {
            var criticalCount = results.Count(r => r.Severity == ValidationSeverity.Critical);
            var errorCount = results.Count(r => r.Severity == ValidationSeverity.Error);
            var warningCount = results.Count(r => r.Severity == ValidationSeverity.Warning);
            var infoCount = results.Count(r => r.Severity == ValidationSeverity.Info);

            UsdpLogger.Log("Configuration.ValidationSummary", new
            {
                TotalIssues = results.Count,
                Critical = criticalCount,
                Errors = errorCount,
                Warnings = warningCount,
                Info = infoCount
            });

            foreach (var result in results)
            {
                UsdpLogger.Log($"Configuration.Validation.{result.Severity}", new
                {
                    Property = result.PropertyName,
                    Message = result.Message,
                    CurrentValue = result.CurrentValue?.ToString(),
                    RecommendedAction = result.RecommendedAction
                });
            }
        }

        /// <summary>
        /// Validates network-related configuration properties.
        /// </summary>
        private static List<ValidationResult> ValidateNetworkConfiguration(UsdpConfiguration config)
        {
            var results = new List<ValidationResult>();

            // Validate multicast address
            results.Add(ValidateMulticastAddress(config.DefaultMulticastAddress));

            // Validate service address
            results.Add(ValidateIpAddress(config.DefaultServiceAddress, nameof(config.DefaultServiceAddress)));

            // Validate ports
            results.Add(ValidatePort(config.DefaultMulticastPort, nameof(config.DefaultMulticastPort)));
            results.Add(ValidatePort(config.DefaultHttpPort, nameof(config.DefaultHttpPort)));
            results.Add(ValidatePort(config.DefaultHttpsPort, nameof(config.DefaultHttpsPort)));
            results.Add(ValidatePort(config.DefaultServicePort, nameof(config.DefaultServicePort)));

            // Validate port conflicts
            results.AddRange(ValidatePortConflicts(config));

            // Validate endpoint paths
            results.Add(ValidateEndpointPath(config.HttpEndpointPath, nameof(config.HttpEndpointPath)));
            results.Add(ValidateEndpointPath(config.HttpsEndpointPath, nameof(config.HttpsEndpointPath)));

            // Validate response port offset
            results.Add(ValidateResponsePortOffset(config.ResponsePortOffset));

            return results.Where(r => r != null).ToList()!;
        }

        /// <summary>
        /// Validates timeout and timing-related configuration properties.
        /// </summary>
        private static List<ValidationResult> ValidateTimeoutConfiguration(UsdpConfiguration config)
        {
            var results = new List<ValidationResult>();

            // Validate timeout values
            results.Add(ValidateTimeout(config.NetworkTimeout, nameof(config.NetworkTimeout),
                TimeSpan.FromSeconds(1), TimeSpan.FromMinutes(10)));

            results.Add(ValidateTimeout(config.QueryResponseTimeout, nameof(config.QueryResponseTimeout),
                TimeSpan.FromMilliseconds(100), TimeSpan.FromMinutes(1)));

            results.Add(ValidateTimeout(config.MdnsOperationDelay, nameof(config.MdnsOperationDelay),
                TimeSpan.FromMilliseconds(1), TimeSpan.FromSeconds(10)));

            results.Add(ValidateTimeout(config.StartupDelay, nameof(config.StartupDelay),
                TimeSpan.Zero, TimeSpan.FromSeconds(30)));

            // Validate TTL values
            if (config.DefaultTtl.HasValue)
            {
                results.Add(ValidateTimeout(config.DefaultTtl.Value, nameof(config.DefaultTtl),
                    TimeSpan.FromSeconds(1), TimeSpan.FromDays(30)));
            }

            results.Add(ValidateTimeout(config.MaxTtl, nameof(config.MaxTtl),
                TimeSpan.FromSeconds(1), TimeSpan.FromDays(365)));

            // Validate TTL relationship
            if (config.DefaultTtl.HasValue && config.DefaultTtl.Value > config.MaxTtl)
            {
                results.Add(new ValidationResult
                {
                    Severity = ValidationSeverity.Error,
                    PropertyName = nameof(config.DefaultTtl),
                    Message = "DefaultTtl cannot be greater than MaxTtl",
                    CurrentValue = $"Default: {config.DefaultTtl}, Max: {config.MaxTtl}",
                    RecommendedAction = "Set DefaultTtl to a value less than or equal to MaxTtl"
                });
            }

            // Validate circuit breaker timeouts if enabled
            if (config.EnableCircuitBreakers)
            {
                results.Add(ValidateTimeout(config.DefaultCircuitBreakerOpenTimeout,
                    nameof(config.DefaultCircuitBreakerOpenTimeout),
                    TimeSpan.FromSeconds(1), TimeSpan.FromMinutes(30)));
            }

            return results.Where(r => r != null).ToList()!;
        }

        /// <summary>
        /// Validates security-related configuration properties.
        /// </summary>
        private static List<ValidationResult> ValidateSecurityConfiguration(UsdpConfiguration config)
        {
            var results = new List<ValidationResult>();

            // Validate security protocol
            results.Add(ValidateSecurityProtocol(config.DefaultSecurity));

            // Validate protocol
            results.Add(ValidateProtocol(config.DefaultProtocol));

            // Validate HTTPS configuration
            if (config.UseHttps && config.DefaultHttpsPort == config.DefaultHttpPort)
            {
                results.Add(new ValidationResult
                {
                    Severity = ValidationSeverity.Warning,
                    PropertyName = nameof(config.UseHttps),
                    Message = "HTTPS is enabled but HTTPS port is same as HTTP port",
                    CurrentValue = $"HTTP: {config.DefaultHttpPort}, HTTPS: {config.DefaultHttpsPort}",
                    RecommendedAction = "Use different ports for HTTP and HTTPS (e.g., 8080 and 8443)"
                });
            }

            // Validate TLS configuration consistency
            if (config.EnableTlsFallback && !config.AllowTlsDowngrade)
            {
                results.Add(new ValidationResult
                {
                    Severity = ValidationSeverity.Warning,
                    PropertyName = nameof(config.EnableTlsFallback),
                    Message = "TLS fallback is enabled but TLS downgrade is not allowed",
                    CurrentValue = $"Fallback: {config.EnableTlsFallback}, Downgrade: {config.AllowTlsDowngrade}",
                    RecommendedAction = "Consider enabling AllowTlsDowngrade for fallback to work effectively"
                });
            }

            // Validate low security TLS fallback setting
            if (config.EnableLowSecurityTlsFallback)
            {
                results.Add(new ValidationResult
                {
                    Severity = ValidationSeverity.Warning,
                    PropertyName = nameof(config.EnableLowSecurityTlsFallback),
                    Message = "⚠️ SECURITY WARNING: Low security TLS fallback is enabled - deprecated TLS versions (1.0/1.1) are allowed",
                    CurrentValue = config.EnableLowSecurityTlsFallback.ToString(),
                    RecommendedAction = "Disable this setting unless absolutely required for legacy system compatibility. Plan migration to TLS 1.2+ as soon as possible."
                });

                // Additional security warnings for specific scenarios
                if (config.RequireAuthentication)
                {
                    results.Add(new ValidationResult
                    {
                        Severity = ValidationSeverity.Warning,
                        PropertyName = nameof(config.EnableLowSecurityTlsFallback),
                        Message = "Authentication is required but low security TLS fallback is enabled - potential security risk",
                        CurrentValue = $"Auth: {config.RequireAuthentication}, LowSecTLS: {config.EnableLowSecurityTlsFallback}",
                        RecommendedAction = "Consider disabling low security TLS when authentication is required"
                    });
                }

                if (config.UseHttps)
                {
                    results.Add(new ValidationResult
                    {
                        Severity = ValidationSeverity.Warning,
                        PropertyName = nameof(config.EnableLowSecurityTlsFallback),
                        Message = "HTTPS is enabled but low security TLS fallback is allowed - may compromise HTTPS security",
                        CurrentValue = $"HTTPS: {config.UseHttps}, LowSecTLS: {config.EnableLowSecurityTlsFallback}",
                        RecommendedAction = "Consider using only modern TLS versions with HTTPS"
                    });
                }
            }
            else
            {
                results.Add(new ValidationResult
                {
                    Severity = ValidationSeverity.Info,
                    PropertyName = nameof(config.EnableLowSecurityTlsFallback),
                    Message = "Low security TLS fallback is disabled - only modern TLS versions (1.2+) will be used",
                    CurrentValue = config.EnableLowSecurityTlsFallback.ToString(),
                    RecommendedAction = "Keep disabled for optimal security unless legacy compatibility is required"
                });
            }

            return results.Where(r => r != null).ToList()!;
        }

        /// <summary>
        /// Validates file path and directory configuration properties.
        /// </summary>
        private static List<ValidationResult> ValidateFilePathConfiguration(UsdpConfiguration config)
        {
            var results = new List<ValidationResult>();

            // Validate config file name
            results.Add(ValidateFileName(config.DefaultConfigFileName, nameof(config.DefaultConfigFileName)));

            // Validate config path
            try
            {
                var configPath = config.DefaultConfigPath;
                var directory = Path.GetDirectoryName(configPath);

                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    results.Add(new ValidationResult
                    {
                        Severity = ValidationSeverity.Warning,
                        PropertyName = nameof(config.DefaultConfigPath),
                        Message = "Configuration directory does not exist",
                        CurrentValue = directory,
                        RecommendedAction = "Ensure the directory exists or will be created at runtime"
                    });
                }
            }
            catch (Exception ex)
            {
                results.Add(new ValidationResult
                {
                    Severity = ValidationSeverity.Error,
                    PropertyName = nameof(config.DefaultConfigPath),
                    Message = $"Invalid configuration path: {ex.Message}",
                    CurrentValue = config.DefaultConfigPath,
                    RecommendedAction = "Use a valid file path format"
                });
            }

            // Validate custom log file path if specified
            if (!string.IsNullOrEmpty(config.CustomLogFilePath))
            {
                results.Add(ValidateLogFilePath(config.CustomLogFilePath));
            }

            return results.Where(r => r != null).ToList()!;
        }

        /// <summary>
        /// Validates buffer size and limit configuration properties.
        /// </summary>
        private static List<ValidationResult> ValidateBufferConfiguration(UsdpConfiguration config)
        {
            var results = new List<ValidationResult>();

            // Validate buffer sizes
            results.Add(ValidateBufferSize(config.DefaultBufferSize, nameof(config.DefaultBufferSize),
                512, 64 * 1024)); // 512 bytes to 64KB

            results.Add(ValidateBufferSize(config.MaxBufferSize, nameof(config.MaxBufferSize),
                1024, 10 * 1024 * 1024)); // 1KB to 10MB

            // Validate buffer size relationship
            if (config.DefaultBufferSize > config.MaxBufferSize)
            {
                results.Add(new ValidationResult
                {
                    Severity = ValidationSeverity.Error,
                    PropertyName = nameof(config.DefaultBufferSize),
                    Message = "DefaultBufferSize cannot be greater than MaxBufferSize",
                    CurrentValue = $"Default: {config.DefaultBufferSize}, Max: {config.MaxBufferSize}",
                    RecommendedAction = "Set DefaultBufferSize to a value less than or equal to MaxBufferSize"
                });
            }

            // Validate circuit breaker thresholds if enabled
            if (config.EnableCircuitBreakers)
            {
                results.Add(ValidateThreshold(config.DefaultCircuitBreakerFailureThreshold,
                    nameof(config.DefaultCircuitBreakerFailureThreshold), 1, 100));

                results.Add(ValidateThreshold(config.DefaultCircuitBreakerSuccessThreshold,
                    nameof(config.DefaultCircuitBreakerSuccessThreshold), 1, 50));
            }

            return results.Where(r => r != null).ToList()!;
        }

        /// <summary>
        /// Validates service-related configuration properties.
        /// </summary>
        private static List<ValidationResult> ValidateServiceConfiguration(UsdpConfiguration config)
        {
            var results = new List<ValidationResult>();

            // Validate service identifiers using helper methods from ValidateConfigurationHelpers
            results.Add(ValidateServiceIdentifier(config.DefaultServiceType, nameof(config.DefaultServiceType)));
            results.Add(ValidateServiceIdentifier(config.DefaultServiceInstance, nameof(config.DefaultServiceInstance)));
            results.Add(ValidateServiceIdentifier(config.DefaultMetadataType, nameof(config.DefaultMetadataType)));
            results.Add(ValidateServiceIdentifier(config.DefaultMetadataLocation, nameof(config.DefaultMetadataLocation)));

            return results.Where(r => r != null).ToList()!;
        }

        /// <summary>
        /// Validates logging-related configuration properties.
        /// </summary>
        private static List<ValidationResult> ValidateLoggingConfiguration(UsdpConfiguration config)
        {
            var results = new List<ValidationResult>();

            // Validate event log name for Windows
            if (config.LogMode == UsdpConfiguration.LoggingMode.EventLog ||
                config.LogMode == UsdpConfiguration.LoggingMode.Both)
            {
                results.Add(ValidateEventLogName(config.EventLogName));
            }

            // Validate syslog configuration
            if (config.LogMode == UsdpConfiguration.LoggingMode.Syslog)
            {
                results.Add(new ValidationResult
                {
                    Severity = ValidationSeverity.Info,
                    PropertyName = nameof(config.LogMode),
                    Message = "Syslog mode requires proper syslog daemon configuration",
                    CurrentValue = config.LogMode.ToString(),
                    RecommendedAction = "Ensure syslog daemon is running and configured to accept local messages"
                });
            }

            return results.Where(r => r != null).ToList()!;
        }

        /// <summary>
        /// Validates circuit breaker configuration properties.
        /// </summary>
        private static List<ValidationResult> ValidateCircuitBreakerConfiguration(UsdpConfiguration config)
        {
            var results = new List<ValidationResult>();

            if (!config.EnableCircuitBreakers)
            {
                results.Add(new ValidationResult
                {
                    Severity = ValidationSeverity.Info,
                    PropertyName = nameof(config.EnableCircuitBreakers),
                    Message = "Circuit breakers are disabled - network operations will not have resilience protection",
                    CurrentValue = config.EnableCircuitBreakers.ToString(),
                    RecommendedAction = "Consider enabling circuit breakers for improved network resilience"
                });
                return results;
            }

            // Validate circuit breaker monitoring
            if (!config.EnableCircuitBreakerMonitoring)
            {
                results.Add(new ValidationResult
                {
                    Severity = ValidationSeverity.Warning,
                    PropertyName = nameof(config.EnableCircuitBreakerMonitoring),
                    Message = "Circuit breaker monitoring is disabled - limited visibility into circuit health",
                    CurrentValue = config.EnableCircuitBreakerMonitoring.ToString(),
                    RecommendedAction = "Enable monitoring for better observability of circuit breaker health"
                });
            }

            return results;
        }

        /// <summary>
        /// Validates cross-property dependencies and potential conflicts.
        /// </summary>
        private static List<ValidationResult> ValidateCrossDependencies(UsdpConfiguration config)
        {
            var results = new List<ValidationResult>();

            // Validate timeout relationships
            if (config.QueryResponseTimeout > config.NetworkTimeout)
            {
                results.Add(new ValidationResult
                {
                    Severity = ValidationSeverity.Warning,
                    PropertyName = nameof(config.QueryResponseTimeout),
                    Message = "QueryResponseTimeout is greater than NetworkTimeout",
                    CurrentValue = $"Query: {config.QueryResponseTimeout}, Network: {config.NetworkTimeout}",
                    RecommendedAction = "QueryResponseTimeout should typically be less than NetworkTimeout"
                });
            }

            // Validate authentication and security consistency
            if (config.RequireAuthentication && config.DefaultSecurity == "none")
            {
                results.Add(new ValidationResult
                {
                    Severity = ValidationSeverity.Error,
                    PropertyName = nameof(config.RequireAuthentication),
                    Message = "Authentication is required but security is set to 'none'",
                    CurrentValue = $"Auth: {config.RequireAuthentication}, Security: {config.DefaultSecurity}",
                    RecommendedAction = "Set DefaultSecurity to a secure protocol (e.g., 'psk-tls1.3') or disable authentication"
                });
            }

            // Validate HTTPS configuration
            if (config.UseHttps && config.DefaultSecurity == "none")
            {
                results.Add(new ValidationResult
                {
                    Severity = ValidationSeverity.Warning,
                    PropertyName = nameof(config.UseHttps),
                    Message = "HTTPS is enabled but security is set to 'none'",
                    CurrentValue = $"HTTPS: {config.UseHttps}, Security: {config.DefaultSecurity}",
                    RecommendedAction = "Consider using a secure protocol when HTTPS is enabled"
                });
            }

            return results;
        }

        /// <summary>
        /// Validates a multicast IP address.
        /// </summary>
        private static ValidationResult ValidateMulticastAddress(string address)
        {
            if (string.IsNullOrWhiteSpace(address))
            {
                return new ValidationResult
                {
                    Severity = ValidationSeverity.Critical,
                    PropertyName = nameof(UsdpConfiguration.DefaultMulticastAddress),
                    Message = "Multicast address cannot be null or empty",
                    CurrentValue = address,
                    RecommendedAction = "Set a valid IPv4 multicast address (********* to ***************)"
                };
            }

            if (!IPAddress.TryParse(address, out var ipAddress))
            {
                return new ValidationResult
                {
                    Severity = ValidationSeverity.Critical,
                    PropertyName = nameof(UsdpConfiguration.DefaultMulticastAddress),
                    Message = "Invalid IP address format",
                    CurrentValue = address,
                    RecommendedAction = "Use a valid IPv4 address format (e.g., ***************)"
                };
            }

            // Check if it's in the multicast range (********* to ***************)
            var bytes = ipAddress.GetAddressBytes();
            if (bytes.Length != 4 || bytes[0] < 224 || bytes[0] > 239)
            {
                return new ValidationResult
                {
                    Severity = ValidationSeverity.Error,
                    PropertyName = nameof(UsdpConfiguration.DefaultMulticastAddress),
                    Message = "Address is not in the IPv4 multicast range",
                    CurrentValue = address,
                    RecommendedAction = "Use an address in the range ********* to ***************"
                };
            }

            // Check for reserved ranges
            if (bytes[0] == 224 && bytes[1] == 0 && bytes[2] == 0)
            {
                return new ValidationResult
                {
                    Severity = ValidationSeverity.Warning,
                    PropertyName = nameof(UsdpConfiguration.DefaultMulticastAddress),
                    Message = "Address is in the reserved local network control block",
                    CurrentValue = address,
                    RecommendedAction = "Consider using an address in the 239.x.x.x range for organization-local scope"
                };
            }

            return new ValidationResult
            {
                Severity = ValidationSeverity.Info,
                PropertyName = nameof(UsdpConfiguration.DefaultMulticastAddress),
                Message = "Multicast address is valid",
                CurrentValue = address
            };
        }
    }
}
