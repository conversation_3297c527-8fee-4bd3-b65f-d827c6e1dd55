/*
AdvancedQuery.cs - A powerful text matching system for metadata filtering

This code creates a powerful text matching system that can compare values against different types of search patterns. Think of it like a smart search engine that can understand multiple ways of looking for information.

Purpose of the Code

The main purpose of this code is to provide flexible ways to match text values against search patterns. Instead of just doing exact matches (where "apple" only matches "apple"), this system supports several advanced matching techniques like wildcards, regular expressions, and numeric ranges. This is particularly useful when you want to search through metadata (which is like labels or tags attached to data) and find items that meet certain criteria.

What Inputs It Takes

The code has two main functions that take different inputs. The first function, called Match, takes two strings: a value(the text you want to check) and a pattern (the search criteria you want to match against). The second function, called MatchMetadata, takes two dictionaries: one containing metadata (key-value pairs of information) and another containing the query (key-value pairs of search criteria).

What Outputs It Produces

Both functions return a simple true or false answer. True means the value matches the pattern according to the rules, and false means it doesn't match. This makes it easy to use in decision-making code where you need to filter or find specific items.

How It Achieves Its Purpose

The code works by examining the search pattern and deciding which type of matching to perform. It supports four different matching strategies. First, if the pattern is surrounded by forward slashes (like "/pattern/"), it treats it as a regular expression, which is a powerful way to describe complex text patterns. Second, if the pattern contains asterisks (*), it treats them as wildcards where the asterisk can represent any sequence of characters. Third, if the pattern contains a dash and the value being checked is a number, it tries to do a range match (like "10-20" meaning any number between 10 and 20). Finally, if none of these special cases apply, it does a simple exact match but ignores whether letters are uppercase or lowercase.

Important Logic Flows and Data Transformations

The code follows a clear decision-making process. It first checks what type of pattern it's dealing with by looking for special characters or formatting. For wildcard patterns, it converts them into regular expressions by escaping special characters and replacing asterisks with ".*" (which means "any characters"). For range patterns, it splits the pattern at the dash, converts both parts to numbers, and checks if the value falls between them. The MatchMetadata function extends this logic by checking multiple criteria at once - it goes through each search criterion in the query and applies the same matching logic to the corresponding value in the metadata. If any criterion fails to match, the entire search fails. This creates an "AND" logic where all conditions must be met for a successful match.
*/
using USDP2;
using System;
using System.Collections.Generic;
using System.Text.RegularExpressions;

namespace USDP2
{
    /// <summary>
    /// Advanced query language for metadata filtering.
    /// Supports wildcards (*), regex (/pattern/), and numeric ranges (min-max).
    /// </summary>
    /// <remarks>
    /// This class provides methods to match string values against various patterns, including:
    /// - Regular Expressions (enclosed in '/')
    /// - Wildcards ('*' representing any character(s))
    /// - Numeric Ranges ("min-max")
    /// - Case-insensitive Exact String Matching
    /// </remarks>
    public static class AdvancedQuery
    {
        /// <summary>
        /// Matches a string value against a specified pattern. The pattern can be a regular expression (e.g., "/pattern/"), include wildcards (e.g., "file*.txt"), represent a numeric range (e.g., "10-20" for numeric values), or be an exact string (case-insensitive).
        /// </summary>
        /// <param name="value">The string value to test. Can be null.</param>
        /// <param name="pattern">The pattern to match against. Can be null.</param>
        /// <returns>
        /// True if the value matches the pattern; otherwise, false.
        /// Returns true if both <paramref name="value"/> and <paramref name="pattern"/> are null.
        /// Returns false if <paramref name="value"/> is null but <paramref name="pattern"/> is not (unless the pattern specifically matches null, e.g., some regex).
        /// Returns false if <paramref name="pattern"/> is null but <paramref name="value"/> is not.
        /// </returns>
        ///<remarks>This method uses the centralized Match method internally to handle individual comparisons.</remarks>
        public static bool Match(string? value, string? pattern)
        {
            try
           {
            // First check for null pattern and value. If both value and pattern are null, they match, so return true.
            if (pattern == null && value == null)
                return true;
            // If one is null and the other is not, they do not match, so return false.
            if (pattern == null || value == null)
                return false;
            // Check for regex match
            if (pattern.StartsWith('/') && pattern.EndsWith('/'))
            {
                /*************************************************
                 The pattern is expected to be a valid regex pattern. If the pattern starts and ends with '/', we treat it as a regex. We remove the leading and trailing slashes to get the actual regex pattern. The regex is created with the pattern inside the slashes.
                 Note: RegexOptions.IgnoreCase is used to make the  match case-insensitive.
                **********************************************/

                // Remove the leading and trailing slashes
                var regex = new Regex(pattern.Trim('/'), RegexOptions.IgnoreCase);
                // Use the regex to check if it matches the value
                return regex.IsMatch(value);
            }

            /*******************************************
            If the pattern contains a '*', we treat it as a wildcard.
            Convert '*' to '.*' in the regex pattern.
            The '^' and '$' ensure the whole string matches.
            *******************************************/

            // Check for wildcard match
            if (pattern.Contains('*'))
            {
                /***************************************************
                 Escape special regex characters in the pattern  except for '*'. Replace '*' with '.*' to match any  sequence of characters. The '^' at the start and  '$' at the end ensure that the entire string  matches the pattern.
                  Note: Regex.Escape escapes all special characters,  so we replace '*' with '.*' after escaping. This  allows the pattern to match any characters in place of '*'. The RegexOptions.IgnoreCase makes the match case-insensitive.
                The pattern is transformed to a regex pattern that matches the entire string.
                 Example: "file*.txt" becomes "^file.*\\.txt$" which matches any string starting with "file" and ending with ".txt".
                The regex ensures the whole string matches (^...$).
                 Example: "*.txt" becomes "^.*\\.txt$" which matches any string ending with ".txt".   
                The result will be true if the value matches the pattern with wildcards.
                 Note: Regex.Escape is used to escape all special characters in the pattern except for '*'.
                 This allows the pattern to match any characters in place of '*'.**************************************************/

                var regex = new Regex("^" + Regex.Escape(pattern).Replace("\\*", ".*") + "$", RegexOptions.IgnoreCase);
                // Use the regex to check if it matches the value
                return regex.IsMatch(value);
            }

            /***********************************************
             If the pattern contains a '-', we treat it as a numeric range. The pattern is expected to be in the format "min-max" where both min and max are numbers. If the pattern contains a '-', we check if the value is a number.
             ************************************************/

            // Check for numeric range match
            if (pattern.Contains('-') && double.TryParse(value, out var v))
            {
                // Range match (e.g., "10-20")
                var parts = pattern.Split('-');
                if (parts.Length == 2 && double.TryParse(parts[0], out var min) && double.TryParse(parts[1], out var max))
                    return v >= min && v <= max;
            }
            // Exact match
            return string.Equals(value, pattern, StringComparison.OrdinalIgnoreCase);
           }
           catch (System.Exception ex)
           {
               Diagnostics.Log("MatchException", new { Value = value, Pattern = pattern, Error = ex.Message });
               return false;
           }
        }
        ///<summary>
        /// Match metadata.
        /// Matches a dictionary of metadata against a dictionary of query criteria.
        /// All criteria specified in the <paramref name="query"/> dictionary must be met for a successful match (AND logic).
        /// </summary>
        /// <param name="metadata">The metadata dictionary to check. Can be null.</param>
        /// <param name="query">The query criteria dictionary. Each key-value pair represents a condition to be met. Can be null or empty.</param>
        /// <returns>A bool. True if all query criteria match the corresponding metadata entries, or if the query is null or empty; false otherwise. If <paramref name="metadata"/> is null and <paramref name="query"/> is not empty, returns false.</returns>
        public static bool MatchMetadata(IReadOnlyDictionary<string, string>? metadata, IReadOnlyDictionary<string, string>? query)
        {
            if (query == null || query.Count == 0) return true; // No query criteria means it's a match.
            if (metadata == null) return false; // Cannot match a non-empty query against null metadata.

            foreach (var kvp in query)
            {
                if (!metadata.TryGetValue(kvp.Key, out var value))
                    return false;

                var pattern = kvp.Value;

                // Use the centralized Match method for the actual comparison.
                if (!Match(value, pattern))
                    return false;
            }
            return true;
        }
    }
}