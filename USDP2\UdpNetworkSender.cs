using System;
using System.Net;
using System.Net.Sockets;
using System.Security.Cryptography;
using System.Threading;
using System.Threading.Tasks;

namespace USDP2
{
    /// <summary>
    /// Sends data over UDP with optional security enhancements.
    ///
    /// This class provides UDP communication with support for:
    /// - Standard UDP transmission (default)
    /// - Optional message authentication and integrity protection
    /// - Graceful fallback when security features are unavailable
    /// - Comprehensive security logging and monitoring
    /// </summary>
    public class UdpNetworkSender : INetworkSender, IDisposable
    {
        private UdpSecurityOverride? _udpSecurity;
        private readonly UsdpConfiguration _config;
        private bool _disposed;

        /// <summary>
        /// Initializes a new instance of the UdpNetworkSender class.
        /// </summary>
        /// <param name="config">The USDP configuration instance. If null, uses UsdpConfiguration.Instance.</param>
        /// <exception cref="ArgumentNullException">Thrown when config is explicitly passed as null.</exception>
        public UdpNetworkSender(UsdpConfiguration? config = null)
        {
            _config = config ?? UsdpConfiguration.Instance;

            // Note: UDP security initialization is deferred to first use to avoid blocking constructor
            // This prevents deadlock issues with async operations in constructors
            Diagnostics.Log("UdpNetworkSender", new
            {
                Message = _config.EnableUdpSecurity ?
                    "Initialized with deferred UDP security initialization" :
                    "Initialized with standard UDP transmission",
                SecurityEnabled = _config.EnableUdpSecurity,
                DeferredInitialization = _config.EnableUdpSecurity
            });
        }

        /// <summary>
        /// Ensures UDP security is initialized if enabled. Called before first use.
        /// </summary>
        /// <exception cref="ObjectDisposedException">Thrown when the instance has been disposed.</exception>
        private async Task EnsureSecurityInitializedAsync()
        {
            ObjectDisposedException.ThrowIf(_disposed, this);

            if (_config.EnableUdpSecurity && _udpSecurity == null)
            {
                try
                {
                    _udpSecurity = await UdpSecurityOverride.CreateAsync();

                    Diagnostics.Log("UdpNetworkSender", new
                    {
                        Message = "Successfully initialized UDP security override",
                        SecurityEnabled = true
                    });
                }
                catch (TypeLoadException ex)
                {
                    // Handle case where UdpSecurityOverride class is not available
                    // (e.g., if the class was removed for simplicity)
                    Diagnostics.Log("UdpNetworkSender", new
                    {
                        Message = "UDP security class not available, falling back to unsecured UDP",
                        SecurityEnabled = false,
                        Error = ex.Message,
                        FallbackToUnsecured = true,
                        ErrorType = "TypeLoadException"
                    });

                    // _udpSecurity remains null, system will use standard UDP
                }
                catch (CryptographicException ex)
                {
                    // Handle cryptographic initialization failures
                    Diagnostics.Log("UdpNetworkSenderWarning", new
                    {
                        Message = "UDP security cryptographic initialization failed, falling back to unsecured UDP",
                        SecurityEnabled = false,
                        Error = ex.Message,
                        FallbackToUnsecured = true,
                        ErrorType = "CryptographicException"
                    });

                    // _udpSecurity remains null, system will use standard UDP
                }
                catch (Exception ex)
                {
                    // Handle any other initialization failures
                    Diagnostics.Log("UdpNetworkSenderWarning", new
                    {
                        Message = "UDP security initialization failed, falling back to unsecured UDP",
                        SecurityEnabled = false,
                        Error = ex.Message,
                        FallbackToUnsecured = true,
                        ErrorType = ex.GetType().Name
                    });

                    // _udpSecurity remains null, system will use standard UDP
                }
            }
        }
        /// <summary>
        /// Sends data asynchronously via UDP to the specified address and port.
        ///
        /// This method automatically applies security enhancements if UDP security is enabled
        /// and available. If security fails, it falls back to unsecured transmission with
        /// appropriate logging.
        /// </summary>
        /// <param name="data">The data to send.</param>
        /// <param name="address">The target IP address.</param>
        /// <param name="port">The target port number.</param>
        /// <param name="cancellationToken">The cancellation token to cancel the operation.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous send operation.</returns>
        public async Task SendAsync(byte[] data, string address, int port, CancellationToken cancellationToken = default)
        {
            ObjectDisposedException.ThrowIf(_disposed, this);
            ArgumentNullException.ThrowIfNull(data);
            ArgumentException.ThrowIfNullOrEmpty(address);

            // Ensure security is initialized if enabled
            await EnsureSecurityInitializedAsync();

            byte[] dataToSend = data;
            bool securityApplied = false;

            // Apply security if enabled and available
            if (_config.EnableUdpSecurity && _udpSecurity != null)
            {
                try
                {
                    dataToSend = _udpSecurity.SecureData(data);
                    securityApplied = true;

                    Diagnostics.Log("UdpNetworkSender", new
                    {
                        Message = "Applied UDP security to outgoing data",
                        OriginalSize = data.Length,
                        SecuredSize = dataToSend.Length,
                        Address = address,
                        Port = port
                    });
                }
                catch (Exception ex)
                {
                    // Security failed, fall back to unsecured transmission
                    Diagnostics.Log("UdpNetworkSenderWarning", new
                    {
                        Message = "UDP security failed, falling back to unsecured transmission",
                        Address = address,
                        Port = port,
                        Error = ex.Message,
                        FallbackToUnsecured = true
                    });

                    dataToSend = data; // Use original data
                    securityApplied = false;
                }
            }

            try
            {
                using var udpClient = new UdpClient();
                var ip = IPAddress.Parse(address);
                var endpoint = new IPEndPoint(ip, port);
                await udpClient.SendAsync(dataToSend, dataToSend.Length, endpoint).WaitAsync(cancellationToken);

                Diagnostics.Log("UdpNetworkSender", new
                {
                    Message = "Successfully sent UDP data",
                    Address = address,
                    Port = port,
                    DataSize = dataToSend.Length,
                    SecurityApplied = securityApplied
                });
            }
            catch (FormatException ex)
            {
                Diagnostics.Log("UdpNetworkSenderError", new
                {
                    Message = "Invalid IP address format",
                    Address = address,
                    Port = port,
                    Exception = ex.Message
                });
                throw;
            }
            catch (SocketException ex)
            {
                Diagnostics.Log("UdpNetworkSenderError", new
                {
                    Message = "Socket error occurred",
                    Address = address,
                    Port = port,
                    Exception = ex.Message
                });
                throw;
            }
            catch (Exception ex)
            {
                Diagnostics.Log("UdpNetworkSenderError", new
                {
                    Message = "Unexpected error occurred",
                    Address = address,
                    Port = port,
                    Exception = ex.Message
                });
                throw;
            }
        }

        /// <summary>
        /// Disposes of managed resources.
        /// </summary>
        public void Dispose()
        {
            if (!_disposed)
            {
                // Note: UdpSecurityOverride doesn't implement IDisposable, so no disposal needed
                // UdpClient instances are created with 'using' statements in SendAsync, so they're automatically disposed

                _disposed = true;
                GC.SuppressFinalize(this);

                Diagnostics.Log("UdpNetworkSender", new
                {
                    Message = "UdpNetworkSender disposed",
                    Disposed = true
                });
            }
        }
    }
}