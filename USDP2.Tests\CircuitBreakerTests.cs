using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace USDP2.Tests
{
    /// <summary>
    /// Tests for the circuit breaker pattern implementation.
    /// 
    /// These tests verify that the circuit breaker correctly handles:
    /// - State transitions (Closed -> Open -> Half-Open -> Closed)
    /// - Failure counting and thresholds
    /// - Timeout handling and recovery
    /// - Exception handling and classification
    /// - Statistics collection and monitoring
    /// </summary>
    [TestClass]
    public class CircuitBreakerTests
    {
        [TestMethod]
        public async Task CircuitBreaker_SuccessfulOperations_KeepsCircuitClosed()
        {
            // Arrange
            var options = new CircuitBreakerOptions
            {
                FailureThreshold = 3,
                OpenTimeout = TimeSpan.FromSeconds(1)
            };
            var circuitBreaker = new CircuitBreaker("TestSuccess", options);

            // Act - Execute successful operations
            for (int i = 0; i < 10; i++)
            {
                var result = await circuitBreaker.ExecuteAsync(async ct =>
                {
                    await Task.Delay(10, ct);
                    return $"Success {i}";
                });

                Assert.AreEqual($"Success {i}", result);
            }

            // Assert
            Assert.AreEqual(CircuitBreakerState.Closed, circuitBreaker.State);
            var stats = circuitBreaker.GetStatistics();
            Assert.AreEqual(10, stats.TotalSuccesses);
            Assert.AreEqual(0, stats.TotalFailures);
        }

        [TestMethod]
        public async Task CircuitBreaker_RepeatedFailures_OpensCircuit()
        {
            // Arrange
            var options = new CircuitBreakerOptions
            {
                FailureThreshold = 3,
                OpenTimeout = TimeSpan.FromSeconds(1)
            };
            var circuitBreaker = new CircuitBreaker("TestFailure", options);

            // Act - Execute failing operations
            for (int i = 0; i < 3; i++)
            {
                await Assert.ThrowsExceptionAsync<InvalidOperationException>(async () =>
                {
                    await circuitBreaker.ExecuteAsync<string>(async ct =>
                    {
                        await Task.Delay(10, ct);
                        throw new InvalidOperationException($"Failure {i}");
                    });
                });
            }

            // Assert
            Assert.AreEqual(CircuitBreakerState.Open, circuitBreaker.State);
            var stats = circuitBreaker.GetStatistics();
            Assert.AreEqual(0, stats.TotalSuccesses);
            Assert.AreEqual(3, stats.TotalFailures);
        }

        [TestMethod]
        public async Task CircuitBreaker_OpenCircuit_BlocksOperations()
        {
            // Arrange
            var options = new CircuitBreakerOptions
            {
                FailureThreshold = 2,
                OpenTimeout = TimeSpan.FromSeconds(10) // Long timeout to keep circuit open
            };
            var circuitBreaker = new CircuitBreaker("TestBlocking", options);

            // Act - Cause circuit to open
            for (int i = 0; i < 2; i++)
            {
                await Assert.ThrowsExceptionAsync<InvalidOperationException>(async () =>
                {
                    await circuitBreaker.ExecuteAsync<string>(async ct =>
                    {
                        throw new InvalidOperationException($"Failure {i}");
                    });
                });
            }

            // Assert - Circuit should be open and block operations
            Assert.AreEqual(CircuitBreakerState.Open, circuitBreaker.State);

            await Assert.ThrowsExceptionAsync<CircuitBreakerOpenException>(async () =>
            {
                await circuitBreaker.ExecuteAsync<string>(async ct =>
                {
                    return "This should not execute";
                });
            });
        }

        [TestMethod]
        public async Task CircuitBreaker_HalfOpenRecovery_ClosesOnSuccess()
        {
            // Arrange
            var options = new CircuitBreakerOptions
            {
                FailureThreshold = 2,
                OpenTimeout = TimeSpan.FromMilliseconds(100), // Short timeout for quick transition
                SuccessThreshold = 2
            };
            var circuitBreaker = new CircuitBreaker("TestRecovery", options);

            // Act - Cause circuit to open
            for (int i = 0; i < 2; i++)
            {
                await Assert.ThrowsExceptionAsync<InvalidOperationException>(async () =>
                {
                    await circuitBreaker.ExecuteAsync<string>(async ct =>
                    {
                        throw new InvalidOperationException($"Failure {i}");
                    });
                });
            }

            Assert.AreEqual(CircuitBreakerState.Open, circuitBreaker.State);

            // Wait for circuit to transition to half-open
            await Task.Delay(200);

            // Execute successful operations to close circuit
            for (int i = 0; i < 2; i++)
            {
                var result = await circuitBreaker.ExecuteAsync(async ct =>
                {
                    await Task.Delay(10, ct);
                    return $"Recovery {i}";
                });

                Assert.AreEqual($"Recovery {i}", result);
            }

            // Assert
            Assert.AreEqual(CircuitBreakerState.Closed, circuitBreaker.State);
        }

        [TestMethod]
        public async Task CircuitBreaker_HalfOpenFailure_ReopensCircuit()
        {
            // Arrange
            var options = new CircuitBreakerOptions
            {
                FailureThreshold = 2,
                OpenTimeout = TimeSpan.FromMilliseconds(100),
                SuccessThreshold = 2
            };
            var circuitBreaker = new CircuitBreaker("TestReopen", options);

            // Act - Cause circuit to open
            for (int i = 0; i < 2; i++)
            {
                await Assert.ThrowsExceptionAsync<InvalidOperationException>(async () =>
                {
                    await circuitBreaker.ExecuteAsync<string>(async ct =>
                    {
                        throw new InvalidOperationException($"Failure {i}");
                    });
                });
            }

            // Wait for half-open transition
            await Task.Delay(200);

            // Fail in half-open state
            await Assert.ThrowsExceptionAsync<InvalidOperationException>(async () =>
            {
                await circuitBreaker.ExecuteAsync<string>(async ct =>
                {
                    throw new InvalidOperationException("Half-open failure");
                });
            });

            // Assert
            Assert.AreEqual(CircuitBreakerState.Open, circuitBreaker.State);
        }

        [TestMethod]
        public async Task CircuitBreaker_OperationTimeout_CountsAsFailure()
        {
            // Arrange
            var options = new CircuitBreakerOptions
            {
                FailureThreshold = 2,
                OperationTimeout = TimeSpan.FromMilliseconds(50), // Very short timeout
                ShouldHandleException = ex => true // Count all exceptions including timeouts as failures
            };
            var circuitBreaker = new CircuitBreaker("TestTimeout", options);

            // Act - Execute operations that timeout
            for (int i = 0; i < 2; i++)
            {
                await Assert.ThrowsExceptionAsync<TaskCanceledException>(async () =>
                {
                    await circuitBreaker.ExecuteAsync<string>(async ct =>
                    {
                        await Task.Delay(200, ct); // Longer than timeout
                        return "Should not complete";
                    });
                });
            }

            // Assert
            Assert.AreEqual(CircuitBreakerState.Open, circuitBreaker.State);
            var stats = circuitBreaker.GetStatistics();
            Assert.AreEqual(2, stats.TotalFailures);
        }

        [TestMethod]
        public async Task CircuitBreaker_CancellationToken_DoesNotCountAsFailure()
        {
            // Arrange
            var options = new CircuitBreakerOptions
            {
                FailureThreshold = 2,
                ShouldHandleException = ex => ex is not OperationCanceledException
            };
            var circuitBreaker = new CircuitBreaker("TestCancellation", options);

            // Act - Cancel operations
            for (int i = 0; i < 5; i++)
            {
                using var cts = new CancellationTokenSource();
                cts.Cancel();

                await Assert.ThrowsExceptionAsync<OperationCanceledException>(async () =>
                {
                    await circuitBreaker.ExecuteAsync<string>(async ct =>
                    {
                        ct.ThrowIfCancellationRequested();
                        return "Should not complete";
                    }, cts.Token);
                });
            }

            // Assert - Circuit should remain closed since cancellations don't count as failures
            Assert.AreEqual(CircuitBreakerState.Closed, circuitBreaker.State);
            var stats = circuitBreaker.GetStatistics();
            Assert.AreEqual(0, stats.TotalFailures);
        }

        [TestMethod]
        public void CircuitBreaker_Statistics_AreAccurate()
        {
            // Arrange
            var options = new CircuitBreakerOptions
            {
                FailureThreshold = 5
            };
            var circuitBreaker = new CircuitBreaker("TestStats", options);

            // Act & Assert - Check initial statistics
            var initialStats = circuitBreaker.GetStatistics();
            Assert.AreEqual(CircuitBreakerState.Closed, initialStats.State);
            Assert.AreEqual(0, initialStats.TotalOperations);
            Assert.AreEqual(0, initialStats.TotalFailures);
            Assert.AreEqual(0, initialStats.TotalSuccesses);
            Assert.AreEqual(0, initialStats.FailureRate);
        }

        [TestMethod]
        public async Task CircuitBreaker_VoidOperations_WorkCorrectly()
        {
            // Arrange
            var options = new CircuitBreakerOptions
            {
                FailureThreshold = 3
            };
            var circuitBreaker = new CircuitBreaker("TestVoid", options);
            int executionCount = 0;

            // Act - Execute void operations
            for (int i = 0; i < 5; i++)
            {
                await circuitBreaker.ExecuteAsync(async ct =>
                {
                    await Task.Delay(10, ct);
                    executionCount++;
                });
            }

            // Assert
            Assert.AreEqual(5, executionCount);
            Assert.AreEqual(CircuitBreakerState.Closed, circuitBreaker.State);
            var stats = circuitBreaker.GetStatistics();
            Assert.AreEqual(5, stats.TotalSuccesses);
        }
    }

    /// <summary>
    /// Tests for the network circuit breaker wrapper.
    /// </summary>
    [TestClass]
    public class NetworkCircuitBreakerWrapperTests
    {
        [TestMethod]
        public async Task NetworkWrapper_SuccessfulSend_PassesThrough()
        {
            // Arrange
            var mockSender = new Mocks.MockNetworkSender();
            var wrapper = new NetworkCircuitBreakerWrapper(mockSender);
            var data = new byte[] { 1, 2, 3, 4 };

            // Act
            await wrapper.SendAsync(data, "***********", 8080);

            // Assert
            Assert.AreEqual(1, mockSender.SentMessages.Count);
            Assert.AreEqual("***********", mockSender.SentMessages[0].address);
            Assert.AreEqual(8080, mockSender.SentMessages[0].port);
            CollectionAssert.AreEqual(data, mockSender.SentMessages[0].data);
        }

        [TestMethod]
        public async Task NetworkWrapper_RepeatedFailures_OpensCircuit()
        {
            // Arrange
            var faultySender = new Mocks.FaultyNetworkSender();
            var options = new CircuitBreakerOptions { FailureThreshold = 2 };
            var wrapper = new NetworkCircuitBreakerWrapper(faultySender, "TestFaulty", options);
            var data = new byte[] { 1, 2, 3 };

            // Act - Cause failures to open circuit
            for (int i = 0; i < 2; i++)
            {
                await Assert.ThrowsExceptionAsync<InvalidOperationException>(async () =>
                {
                    await wrapper.SendAsync(data, "***********", 8080);
                });
            }

            // Assert - Next operation should be blocked
            await Assert.ThrowsExceptionAsync<CircuitBreakerOpenException>(async () =>
            {
                await wrapper.SendAsync(data, "***********", 8080);
            });

            var stats = wrapper.GetCircuitBreakerStatistics();
            Assert.AreEqual(CircuitBreakerState.Open, stats.State);
        }

        [TestMethod]
        public void NetworkFactory_CreatesSendersWithCircuitBreakers()
        {
            // Act
            var httpSender = NetworkCircuitBreakerFactory.CreateHttpSender();
            var udpSender = NetworkCircuitBreakerFactory.CreateUdpSender();
            var tcpSender = NetworkCircuitBreakerFactory.CreateTcpSender();

            // Assert
            Assert.IsInstanceOfType(httpSender, typeof(NetworkCircuitBreakerWrapper));
            Assert.IsInstanceOfType(udpSender, typeof(NetworkCircuitBreakerWrapper));
            Assert.IsInstanceOfType(tcpSender, typeof(NetworkCircuitBreakerWrapper));
        }
    }
}
