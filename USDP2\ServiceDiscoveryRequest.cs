using System.Collections.Generic;

namespace USDP2
{
    /// <summary>
    /// Represents a service discovery request with type, location, and transport preferences.
    /// </summary>
    public sealed class ServiceDiscoveryRequest(string serviceType)
    {
        /// <summary>
        /// The type of service being requested (e.g., "printer", "sensor").
        /// </summary>
        public string ServiceType { get; set; } = serviceType;

        /// <summary>
        /// Gets or sets optional location constraints for filtering services by location.
        /// </summary>
        /// <value>A list of location constraints such as "BuildingA", "Floor2", or "Region:EU". Can be null if no location filtering is required.</value>
        public List<string>? LocationConstraints { get; set; }

        /// <summary>
        /// Gets or sets preferred transport protocols for the discovered services.
        /// </summary>
        /// <value>A list of preferred transport protocols such as "HTTP", "MQTT", or "HTTPS". Can be null if no transport preference is specified.</value>
        public List<string>? TransportPreferences { get; set; }

        /// <summary>
        /// Gets or sets optional metadata query parameters for advanced service filtering.
        /// </summary>
        /// <value>A dictionary of key-value pairs used for metadata-based service filtering. Can be null if no metadata filtering is required.</value>
        public Dictionary<string, string>? MetadataQuery { get; set; }
    }
}