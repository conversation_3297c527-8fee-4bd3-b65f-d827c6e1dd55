using System;
using System.Net.Http;
using System.Threading.Tasks;

namespace USDP2
{
    /// <summary>
    /// Secure Dynamic DNS (DDNS) client for updating DNS records via HTTP-based DDNS providers.
    /// </summary>
    public class DynamicDnsClient : IDisposable
    {
        /// <summary>
        /// The http client.
        /// </summary>
        private readonly HttpClient _httpClient;
        /// <summary>
        /// Initializes a new instance of the <see cref="DynamicDnsClient"/> class.
        /// </summary>
        /// <param name="domain">The domain.</param>
        /// <param name="token">The token.</param>
        public DynamicDnsClient(string domain, string token)
        {
            // Initialize the HttpClient
            _httpClient = new HttpClient();
            Diagnostics.Log("DnsClientInitialized", new { domain, AuthenticationMethod = "Token", hasToken = !string.IsNullOrEmpty(token) });

        }

        /// <summary>
        /// Initializes a new instance of the <see cref="DynamicDnsClient"/> class.
        /// </summary>
        /// <param name="handler">The handler.</param>
        public DynamicDnsClient(HttpMessageHandler? handler = null)
        {
            // Enforce HTTPS and allow custom handlers for advanced scenarios
            _httpClient = handler != null ? new HttpClient(handler, disposeHandler: true) : new HttpClient();
            Diagnostics.Log("DnsClientInitialized", new { hasCustomHandler = handler != null });

        }

        /// <summary>
        /// Updates the DNS record at the DDNS provider using HTTPS and secure authentication.
        /// </summary>
        /// <param name="updateUrl">The provider's update URL (must use HTTPS).</param>
        /// <param name="username">Optional username for basic auth.</param>
        /// <param name="password">Optional password for basic auth.</param>
        /// <param name="bearerToken">Optional OAuth2 bearer token.</param>
        /// <returns>True if update was successful, false otherwise.</returns>
public async Task<bool> UpdateAsync(
    string updateUrl,
    string? username = null,
    string? password = null,
    string? bearerToken = null)
{
    if (!Uri.TryCreate(updateUrl, UriKind.Absolute, out var uri) || uri.Scheme != Uri.UriSchemeHttps)
        throw new ArgumentException("Update URL must use HTTPS.", nameof(updateUrl));

    var authType = !string.IsNullOrEmpty(bearerToken) ? "Bearer" : 
                   !string.IsNullOrEmpty(username) ? "Basic" : "None";
    
    Diagnostics.Log("DnsUpdateAttempt", new { 
        updateUrl = uri.Host, // Log host only for security
        authType,
        timestamp = DateTimeOffset.UtcNow 
    });

    var request = new HttpRequestMessage(HttpMethod.Get, updateUrl);

    if (!string.IsNullOrEmpty(bearerToken))
    {
        request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", bearerToken);
    }
    else if (!string.IsNullOrEmpty(username) && password != null)
    {
        var byteArray = System.Text.Encoding.ASCII.GetBytes($"{username}:{password}");
        request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Basic", Convert.ToBase64String(byteArray));
    }

    try
    {
        var response = await _httpClient.SendAsync(request);
        
        if (response.IsSuccessStatusCode)
        {
            Diagnostics.Log("DnsUpdateSuccess", new { 
                host = uri.Host,
                statusCode = (int)response.StatusCode,
                authType
            });
            return true;
        }
        else
        {
            Diagnostics.Log("DnsUpdateFailure", new { 
                host = uri.Host,
                statusCode = (int)response.StatusCode,
                reasonPhrase = response.ReasonPhrase,
                authType
            });
            return false;
        }
    }
    catch (HttpRequestException ex)
    {
        Diagnostics.Log("DnsUpdateError", new { 
            host = uri.Host,
            error = "HttpRequestException",
            message = ex.Message,
            authType
        });
        return false;
    }
    catch (TaskCanceledException ex) when (ex.InnerException is TimeoutException)
    {
        Diagnostics.Log("DnsUpdateError", new { 
            host = uri.Host,
            error = "Timeout",
            message = ex.Message,
            authType
        });
        return false;
    }
    catch (Exception ex)
    {
        Diagnostics.Log("DnsUpdateError", new { 
            host = uri.Host,
            error = ex.GetType().Name,
            message = ex.Message,
            authType
        });
        return false;
    }
}

        /// <summary>
        /// TODO: Add Summary.
        /// </summary>
        public void Dispose()
        {
            Diagnostics.Log("DnsClientDisposed", new { timestamp = DateTimeOffset.UtcNow });
            _httpClient.Dispose();
            GC.SuppressFinalize(this);
        }
    }
}