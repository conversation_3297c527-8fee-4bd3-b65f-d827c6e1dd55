using Microsoft.Extensions.Logging;
using System;
using System.IO;
using System.Runtime.InteropServices;
using Serilog;
using Serilog.Extensions.Logging;

namespace USDP2
{
    /// <summary>
    /// Provides platform-specific logging provider selection and configuration.
    /// 
    /// This class implements intelligent logging provider selection based on the current
    /// platform and runtime environment, with automatic fallback to file logging when
    /// system logging services are unavailable.
    /// 
    /// <para><strong>Platform Support:</strong></para>
    /// <list type="bullet">
    /// <item><description><strong>Windows:</strong> Event Log → File fallback</description></item>
    /// <item><description><strong>Linux:</strong> Syslog → File fallback</description></item>
    /// <item><description><strong>macOS:</strong> Syslog → File fallback</description></item>
    /// <item><description><strong>Containers:</strong> Structured logging → File fallback</description></item>
    /// </list>
    /// 
    /// <para><strong>Fallback Strategy:</strong></para>
    /// <para>
    /// When system logging is unavailable (permissions, service not running, etc.),
    /// the provider automatically falls back to file logging in platform-appropriate
    /// temporary directories with proper error handling and graceful degradation.
    /// </para>
    /// </summary>
    public static class PlatformLoggingProvider
    {
        private static readonly string DefaultLogDirectory = GetDefaultLogDirectory();
        private static readonly string DefaultLogFileName = "usdp2.log";

        /// <summary>
        /// Creates a logger factory with platform-appropriate providers and file fallback.
        /// </summary>
        /// <param name="minimumLevel">The minimum log level to capture.</param>
        /// <param name="customLogPath">Optional custom log file path. If null, uses platform default.</param>
        /// <returns>A configured ILoggerFactory with platform-specific providers.</returns>
        public static ILoggerFactory CreatePlatformLoggerFactory(LogLevel minimumLevel, string? customLogPath = null)
        {
            var logFilePath = customLogPath ?? Path.Combine(DefaultLogDirectory, DefaultLogFileName);

            // Ensure log directory exists
            try
            {
                var logDir = Path.GetDirectoryName(logFilePath);
                if (!string.IsNullOrEmpty(logDir) && !Directory.Exists(logDir))
                {
                    Directory.CreateDirectory(logDir);
                }
            }
            catch
            {
                // If we can't create the directory, fall back to temp
                logFilePath = Path.Combine(Path.GetTempPath(), "usdp2", DefaultLogFileName);
                try
                {
                    var tempDir = Path.GetDirectoryName(logFilePath);
                    if (!string.IsNullOrEmpty(tempDir) && !Directory.Exists(tempDir))
                    {
                        Directory.CreateDirectory(tempDir);
                    }
                }
                catch
                {
                    // Last resort - use temp file
                    logFilePath = Path.GetTempFileName();
                }
            }

            // Create Serilog logger with platform-specific sinks
            var loggerConfig = new LoggerConfiguration()
                .MinimumLevel.Is(ConvertLogLevel(minimumLevel))
                .Enrich.WithProperty("Application", "USDP2")
                .Enrich.WithProperty("Platform", GetPlatformName())
                .Enrich.WithProperty("ProcessId", Environment.ProcessId);

            // Add platform-specific sinks with fallback
            AddPlatformSpecificSinks(loggerConfig, logFilePath);

            var serilogLogger = loggerConfig.CreateLogger();

            return new SerilogLoggerFactory(serilogLogger, dispose: true);
        }

        /// <summary>
        /// Adds platform-specific logging sinks with automatic fallback to file logging.
        /// </summary>
        private static void AddPlatformSpecificSinks(LoggerConfiguration config, string fallbackLogPath)
        {
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            {
                AddWindowsLogging(config, fallbackLogPath);
            }
            else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
            {
                AddLinuxLogging(config, fallbackLogPath);
            }
            else if (RuntimeInformation.IsOSPlatform(OSPlatform.OSX))
            {
                AddMacOSLogging(config, fallbackLogPath);
            }
            else
            {
                // Unknown platform - use file logging only
                AddFileLogging(config, fallbackLogPath);
            }
        }

        /// <summary>
        /// Configures Windows-specific logging (Event Log with file fallback).
        /// </summary>
        private static void AddWindowsLogging(LoggerConfiguration config, string fallbackLogPath)
        {
            try
            {
                // Try to add Windows Event Log
                config.WriteTo.EventLog(
                    source: "USDP2",
                    logName: "Application",
                    restrictedToMinimumLevel: Serilog.Events.LogEventLevel.Information);

                // Also add file logging for detailed logs
                AddFileLogging(config, fallbackLogPath);
            }
            catch
            {
                // Event Log not available (permissions, etc.) - fall back to file only
                AddFileLogging(config, fallbackLogPath);
            }
        }

        /// <summary>
        /// Configures Linux-specific logging (file-based with structured format).
        /// </summary>
        private static void AddLinuxLogging(LoggerConfiguration config, string fallbackLogPath)
        {
            // For Linux, use structured file logging
            // Applications can add syslog integration if needed
            AddFileLogging(config, fallbackLogPath);
        }

        /// <summary>
        /// Configures macOS-specific logging (file-based with structured format).
        /// </summary>
        private static void AddMacOSLogging(LoggerConfiguration config, string fallbackLogPath)
        {
            // For macOS, use structured file logging
            // Applications can add unified logging integration if needed
            AddFileLogging(config, fallbackLogPath);
        }

        /// <summary>
        /// Adds file logging with rotation and structured format.
        /// </summary>
        private static void AddFileLogging(LoggerConfiguration config, string logPath)
        {
            try
            {
                config.WriteTo.File(
                    path: logPath,
                    rollingInterval: RollingInterval.Day,
                    retainedFileCountLimit: 7,
                    fileSizeLimitBytes: 10 * 1024 * 1024, // 10MB
                    outputTemplate: "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {SourceContext}: {Message:lj}{NewLine}{Exception}");
            }
            catch
            {
                // If file logging fails, we'll have no output - but we won't crash
                // This is appropriate for a class library
            }
        }

        /// <summary>
        /// Gets the platform-appropriate default log directory.
        /// </summary>
        private static string GetDefaultLogDirectory()
        {
            try
            {
                if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                {
                    var programData = Environment.GetFolderPath(Environment.SpecialFolder.CommonApplicationData);
                    return Path.Combine(programData, "USDP2", "Logs");
                }
                else
                {
                    // Unix-like systems
                    return "/var/log/usdp2";
                }
            }
            catch
            {
                // Fallback to temp directory
                return Path.Combine(Path.GetTempPath(), "usdp2", "logs");
            }
        }

        /// <summary>
        /// Gets a human-readable platform name for logging.
        /// </summary>
        private static string GetPlatformName()
        {
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                return "Windows";
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
                return "Linux";
            if (RuntimeInformation.IsOSPlatform(OSPlatform.OSX))
                return "macOS";
            return "Unknown";
        }

        /// <summary>
        /// Converts Microsoft.Extensions.Logging.LogLevel to Serilog.Events.LogEventLevel.
        /// </summary>
        private static Serilog.Events.LogEventLevel ConvertLogLevel(LogLevel logLevel)
        {
            return logLevel switch
            {
                LogLevel.Trace => Serilog.Events.LogEventLevel.Verbose,
                LogLevel.Debug => Serilog.Events.LogEventLevel.Debug,
                LogLevel.Information => Serilog.Events.LogEventLevel.Information,
                LogLevel.Warning => Serilog.Events.LogEventLevel.Warning,
                LogLevel.Error => Serilog.Events.LogEventLevel.Error,
                LogLevel.Critical => Serilog.Events.LogEventLevel.Fatal,
                LogLevel.None => Serilog.Events.LogEventLevel.Fatal,
                _ => Serilog.Events.LogEventLevel.Information
            };
        }

        /// <summary>
        /// Checks if the current environment is likely running in a container.
        /// </summary>
        public static bool IsRunningInContainer()
        {
            try
            {
                // Check for common container indicators
                return Environment.GetEnvironmentVariable("DOTNET_RUNNING_IN_CONTAINER") == "true" ||
                       File.Exists("/.dockerenv") ||
                       Environment.GetEnvironmentVariable("KUBERNETES_SERVICE_HOST") != null;
            }
            catch
            {
                return false;
            }
        }
    }
}
