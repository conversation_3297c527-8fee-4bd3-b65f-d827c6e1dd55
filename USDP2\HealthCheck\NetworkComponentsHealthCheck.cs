using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Net.NetworkInformation;
using System.Threading;
using System.Threading.Tasks;

namespace USDP2.HealthCheck
{
    /// <summary>
    /// Health check for network components that tests connectivity, response times,
    /// and overall network communication functionality.
    /// </summary>
    public class NetworkComponentsHealthCheck : HealthCheckBase
    {
        private readonly INetworkSender? _networkSender;
        private readonly INetworkReceiver? _networkReceiver;
        private readonly HealthCheckOptions _options;

        /// <summary>
        /// Initializes a new instance of the <see cref="NetworkComponentsHealthCheck"/> class.
        /// </summary>
        /// <param name="networkSender">The network sender to monitor (optional).</param>
        /// <param name="networkReceiver">The network receiver to monitor (optional).</param>
        /// <param name="options">The health check options.</param>
        public NetworkComponentsHealthCheck(
            INetworkSender? networkSender = null, 
            INetworkReceiver? networkReceiver = null, 
            HealthCheckOptions? options = null)
            : base("NetworkComponents", "Monitors network connectivity and response times", options?.Timeout)
        {
            _networkSender = networkSender;
            _networkReceiver = networkReceiver;
            _options = options ?? new HealthCheckOptions();
        }

        /// <summary>
        /// Performs the network components health check.
        /// </summary>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>A <see cref="Task{HealthCheckResult}"/> representing the health check result.</returns>
        protected override async Task<HealthCheckResult> PerformHealthCheckAsync(CancellationToken cancellationToken)
        {
            var stopwatch = Stopwatch.StartNew();
            var healthData = new Dictionary<string, object>();
            var issues = new List<string>();

            try
            {
                // Check 1: Test basic network connectivity
                await CheckNetworkConnectivityAsync(healthData, issues, cancellationToken).ConfigureAwait(false);

                // Check 2: Test network sender functionality
                if (_networkSender != null)
                {
                    await CheckNetworkSenderAsync(healthData, issues, cancellationToken).ConfigureAwait(false);
                }

                // Check 3: Test network receiver functionality
                if (_networkReceiver != null)
                {
                    await CheckNetworkReceiverAsync(healthData, issues, cancellationToken).ConfigureAwait(false);
                }

                // Check 4: Test response times and latency
                await CheckNetworkLatencyAsync(healthData, issues, cancellationToken).ConfigureAwait(false);

                // Check 5: Monitor network interface status
                await CheckNetworkInterfacesAsync(healthData, issues, cancellationToken).ConfigureAwait(false);

                stopwatch.Stop();

                // Determine overall health status
                var status = DetermineHealthStatus(issues, healthData);
                var description = CreateHealthDescription(status, issues);

                var result = new HealthCheckResult(status, description, stopwatch.Elapsed, null, healthData);

                if (_options.LogResults)
                {
                    LogResult(result);
                }

                return result;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                healthData["exception"] = ex.Message;
                healthData["stackTrace"] = ex.StackTrace ?? string.Empty;

                var result = HealthCheckResult.Unhealthy($"Network components health check failed: {ex.Message}", stopwatch.Elapsed, ex, healthData);

                if (_options.LogResults)
                {
                    LogResult(result);
                }

                return result;
            }
        }

        /// <summary>
        /// Checks basic network connectivity.
        /// </summary>
        private async Task CheckNetworkConnectivityAsync(Dictionary<string, object> healthData, List<string> issues, CancellationToken cancellationToken)
        {
            try
            {
                // Test connectivity to localhost
                var ping = new Ping();
                var reply = await ping.SendPingAsync("127.0.0.1", 5000).ConfigureAwait(false);

                healthData["localhostPingSuccessful"] = reply.Status == IPStatus.Success;
                healthData["localhostPingTime"] = reply.RoundtripTime;

                if (reply.Status != IPStatus.Success)
                {
                    issues.Add($"Localhost ping failed: {reply.Status}");
                }
                else if (reply.RoundtripTime > 100)
                {
                    issues.Add($"High localhost ping time: {reply.RoundtripTime}ms");
                }

                // Test connectivity to a well-known public DNS server
                try
                {
                    var publicReply = await ping.SendPingAsync("*******", 5000).ConfigureAwait(false);
                    healthData["publicDnsPingSuccessful"] = publicReply.Status == IPStatus.Success;
                    healthData["publicDnsPingTime"] = publicReply.RoundtripTime;

                    if (publicReply.Status != IPStatus.Success)
                    {
                        issues.Add($"Public DNS ping failed: {publicReply.Status} (may indicate network isolation)");
                    }
                    else if (publicReply.RoundtripTime > 500)
                    {
                        issues.Add($"High public DNS ping time: {publicReply.RoundtripTime}ms");
                    }
                }
                catch (Exception ex)
                {
                    healthData["publicDnsPingError"] = ex.Message;
                    // Don't add as issue since this might be expected in isolated environments
                }
            }
            catch (Exception ex)
            {
                healthData["networkConnectivityError"] = ex.Message;
                issues.Add($"Network connectivity check failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Checks network sender functionality.
        /// </summary>
        private async Task CheckNetworkSenderAsync(Dictionary<string, object> healthData, List<string> issues, CancellationToken cancellationToken)
        {
            if (_networkSender == null) return;

            var stopwatch = Stopwatch.StartNew();
            var successfulSends = 0;
            var failedSends = 0;
            const int testSends = 3;

            try
            {
                for (int i = 0; i < testSends; i++)
                {
                    try
                    {
                        var testData = System.Text.Encoding.UTF8.GetBytes($"health_check_test_{i}_{DateTime.UtcNow:yyyy-MM-dd_HH-mm-ss}");
                        
                        // Send to localhost on a test port
                        await _networkSender.SendAsync(testData, "127.0.0.1", 9999, cancellationToken).ConfigureAwait(false);
                        successfulSends++;
                    }
                    catch
                    {
                        failedSends++;
                    }

                    if (cancellationToken.IsCancellationRequested)
                        break;
                }

                stopwatch.Stop();

                healthData["senderTestTotal"] = testSends;
                healthData["senderTestSuccessful"] = successfulSends;
                healthData["senderTestFailed"] = failedSends;
                healthData["senderTestDuration"] = stopwatch.ElapsedMilliseconds;
                healthData["senderSuccessRate"] = testSends > 0 ? (double)successfulSends / testSends * 100 : 0;

                if (failedSends > testSends * 0.3) // More than 30% failure rate
                {
                    issues.Add($"High network sender failure rate: {failedSends}/{testSends} failed");
                }

                if (stopwatch.ElapsedMilliseconds > 3000) // More than 3 seconds for 3 sends
                {
                    issues.Add($"Network sender is slow: {stopwatch.ElapsedMilliseconds}ms for {testSends} sends");
                }
            }
            catch (Exception ex)
            {
                healthData["senderTestError"] = ex.Message;
                issues.Add($"Network sender check failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Checks network receiver functionality.
        /// </summary>
        private async Task CheckNetworkReceiverAsync(Dictionary<string, object> healthData, List<string> issues, CancellationToken cancellationToken)
        {
            if (_networkReceiver == null) return;

            try
            {
                // Since we can't easily test the receiver without setting up a full test scenario,
                // we'll check if it's in a valid state and can be started/stopped
                
                var receiverType = _networkReceiver.GetType().Name;
                healthData["receiverType"] = receiverType;
                healthData["receiverAvailable"] = true;

                // For UDP receivers, we could check if the port is available
                if (_networkReceiver is UdpNetworkReceiver udpReceiver)
                {
                    // Check if UDP receiver is properly configured
                    healthData["receiverProtocol"] = "UDP";
                    // Additional UDP-specific checks could be added here
                }
                else if (_networkReceiver is HttpNetworkReceiver httpReceiver)
                {
                    // Check if HTTP receiver is properly configured
                    healthData["receiverProtocol"] = "HTTP";
                    // Additional HTTP-specific checks could be added here
                }

                await Task.Delay(1, cancellationToken).ConfigureAwait(false); // Minimal async operation
            }
            catch (Exception ex)
            {
                healthData["receiverTestError"] = ex.Message;
                issues.Add($"Network receiver check failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Checks network latency and response times.
        /// </summary>
        private async Task CheckNetworkLatencyAsync(Dictionary<string, object> healthData, List<string> issues, CancellationToken cancellationToken)
        {
            try
            {
                var ping = new Ping();
                var latencies = new List<long>();
                const int pingCount = 5;

                for (int i = 0; i < pingCount; i++)
                {
                    var reply = await ping.SendPingAsync("127.0.0.1", 2000).ConfigureAwait(false);
                    if (reply.Status == IPStatus.Success)
                    {
                        latencies.Add(reply.RoundtripTime);
                    }

                    if (cancellationToken.IsCancellationRequested)
                        break;
                }

                if (latencies.Count > 0)
                {
                    var avgLatency = latencies.Sum() / (double)latencies.Count;
                    var maxLatency = latencies.Max();
                    var minLatency = latencies.Min();

                    healthData["averageLatency"] = avgLatency;
                    healthData["maxLatency"] = maxLatency;
                    healthData["minLatency"] = minLatency;
                    healthData["latencyVariance"] = maxLatency - minLatency;

                    if (avgLatency > 50)
                    {
                        issues.Add($"High average network latency: {avgLatency:F1}ms");
                    }

                    if (maxLatency - minLatency > 100)
                    {
                        issues.Add($"High network latency variance: {maxLatency - minLatency}ms");
                    }
                }
                else
                {
                    issues.Add("Unable to measure network latency - all pings failed");
                }
            }
            catch (Exception ex)
            {
                healthData["latencyTestError"] = ex.Message;
                issues.Add($"Network latency check failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Checks network interface status.
        /// </summary>
        private async Task CheckNetworkInterfacesAsync(Dictionary<string, object> healthData, List<string> issues, CancellationToken cancellationToken)
        {
            try
            {
                var interfaces = NetworkInterface.GetAllNetworkInterfaces();
                var activeInterfaces = 0;
                var upInterfaces = 0;

                foreach (var networkInterface in interfaces)
                {
                    if (networkInterface.NetworkInterfaceType != NetworkInterfaceType.Loopback &&
                        networkInterface.NetworkInterfaceType != NetworkInterfaceType.Tunnel)
                    {
                        activeInterfaces++;
                        
                        if (networkInterface.OperationalStatus == OperationalStatus.Up)
                        {
                            upInterfaces++;
                        }
                    }
                }

                healthData["totalNetworkInterfaces"] = interfaces.Length;
                healthData["activeNetworkInterfaces"] = activeInterfaces;
                healthData["upNetworkInterfaces"] = upInterfaces;

                if (upInterfaces == 0)
                {
                    issues.Add("No network interfaces are up");
                }
                else if (upInterfaces < activeInterfaces * 0.5)
                {
                    issues.Add($"Many network interfaces are down: {upInterfaces}/{activeInterfaces} up");
                }

                await Task.Delay(1, cancellationToken).ConfigureAwait(false); // Minimal async operation
            }
            catch (Exception ex)
            {
                healthData["networkInterfacesError"] = ex.Message;
                issues.Add($"Network interfaces check failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Determines the overall health status based on issues found.
        /// </summary>
        private static HealthStatus DetermineHealthStatus(List<string> issues, Dictionary<string, object> healthData)
        {
            if (issues.Count == 0)
                return HealthStatus.Healthy;

            // Check for critical issues
            var criticalIssues = issues.FindAll(issue => 
                issue.Contains("No network interfaces") || 
                issue.Contains("Localhost ping failed") ||
                issue.Contains("all pings failed"));

            if (criticalIssues.Count > 0)
                return HealthStatus.Unhealthy;

            // Check for performance issues
            var performanceIssues = issues.FindAll(issue => 
                issue.Contains("High") || 
                issue.Contains("slow") ||
                issue.Contains("Many network interfaces"));

            if (performanceIssues.Count > 0)
                return HealthStatus.Degraded;

            return issues.Count > 3 ? HealthStatus.Degraded : HealthStatus.Healthy;
        }

        /// <summary>
        /// Creates a health description based on the status and issues.
        /// </summary>
        private static string CreateHealthDescription(HealthStatus status, List<string> issues)
        {
            return status switch
            {
                HealthStatus.Healthy => "Network components are healthy and functioning normally",
                HealthStatus.Degraded => $"Network components are degraded with {issues.Count} issue(s): {string.Join("; ", issues)}",
                HealthStatus.Unhealthy => $"Network components are unhealthy with {issues.Count} issue(s): {string.Join("; ", issues)}",
                _ => $"Network components health status is unknown with {issues.Count} issue(s): {string.Join("; ", issues)}"
            };
        }
    }
}
