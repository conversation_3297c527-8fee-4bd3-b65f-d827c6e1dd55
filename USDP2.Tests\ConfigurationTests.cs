using System;
using System.IO;
using Microsoft.VisualStudio.TestTools.UnitTesting;

namespace USDP2.Tests
{
    [TestClass]
    public class ConfigurationTests
    {
        [TestMethod]
        public void UsdpConfiguration_Singleton_ReturnsSameInstance()
        {
            // Act
            var instance1 = UsdpConfiguration.Instance;
            var instance2 = UsdpConfiguration.Instance;

            // Assert
            Assert.AreSame(instance1, instance2);
            Assert.IsNotNull(instance1);
        }

        [TestMethod]
        public void UsdpConfiguration_DefaultValues_AreCorrect()
        {
            // Arrange
            var config = UsdpConfiguration.Instance;

            // Assert - Verify all default values match expected configuration
            Assert.AreEqual(UsdpConfiguration.LoggingMode.Auto, config.LogMode);
            Assert.AreEqual(TimeSpan.FromMinutes(5), config.DefaultTtl);
            Assert.AreEqual(TimeSpan.FromDays(365), config.MaxTtl);
            Assert.IsTrue(config.RequireAuthentication);
            Assert.AreEqual("psk-tls1.3", config.DefaultSecurity);
            Assert.AreEqual("***************", config.DefaultMulticastAddress);
            Assert.AreEqual(5353, config.DefaultMulticastPort);
            Assert.AreEqual(8080, config.DefaultHttpPort);
            Assert.AreEqual(8443, config.DefaultHttpsPort);
            Assert.AreEqual("/usdp", config.HttpEndpointPath);
            Assert.AreEqual("/usdp", config.HttpsEndpointPath);
            Assert.IsTrue(config.UseHttps);
            Assert.AreEqual("coap+udp", config.DefaultProtocol);
            Assert.AreEqual("127.0.0.1", config.DefaultServiceAddress);
            Assert.AreEqual(5683, config.DefaultServicePort);
            Assert.AreEqual(TimeSpan.FromSeconds(30), config.NetworkTimeout);
            Assert.AreEqual(TimeSpan.FromSeconds(2), config.QueryResponseTimeout);
            Assert.AreEqual(TimeSpan.FromMilliseconds(10), config.MdnsOperationDelay);
            Assert.AreEqual(TimeSpan.FromMilliseconds(100), config.StartupDelay);
            Assert.AreEqual("usdp_config.json", config.DefaultConfigFileName);
            Assert.AreEqual(1024, config.DefaultBufferSize);
            Assert.AreEqual(65536, config.MaxBufferSize);
            Assert.AreEqual(1, config.ResponsePortOffset);
            Assert.AreEqual("home/lighting", config.DefaultServiceType);
            Assert.AreEqual("bulb1", config.DefaultServiceInstance);
            Assert.AreEqual("lighting", config.DefaultMetadataType);
            Assert.AreEqual("room=101", config.DefaultMetadataLocation);
            Assert.IsNull(config.CustomSettings);
        }

        [TestMethod]
        public void UsdpConfiguration_DefaultConfigPath_IsValid()
        {
            // Arrange
            var config = UsdpConfiguration.Instance;

            // Act
            var configPath = config.DefaultConfigPath;

            // Assert
            Assert.IsNotNull(configPath);
            Assert.IsTrue(configPath.EndsWith("usdp_config.json"));
            Assert.IsTrue(Path.IsPathRooted(configPath));
        }

        [TestMethod]
        public void UsdpConfiguration_ModifyValues_PersistsChanges()
        {
            // Arrange
            var config = UsdpConfiguration.Instance;
            var originalPort = config.DefaultHttpPort;
            var newPort = 9999;

            try
            {
                // Act
                config.DefaultHttpPort = newPort;

                // Assert
                Assert.AreEqual(newPort, config.DefaultHttpPort);
                Assert.AreEqual(newPort, UsdpConfiguration.Instance.DefaultHttpPort);
            }
            finally
            {
                // Cleanup - restore original value
                config.DefaultHttpPort = originalPort;
            }
        }

        [TestMethod]
        public void UsdpConfiguration_LoggingModeEnum_HasCorrectValues()
        {
            // Assert - Verify the new enum values with Auto as first option
            Assert.AreEqual(0, (int)UsdpConfiguration.LoggingMode.Auto);
            Assert.AreEqual(1, (int)UsdpConfiguration.LoggingMode.None);
            Assert.AreEqual(2, (int)UsdpConfiguration.LoggingMode.Console);
            Assert.AreEqual(3, (int)UsdpConfiguration.LoggingMode.File);
            Assert.AreEqual(4, (int)UsdpConfiguration.LoggingMode.Both);
            Assert.AreEqual(5, (int)UsdpConfiguration.LoggingMode.EventLog);
            Assert.AreEqual(6, (int)UsdpConfiguration.LoggingMode.Syslog);
        }

        [TestMethod]
        public void UsdpConfiguration_NetworkPorts_AreInValidRange()
        {
            // Arrange
            var config = UsdpConfiguration.Instance;

            // Assert - All ports should be in valid range (1-65535)
            Assert.IsTrue(config.DefaultMulticastPort > 0 && config.DefaultMulticastPort <= 65535);
            Assert.IsTrue(config.DefaultHttpPort > 0 && config.DefaultHttpPort <= 65535);
            Assert.IsTrue(config.DefaultHttpsPort > 0 && config.DefaultHttpsPort <= 65535);
            Assert.IsTrue(config.DefaultServicePort > 0 && config.DefaultServicePort <= 65535);
        }

        [TestMethod]
        public void UsdpConfiguration_TimeoutValues_ArePositive()
        {
            // Arrange
            var config = UsdpConfiguration.Instance;

            // Assert - All timeout values should be positive
            Assert.IsTrue(config.NetworkTimeout > TimeSpan.Zero);
            Assert.IsTrue(config.QueryResponseTimeout > TimeSpan.Zero);
            Assert.IsTrue(config.MdnsOperationDelay > TimeSpan.Zero);
            Assert.IsTrue(config.StartupDelay > TimeSpan.Zero);
            Assert.IsTrue(config.DefaultTtl > TimeSpan.Zero);
            Assert.IsTrue(config.MaxTtl > TimeSpan.Zero);
        }

        [TestMethod]
        public void UsdpConfiguration_BufferSizes_AreValid()
        {
            // Arrange
            var config = UsdpConfiguration.Instance;

            // Assert
            Assert.IsTrue(config.DefaultBufferSize > 0);
            Assert.IsTrue(config.MaxBufferSize > 0);
            Assert.IsTrue(config.MaxBufferSize >= config.DefaultBufferSize);
        }

        [TestMethod]
        public void UsdpConfiguration_EndpointPaths_StartWithSlash()
        {
            // Arrange
            var config = UsdpConfiguration.Instance;

            // Assert
            Assert.IsTrue(config.HttpEndpointPath.StartsWith("/"));
            Assert.IsTrue(config.HttpsEndpointPath.StartsWith("/"));
        }

        [TestMethod]
        public void UsdpConfiguration_DefaultValues_AreNotNullOrEmpty()
        {
            // Arrange
            var config = UsdpConfiguration.Instance;

            // Assert - String values should not be null or empty
            Assert.IsFalse(string.IsNullOrEmpty(config.DefaultSecurity));
            Assert.IsFalse(string.IsNullOrEmpty(config.DefaultMulticastAddress));
            Assert.IsFalse(string.IsNullOrEmpty(config.HttpEndpointPath));
            Assert.IsFalse(string.IsNullOrEmpty(config.HttpsEndpointPath));
            Assert.IsFalse(string.IsNullOrEmpty(config.DefaultProtocol));
            Assert.IsFalse(string.IsNullOrEmpty(config.DefaultServiceAddress));
            Assert.IsFalse(string.IsNullOrEmpty(config.DefaultConfigFileName));
            Assert.IsFalse(string.IsNullOrEmpty(config.DefaultServiceType));
            Assert.IsFalse(string.IsNullOrEmpty(config.DefaultServiceInstance));
            Assert.IsFalse(string.IsNullOrEmpty(config.DefaultMetadataType));
            Assert.IsFalse(string.IsNullOrEmpty(config.DefaultMetadataLocation));
        }

        [TestMethod]
        public void UsdpConfiguration_CustomSettings_CanBeSet()
        {
            // Arrange
            var config = UsdpConfiguration.Instance;
            var customData = new { TestValue = "test", TestNumber = 42 };

            try
            {
                // Act
                config.CustomSettings = customData;

                // Assert
                Assert.IsNotNull(config.CustomSettings);
                Assert.AreSame(customData, config.CustomSettings);
            }
            finally
            {
                // Cleanup
                config.CustomSettings = null;
            }
        }

        [TestMethod]
        public void UsdpConfiguration_TTL_MaxIsGreaterThanDefault()
        {
            // Arrange
            var config = UsdpConfiguration.Instance;

            // Assert
            Assert.IsTrue(config.MaxTtl > config.DefaultTtl);
        }

        [TestMethod]
        public void UsdpConfiguration_ResponsePortOffset_IsPositive()
        {
            // Arrange
            var config = UsdpConfiguration.Instance;

            // Assert
            Assert.IsTrue(config.ResponsePortOffset > 0);
        }
    }
}
