using System;
using System.Threading;
using System.Threading.Tasks;

namespace USDP2.Examples
{
    /// <summary>
    /// Demonstrates how to use the circuit breaker pattern for network operations in USDP2.
    /// 
    /// This example shows:
    /// - Basic circuit breaker usage
    /// - Network sender wrapping with circuit breakers
    /// - Handling circuit breaker exceptions
    /// - Monitoring circuit breaker health
    /// - Integration with existing USDP2 components
    /// </summary>
    public class CircuitBreakerExample
    {
        /// <summary>
        /// Demonstrates basic circuit breaker usage for network operations.
        /// </summary>
        public static async Task BasicCircuitBreakerExampleAsync()
        {
            Console.WriteLine("=== Basic Circuit Breaker Example ===");

            // Create a circuit breaker with custom configuration
            var options = new CircuitBreakerOptions
            {
                FailureThreshold = 3,           // Open after 3 failures
                OpenTimeout = TimeSpan.FromSeconds(10), // Stay open for 10 seconds
                SuccessThreshold = 2,           // Close after 2 successes in half-open
                OperationTimeout = TimeSpan.FromSeconds(5) // 5 second operation timeout
            };

            var circuitBreaker = new CircuitBreaker("ExampleOperation", options);

            // Simulate successful operations
            Console.WriteLine("Executing successful operations...");
            for (int i = 0; i < 5; i++)
            {
                try
                {
                    var result = await circuitBreaker.ExecuteAsync(async ct =>
                    {
                        await Task.Delay(100, ct); // Simulate work
                        return $"Success {i + 1}";
                    });

                    Console.WriteLine($"✓ {result} - Circuit State: {circuitBreaker.State}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"✗ Failed: {ex.Message}");
                }
            }

            // Simulate failures to open the circuit
            Console.WriteLine("\nSimulating failures to open circuit...");
            for (int i = 0; i < 3; i++)
            {
                try
                {
                    await circuitBreaker.ExecuteAsync<string>(async ct =>
                    {
                        throw new InvalidOperationException($"Simulated failure {i + 1}");
                    });
                }
                catch (InvalidOperationException ex)
                {
                    Console.WriteLine($"✗ Expected failure: {ex.Message} - Circuit State: {circuitBreaker.State}");
                }
            }

            // Try operation when circuit is open
            Console.WriteLine("\nTrying operation when circuit is open...");
            try
            {
                await circuitBreaker.ExecuteAsync<string>(async ct =>
                {
                    return "This should not execute";
                });
            }
            catch (CircuitBreakerOpenException ex)
            {
                Console.WriteLine($"✗ Circuit breaker blocked operation: {ex.Message}");
                Console.WriteLine($"   Time until retry: {ex.TimeUntilRetry.TotalSeconds:F1} seconds");
            }

            // Display statistics
            var stats = circuitBreaker.GetStatistics();
            Console.WriteLine($"\nCircuit Breaker Statistics:");
            Console.WriteLine($"  State: {stats.State}");
            Console.WriteLine($"  Total Operations: {stats.TotalOperations}");
            Console.WriteLine($"  Total Successes: {stats.TotalSuccesses}");
            Console.WriteLine($"  Total Failures: {stats.TotalFailures}");
            Console.WriteLine($"  Failure Rate: {stats.FailureRate:P1}");
        }

        /// <summary>
        /// Demonstrates using circuit breaker-wrapped network senders.
        /// </summary>
        public static async Task NetworkSenderCircuitBreakerExampleAsync()
        {
            Console.WriteLine("\n=== Network Sender Circuit Breaker Example ===");

            // Create circuit breaker-wrapped network senders
            var httpSender = NetworkCircuitBreakerFactory.CreateHttpSender();
            var udpSender = NetworkCircuitBreakerFactory.CreateUdpSender();

            // Example data to send
            var data = System.Text.Encoding.UTF8.GetBytes("Hello, USDP2!");

            Console.WriteLine("Sending data with circuit breaker protection...");

            try
            {
                // HTTP send with circuit breaker protection
                await httpSender.SendAsync(data, "httpbin.org", 80);
                Console.WriteLine("✓ HTTP send successful");

                // Get circuit breaker statistics if using wrapper
                if (httpSender is NetworkCircuitBreakerWrapper wrapper)
                {
                    var stats = wrapper.GetCircuitBreakerStatistics();
                    Console.WriteLine($"  HTTP Circuit State: {stats.State}");
                }
            }
            catch (CircuitBreakerOpenException ex)
            {
                Console.WriteLine($"✗ HTTP circuit breaker blocked: {ex.Message}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ HTTP send failed: {ex.Message}");
            }

            try
            {
                // UDP send with circuit breaker protection
                await udpSender.SendAsync(data, "127.0.0.1", 12345);
                Console.WriteLine("✓ UDP send successful");
            }
            catch (CircuitBreakerOpenException ex)
            {
                Console.WriteLine($"✗ UDP circuit breaker blocked: {ex.Message}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ UDP send failed: {ex.Message}");
            }

            // Clean up
            if (httpSender is IDisposable disposableHttp)
                disposableHttp.Dispose();
            if (udpSender is IDisposable disposableUdp)
                disposableUdp.Dispose();
        }

        /// <summary>
        /// Demonstrates monitoring circuit breaker health across the system.
        /// </summary>
        public static void CircuitBreakerHealthMonitoringExample()
        {
            Console.WriteLine("\n=== Circuit Breaker Health Monitoring Example ===");

            // Get system-wide circuit breaker health
            var healthSummary = CircuitBreakerManager.Instance.GetHealthSummary();

            Console.WriteLine("System-wide Circuit Breaker Health:");
            Console.WriteLine($"  Total Circuit Breakers: {healthSummary.TotalCircuitBreakers}");
            Console.WriteLine($"  Closed Circuits: {healthSummary.ClosedCircuits}");
            Console.WriteLine($"  Open Circuits: {healthSummary.OpenCircuits}");
            Console.WriteLine($"  Half-Open Circuits: {healthSummary.HalfOpenCircuits}");
            Console.WriteLine($"  Overall Failure Rate: {healthSummary.OverallFailureRate:P1}");

            if (healthSummary.MostProblematicCircuits.Count > 0)
            {
                Console.WriteLine("\nMost Problematic Circuits:");
                foreach (var circuit in healthSummary.MostProblematicCircuits)
                {
                    Console.WriteLine($"  {circuit.Key}: {circuit.Value:P1} failure rate");
                }
            }

            // Get detailed statistics for all circuit breakers
            var allStatistics = CircuitBreakerManager.Instance.GetHealthStatistics();
            if (allStatistics.Count > 0)
            {
                Console.WriteLine("\nDetailed Circuit Breaker Statistics:");
                foreach (var kvp in allStatistics)
                {
                    var stats = kvp.Value;
                    Console.WriteLine($"  {kvp.Key}:");
                    Console.WriteLine($"    State: {stats.State}");
                    Console.WriteLine($"    Operations: {stats.TotalOperations}");
                    Console.WriteLine($"    Failure Rate: {stats.FailureRate:P1}");
                    if (stats.State == CircuitBreakerState.Open)
                    {
                        Console.WriteLine($"    Time Until Retry: {stats.TimeUntilRetry.TotalSeconds:F1}s");
                    }
                }
            }
        }

        /// <summary>
        /// Demonstrates using circuit breakers with LocalDirectory and MdnsProxy.
        /// </summary>
        public static async Task IntegratedCircuitBreakerExampleAsync()
        {
            Console.WriteLine("\n=== Integrated Circuit Breaker Example ===");

            try
            {
                // Create network components with circuit breaker protection
                var sender = NetworkCircuitBreakerFactory.CreateHttpSender();
                var receiver = new HttpNetworkReceiver(8080); // This would need circuit breaker integration too

                // Create LocalDirectory with circuit breaker-protected sender
                var directory = new LocalDirectory(sender, receiver, "224.0.0.1", 5353);

                Console.WriteLine("LocalDirectory created with circuit breaker protection");

                // Create MdnsProxy with built-in circuit breaker protection
                var cache = new ServiceAdvertisementCache();
                var mdnsProxy = new MdnsProxy(cache);

                Console.WriteLine("MdnsProxy created with built-in circuit breaker protection");

                // Simulate operations that might fail
                try
                {
                    await mdnsProxy.PublishToMdnsAsync();
                    Console.WriteLine("✓ mDNS publish successful");
                }
                catch (CircuitBreakerOpenException ex)
                {
                    Console.WriteLine($"✗ mDNS publish blocked by circuit breaker: {ex.Message}");
                }

                try
                {
                    await mdnsProxy.ImportFromMdnsAsync();
                    Console.WriteLine("✓ mDNS import successful");
                }
                catch (CircuitBreakerOpenException ex)
                {
                    Console.WriteLine($"✗ mDNS import blocked by circuit breaker: {ex.Message}");
                }

                // Clean up
                await directory.DisposeAsync();
                await receiver.DisposeAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Integrated example failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Main example runner that demonstrates all circuit breaker features.
        /// </summary>
        public static async Task RunAllExamplesAsync()
        {
            Console.WriteLine("USDP2 Circuit Breaker Pattern Examples");
            Console.WriteLine("=====================================");

            try
            {
                await BasicCircuitBreakerExampleAsync();
                await NetworkSenderCircuitBreakerExampleAsync();
                CircuitBreakerHealthMonitoringExample();
                await IntegratedCircuitBreakerExampleAsync();

                Console.WriteLine("\n✓ All circuit breaker examples completed successfully!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"\n✗ Example execution failed: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }
    }
}
