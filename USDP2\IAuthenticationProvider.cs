namespace USDP2
{
    /// <summary>
    /// Interface for authentication providers (e.g., PSK, OAuth2).
    /// </summary>
    public interface IAuthenticationProvider
    {
        /// <summary>
        /// Returns true if authentication is successful.
        /// </summary>
        /// <param name="tokenOr<PERSON>ey">The token or key.</param>
        /// <returns></returns>
        bool Authenticate(string tokenOrKey); // (optional, for legacy sync support)
        /// <summary>
        /// Authenticates the asynchronous.
        /// </summary>
        /// <param name="tokenOr<PERSON>ey">The token or key.</param>
        /// <returns></returns>
        Task<bool> AuthenticateAsync(string tokenOrKey);
    }
}