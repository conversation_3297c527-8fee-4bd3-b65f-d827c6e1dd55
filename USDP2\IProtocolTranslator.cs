using System.Threading;
using System.Threading.Tasks;

namespace USDP2
{
   
    /// <summary>
    /// Interface for translating between different protocols.
    /// </summary>
    public interface IProtocolTranslator
    {
        /// <summary>
        /// Translates the asynchronous.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <param name="fromProtocol">From protocol.</param>
        /// <param name="toProtocol">To protocol.</param>
        /// <param name="targetAddress">The target address.</param>
        /// <param name="targetPort">The target port.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns></returns>
        Task TranslateAsync(byte[] request, string fromProtocol, string toProtocol, string targetAddress, int targetPort, CancellationToken cancellationToken = default);
    }
}