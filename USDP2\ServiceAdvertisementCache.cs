using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace USDP2
{
    /// <summary>
    /// Caches service advertisements and manages TTL/heartbeat.
    /// </summary>
    public class ServiceAdvertisementCache : IDisposable
    {
        /// <summary>
        /// The cache.
        /// </summary>
        private readonly ConcurrentDictionary<string, CachedAdvertisement> _cache = new();
        /// <summary>
        /// The cleanup interval.
        /// </summary>
        private readonly TimeSpan _cleanupInterval;
        /// <summary>
        /// The cleanup timer.
        /// </summary>
        private readonly Timer _cleanupTimer;

        /// <summary>
        /// Initializes a new instance of the <see cref="ServiceAdvertisementCache"/> class.
        /// </summary>
        /// <param name="cleanupInterval">The cleanup interval.</param>
        public ServiceAdvertisementCache(TimeSpan? cleanupInterval = null)
        {
            _cleanupInterval = cleanupInterval ?? TimeSpan.FromSeconds(30);
            _cleanupTimer = new Timer(_ => CleanupExpired(), null, _cleanupInterval, _cleanupInterval);
        }

        /// <summary>
        /// Add or update.
        /// </summary>
        /// <param name="adv">The adv.</param>
        public void AddOrUpdate(ServiceAdvertisement adv)
        {
            var key = adv.ServiceId.ToString();
            var expiry = adv.Ttl.HasValue ? adv.Timestamp + adv.Ttl.Value : (DateTimeOffset?)null;
            _cache[key] = new CachedAdvertisement(adv, expiry);
        }


        /// <summary>
        /// Get active advertisements.
        /// </summary>
        /// <returns>A list of serviceadvertisements.</returns>
        public IEnumerable<ServiceAdvertisement> GetActiveAdvertisements()
        {
            var now = DateTimeOffset.UtcNow;
            return _cache.Values
                .Where(ca => !ca.Expiry.HasValue || ca.Expiry > now)
                .Select(ca => ca.Advertisement);
        }

        /// <summary>
        /// Receive the heartbeat.
        /// </summary>
        /// <param name="serviceId">The service id.</param>
        public void ReceiveHeartbeat(ServiceIdentifier serviceId)
        {
            if (_cache.TryGetValue(serviceId.ToString(), out var cached))
            {
                // Update timestamp to now for ephemeral services
                var adv = cached.Advertisement;
                adv.Timestamp = DateTimeOffset.UtcNow;
                AddOrUpdate(adv);
            }
            else Diagnostics.Log("HeartbeatReceivedForUnknownService", new { ServiceId = serviceId.ToString() });
        }

        /// <summary>
        /// Cleanups the expired.
        /// </summary>
        private void CleanupExpired()
        {
            var now = DateTimeOffset.UtcNow;
            foreach (var kvp in _cache)
            {
                if (kvp.Value.Expiry.HasValue && kvp.Value.Expiry <= now)
                    _cache.TryRemove(kvp.Key, out _);
                    Diagnostics.Log("ExpiredAdvertisementRemoved", new { ServiceId = kvp.Key });
            }
        }

        /// <summary>
        /// The cached advertisement.
        /// </summary>
        private sealed record CachedAdvertisement(ServiceAdvertisement Advertisement, DateTimeOffset? Expiry);

        /// <summary>
        /// TODO: Add Summary.
        /// </summary>
        public void Dispose()
        {
            _cleanupTimer.Dispose();
            GC.SuppressFinalize(this);
        }
    }
}