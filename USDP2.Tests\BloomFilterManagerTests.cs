using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using USDP2;

namespace USDP2.Tests
{
    [TestClass]
    public class BloomFilterManagerTests
    {
        [TestInitialize]
        public void Setup()
        {
            // Configure the singleton instance for testing
            var config = UsdpConfiguration.Instance;
            config.EnableBloomFilters = true;
            config.BloomFilterSize = 10000;
            config.BloomFilterHashFunctions = 5;
            config.ExpectedServiceCount = 1000;
            config.MaxFalsePositiveRate = 0.01;
            config.AutoTuneBloomFilter = false; // Disable auto-tune for predictable tests
            config.BloomFilterRefreshInterval = TimeSpan.FromSeconds(1);
        }

        [TestMethod]
        public void BloomFilterManager_Constructor_EnabledConfiguration_InitializesCorrectly()
        {
            // Act
            using var manager = new BloomFilterManager();

            // Assert
            Assert.IsTrue(manager.IsEnabled);
            Assert.AreEqual(0, manager.ServiceCount);
            Assert.IsTrue(manager.EstimatedFalsePositiveRate >= 0);
        }

        [TestMethod]
        public void BloomFilterManager_Constructor_DisabledConfiguration_DoesNotInitialize()
        {
            // Arrange
            var config = UsdpConfiguration.Instance;
            var originalValue = config.EnableBloomFilters;
            config.EnableBloomFilters = false;

            try
            {
                // Act
                using var manager = new BloomFilterManager();

                // Assert
                Assert.IsFalse(manager.IsEnabled);
                Assert.AreEqual(0, manager.ServiceCount);
            }
            finally
            {
                // Restore original value
                config.EnableBloomFilters = originalValue;
            }
        }

        [TestMethod]
        public void BloomFilterManager_AddService_IncreasesServiceCount()
        {
            // Arrange
            using var manager = new BloomFilterManager();
            var config = UsdpConfiguration.Instance;
            var serviceId = new ServiceIdentifier(config.DefaultServiceType, config.DefaultServiceInstance);
            var endpoint = new TransportEndpoint
            {
                Protocol = config.DefaultProtocol,
                Address = config.DefaultServiceAddress,
                Port = config.DefaultServicePort,
                Security = config.DefaultSecurity
            };
            var advertisement = new ServiceAdvertisement(serviceId, endpoint);

            // Act
            manager.AddService(advertisement);

            // Assert
            Assert.AreEqual(1, manager.ServiceCount);
        }

        [TestMethod]
        public void BloomFilterManager_AddService_NullAdvertisement_DoesNotThrow()
        {
            // Arrange
            using var manager = new BloomFilterManager();

            // Act & Assert - Should not throw
            manager.AddService(null!);
            Assert.AreEqual(0, manager.ServiceCount);
        }

        [TestMethod]
        public void BloomFilterManager_MightContainService_AddedService_ReturnsTrue()
        {
            // Arrange
            using var manager = new BloomFilterManager();
            var config = UsdpConfiguration.Instance;
            var serviceId = new ServiceIdentifier("test", "service1");
            var endpoint = new TransportEndpoint
            {
                Protocol = config.DefaultProtocol,
                Address = config.DefaultServiceAddress,
                Port = config.DefaultServicePort,
                Security = config.DefaultSecurity
            };
            var advertisement = new ServiceAdvertisement(serviceId, endpoint);

            // Act
            manager.AddService(advertisement);
            bool result = manager.MightContainService("test", "service1");

            // Assert
            Assert.IsTrue(result);
        }

        [TestMethod]
        public void BloomFilterManager_MightContainMetadata_AddedMetadata_ReturnsTrue()
        {
            // Arrange
            using var manager = new BloomFilterManager();
            var config = UsdpConfiguration.Instance;
            var serviceId = new ServiceIdentifier("test", "service1");
            var endpoint = new TransportEndpoint
            {
                Protocol = config.DefaultProtocol,
                Address = config.DefaultServiceAddress,
                Port = config.DefaultServicePort,
                Security = config.DefaultSecurity
            };
            var advertisement = new ServiceAdvertisement(serviceId, endpoint)
            {
                Metadata = new Dictionary<string, object>
                {
                    { "type", "sensor" },
                    { "location", "room1" }
                }
            };

            // Act
            manager.AddService(advertisement);
            bool typeResult = manager.MightContainMetadata("type", "sensor");
            bool locationResult = manager.MightContainMetadata("location", "room1");

            // Assert
            Assert.IsTrue(typeResult);
            Assert.IsTrue(locationResult);
        }

        [TestMethod]
        public void BloomFilterManager_DisabledFilter_AlwaysReturnsTrue()
        {
            // Arrange
            var config = UsdpConfiguration.Instance;
            var originalValue = config.EnableBloomFilters;
            config.EnableBloomFilters = false;

            try
            {
                using var manager = new BloomFilterManager();

                // Act
                bool serviceResult = manager.MightContainService("nonexistent", "service");
                bool metadataResult = manager.MightContainMetadata("nonexistent", "metadata");

                // Assert
                Assert.IsTrue(serviceResult);
                Assert.IsTrue(metadataResult);
            }
            finally
            {
                // Restore original value
                config.EnableBloomFilters = originalValue;
            }
        }

        [TestMethod]
        public void BloomFilterManager_RefreshFilters_ResetsAndRebuilds()
        {
            // Arrange
            using var manager = new BloomFilterManager();
            var config = UsdpConfiguration.Instance;
            var services = new List<ServiceAdvertisement>();

            for (int i = 0; i < 5; i++)
            {
                var serviceId = new ServiceIdentifier("test", $"service{i}");
                var endpoint = new TransportEndpoint
                {
                    Protocol = config.DefaultProtocol,
                    Address = config.DefaultServiceAddress,
                    Port = config.DefaultServicePort + i,
                    Security = config.DefaultSecurity
                };
                services.Add(new ServiceAdvertisement(serviceId, endpoint));
            }

            // Add services individually first
            foreach (var service in services.Take(3))
            {
                manager.AddService(service);
            }
            Assert.AreEqual(3, manager.ServiceCount);

            // Act - Refresh with all 5 services
            manager.RefreshFilters(services);

            // Assert
            Assert.AreEqual(5, manager.ServiceCount);

            // Verify all services are present
            for (int i = 0; i < 5; i++)
            {
                Assert.IsTrue(manager.MightContainService("test", $"service{i}"));
            }
        }

        [TestMethod]
        public void BloomFilterManager_AutoTuning_CalculatesOptimalParameters()
        {
            // Arrange
            var config = UsdpConfiguration.Instance;
            var originalAutoTune = config.AutoTuneBloomFilter;
            var originalExpected = config.ExpectedServiceCount;
            var originalFPR = config.MaxFalsePositiveRate;

            config.AutoTuneBloomFilter = true;
            config.ExpectedServiceCount = 1000;
            config.MaxFalsePositiveRate = 0.01;

            try
            {
                // Act
                using var manager = new BloomFilterManager();

                // Assert
                Assert.IsTrue(manager.IsEnabled);
                Assert.IsTrue(manager.EstimatedFalsePositiveRate <= config.MaxFalsePositiveRate * 2); // Allow some margin

                var stats = manager.GetStatistics();
                Assert.IsTrue((bool)stats["AutoTuned"]);
            }
            finally
            {
                // Restore original values
                config.AutoTuneBloomFilter = originalAutoTune;
                config.ExpectedServiceCount = originalExpected;
                config.MaxFalsePositiveRate = originalFPR;
            }
        }

        [TestMethod]
        public void BloomFilterManager_GetStatistics_ReturnsCorrectInformation()
        {
            // Arrange
            using var manager = new BloomFilterManager();
            var config = UsdpConfiguration.Instance;
            var serviceId = new ServiceIdentifier("test", "service1");
            var endpoint = new TransportEndpoint
            {
                Protocol = config.DefaultProtocol,
                Address = config.DefaultServiceAddress,
                Port = config.DefaultServicePort,
                Security = config.DefaultSecurity
            };
            var advertisement = new ServiceAdvertisement(serviceId, endpoint);

            // Act
            manager.AddService(advertisement);
            var stats = manager.GetStatistics();

            // Assert
            Assert.IsTrue((bool)stats["Enabled"]);
            Assert.AreEqual(1, (int)stats["ServiceCount"]);
            Assert.AreEqual(config.BloomFilterSize, (int)stats["FilterSize"]);
            Assert.AreEqual(config.BloomFilterHashFunctions, (int)stats["HashFunctions"]);
            Assert.IsTrue((double)stats["EstimatedFalsePositiveRate"] >= 0);
            Assert.IsTrue((int)stats["MemoryUsageBytes"] > 0);
            Assert.AreEqual(config.AutoTuneBloomFilter, (bool)stats["AutoTuned"]);
            Assert.AreEqual(config.BloomFilterRefreshInterval, (TimeSpan)stats["RefreshInterval"]);
        }

        [TestMethod]
        public void BloomFilterManager_MultipleServices_MaintainsAccuracy()
        {
            // Arrange
            using var manager = new BloomFilterManager();
            var config = UsdpConfiguration.Instance;
            var addedServices = new List<string>();

            // Act - Add multiple services
            for (int i = 0; i < 100; i++)
            {
                var serviceId = new ServiceIdentifier("test", $"service{i}");
                var endpoint = new TransportEndpoint
                {
                    Protocol = config.DefaultProtocol,
                    Address = config.DefaultServiceAddress,
                    Port = config.DefaultServicePort + i,
                    Security = config.DefaultSecurity
                };
                var advertisement = new ServiceAdvertisement(serviceId, endpoint)
                {
                    Metadata = new Dictionary<string, object>
                    {
                        { "index", i },
                        { "type", i % 2 == 0 ? "even" : "odd" }
                    }
                };

                manager.AddService(advertisement);
                addedServices.Add($"service{i}");
            }

            // Assert - All added services should be found
            Assert.AreEqual(100, manager.ServiceCount);

            for (int i = 0; i < 100; i++)
            {
                Assert.IsTrue(manager.MightContainService("test", $"service{i}"),
                    $"Should contain service{i}");
                Assert.IsTrue(manager.MightContainMetadata("index", i),
                    $"Should contain metadata index={i}");
            }

            // Check type metadata
            Assert.IsTrue(manager.MightContainMetadata("type", "even"));
            Assert.IsTrue(manager.MightContainMetadata("type", "odd"));
        }

        [TestMethod]
        public void BloomFilterManager_Dispose_CleansUpResources()
        {
            // Arrange
            var manager = new BloomFilterManager();
            var config = UsdpConfiguration.Instance;
            var serviceId = new ServiceIdentifier("test", "service1");
            var endpoint = new TransportEndpoint
            {
                Protocol = config.DefaultProtocol,
                Address = config.DefaultServiceAddress,
                Port = config.DefaultServicePort,
                Security = config.DefaultSecurity
            };
            var advertisement = new ServiceAdvertisement(serviceId, endpoint);

            manager.AddService(advertisement);
            Assert.AreEqual(1, manager.ServiceCount);

            // Act
            manager.Dispose();

            // Assert - Should not throw when disposed
            // Multiple dispose calls should be safe
            manager.Dispose();
        }

        [TestMethod]
        public void BloomFilterManager_LargeServiceCount_PerformsWell()
        {
            // Arrange
            var config = UsdpConfiguration.Instance;
            var originalSize = config.BloomFilterSize;
            config.BloomFilterSize = 100000; // Larger filter for this test

            try
            {
                using var manager = new BloomFilterManager();

                // Act - Add many services
                var startTime = DateTime.UtcNow;
                for (int i = 0; i < 1000; i++)
                {
                    var serviceId = new ServiceIdentifier($"namespace{i % 10}", $"service{i}");
                    var endpoint = new TransportEndpoint
                    {
                        Protocol = config.DefaultProtocol,
                        Address = config.DefaultServiceAddress,
                        Port = config.DefaultServicePort + i,
                        Security = config.DefaultSecurity
                    };
                    var advertisement = new ServiceAdvertisement(serviceId, endpoint);
                    manager.AddService(advertisement);
                }
                var addTime = DateTime.UtcNow - startTime;

                // Test query performance
                startTime = DateTime.UtcNow;
                for (int i = 0; i < 1000; i++)
                {
                    manager.MightContainService($"namespace{i % 10}", $"service{i}");
                }
                var queryTime = DateTime.UtcNow - startTime;

                // Assert
                Assert.AreEqual(1000, manager.ServiceCount);
                Assert.IsTrue(addTime.TotalSeconds < 5, $"Adding 1000 services took {addTime.TotalSeconds:F2} seconds");
                Assert.IsTrue(queryTime.TotalSeconds < 1, $"Querying 1000 services took {queryTime.TotalSeconds:F2} seconds");

                Console.WriteLine($"Performance: Add={addTime.TotalMilliseconds:F1}ms, Query={queryTime.TotalMilliseconds:F1}ms");
            }
            finally
            {
                // Restore original value
                config.BloomFilterSize = originalSize;
            }
        }
    }
}
