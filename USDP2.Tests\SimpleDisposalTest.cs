using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Threading.Tasks;
using USDP2.Tests.Mocks;

namespace USDP2.Tests
{
    /// <summary>
    /// Simple test to verify the async disposal pattern is working correctly.
    /// </summary>
    [TestClass]
    public class SimpleDisposalTest
    {
        [TestMethod]
        public async Task LocalDirectory_DisposeAsync_WorksCorrectly()
        {
            // Arrange
            var mockSender = new MockNetworkSender();
            var mockReceiver = new MockNetworkReceiver();
            var directory = new LocalDirectory(mockSender, mockReceiver, "*********", 12345);

            // Act
            await directory.DisposeAsync();

            // Assert
            Assert.IsTrue(mockSender.WasDisposed, "Sender should be disposed");
            Assert.IsTrue(mockReceiver.WasDisposed, "Receiver should be disposed");
            
            // Verify object is disposed
            Assert.ThrowsException<ObjectDisposedException>(() => directory.GetCachedAdvertisements());
        }

        [TestMethod]
        public void LocalDirectory_Dispose_WorksCorrectly()
        {
            // Arrange
            var mockSender = new MockNetworkSender();
            var mockReceiver = new MockNetworkReceiver();
            var directory = new LocalDirectory(mockSender, mockReceiver, "*********", 12345);

            // Act
            directory.Dispose();

            // Assert
            Assert.IsTrue(mockSender.WasDisposed, "Sender should be disposed");
            Assert.IsTrue(mockReceiver.WasDisposed, "Receiver should be disposed");
            
            // Verify object is disposed
            Assert.ThrowsException<ObjectDisposedException>(() => directory.GetCachedAdvertisements());
        }

        [TestMethod]
        public async Task LocalDirectory_AsyncDisposableComponents_PreferAsyncDisposal()
        {
            // Arrange
            var mockAsyncSender = new MockAsyncNetworkSender();
            var mockAsyncReceiver = new MockAsyncNetworkReceiver();
            var directory = new LocalDirectory(mockAsyncSender, mockAsyncReceiver, "*********", 12345);

            // Act
            await directory.DisposeAsync();

            // Assert
            Assert.IsTrue(mockAsyncSender.WasAsyncDisposed, "Async sender should use async disposal");
            Assert.IsTrue(mockAsyncReceiver.WasAsyncDisposed, "Async receiver should use async disposal");
            Assert.IsFalse(mockAsyncSender.WasSyncDisposed, "Async sender should not use sync disposal when async is available");
            Assert.IsFalse(mockAsyncReceiver.WasSyncDisposed, "Async receiver should not use sync disposal when async is available");
        }

        [TestMethod]
        public async Task LocalDirectory_ExceptionDuringDisposal_CollectsAllExceptions()
        {
            // Arrange
            var faultySender = new FaultyNetworkSender();
            var faultyReceiver = new FaultyNetworkReceiver();
            var directory = new LocalDirectory(faultySender, faultyReceiver, "*********", 12345);

            // Act & Assert
            var aggregateException = await Assert.ThrowsExceptionAsync<AggregateException>(async () =>
            {
                await directory.DisposeAsync();
            });

            // Verify we collected multiple exceptions
            Assert.IsTrue(aggregateException.InnerExceptions.Count >= 2, 
                $"Expected at least 2 inner exceptions, got {aggregateException.InnerExceptions.Count}");
            
            // Even with exceptions, object should be marked as disposed
            Assert.ThrowsException<ObjectDisposedException>(() => directory.GetCachedAdvertisements());
        }

        [TestMethod]
        public async Task LocalDirectory_MultipleDisposeAsync_IsSafe()
        {
            // Arrange
            var mockSender = new MockNetworkSender();
            var mockReceiver = new MockNetworkReceiver();
            var directory = new LocalDirectory(mockSender, mockReceiver, "*********", 12345);

            // Act - Multiple disposals should be safe
            await directory.DisposeAsync();
            await directory.DisposeAsync(); // Should not throw
            await directory.DisposeAsync(); // Should not throw

            // Assert
            Assert.ThrowsException<ObjectDisposedException>(() => directory.GetCachedAdvertisements());
        }

        [TestMethod]
        public async Task LocalDirectory_UsingStatement_WorksCorrectly()
        {
            MockNetworkSender? sender = null;
            MockNetworkReceiver? receiver = null;

            // Act
            await using (var directory = new LocalDirectory(
                sender = new MockNetworkSender(), 
                receiver = new MockNetworkReceiver(), 
                "*********", 
                12345))
            {
                // Use the directory
                var ads = directory.GetCachedAdvertisements();
                Assert.IsNotNull(ads);
            }
            // Directory should be automatically disposed here

            // Assert
            Assert.IsTrue(sender.WasDisposed, "Sender should be disposed after using statement");
            Assert.IsTrue(receiver.WasDisposed, "Receiver should be disposed after using statement");
        }
    }
}
