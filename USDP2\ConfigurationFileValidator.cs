using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;

namespace USDP2
{
    /// <summary>
    /// Validates configuration files for the USDP2 protocol.
    /// </summary>
    public static class ConfigurationFileValidator
    {
        // Cached JsonSerializerOptions for better performance
        private static readonly JsonSerializerOptions _validationSerializerOptions = new JsonSerializerOptions
        {
            WriteIndented = true,
            ReadCommentHandling = JsonCommentHandling.Skip,
            AllowTrailingCommas = true
        };

        /// <summary>
        /// Validates a configuration file.
        /// </summary>
        /// <param name="configPath">Path to the configuration file.</param>
        /// <returns>A list of validation results.</returns>
        public static async Task<List<ValidationResult>> ValidateConfigurationFileAsync(string configPath)
        {
            var results = new List<ValidationResult>();

            if (!File.Exists(configPath))
            {
                results.Add(new ValidationResult
                {
                    Severity = ValidationSeverity.Error,
                    PropertyName = "ConfigPath",
                    Message = $"Configuration file not found: {configPath}",
                    RecommendedAction = "Create the configuration file or specify a valid path"
                });
                return results;
            }

            try
            {
                string json = await File.ReadAllTextAsync(configPath);
                
                // Validate JSON syntax
                try
                {
                    using var document = JsonDocument.Parse(json, new JsonDocumentOptions
                    {
                        AllowTrailingCommas = true,
                        CommentHandling = JsonCommentHandling.Skip
                    });
                    
                    // JSON is valid, now validate content
                    results.AddRange(ValidateConfigurationContent(document.RootElement));
                }
                catch (JsonException ex)
                {
                    results.Add(new ValidationResult
                    {
                        Severity = ValidationSeverity.Error,
                        PropertyName = "JsonSyntax",
                        Message = $"Invalid JSON syntax: {ex.Message}",
                        RecommendedAction = "Fix the JSON syntax error in the configuration file"
                    });
                }
            }
            catch (Exception ex) when (ex is IOException or UnauthorizedAccessException)
            {
                results.Add(new ValidationResult
                {
                    Severity = ValidationSeverity.Error,
                    PropertyName = "FileAccess",
                    Message = $"Cannot access configuration file: {ex.Message}",
                    RecommendedAction = "Ensure the application has read access to the configuration file"
                });
            }

            return results;
        }

        /// <summary>
        /// Validates the content of a configuration file.
        /// </summary>
        /// <param name="root">The root element of the configuration JSON.</param>
        /// <returns>A list of validation results.</returns>
        private static List<ValidationResult> ValidateConfigurationContent(JsonElement root)
        {
            var results = new List<ValidationResult>();

            // Validate required properties
            ValidateRequiredProperties(root, results);
            
            // Validate property types and values
            ValidatePropertyTypes(root, results);
            
            // Validate property relationships
            ValidatePropertyRelationships(root, results);

            return results;
        }

        /// <summary>
        /// Validates that required properties are present in the configuration.
        /// </summary>
        /// <param name="root">The root element of the configuration JSON.</param>
        /// <param name="results">The list of validation results to add to.</param>
        private static void ValidateRequiredProperties(JsonElement root, List<ValidationResult> results)
        {
            // Define required properties
            var requiredProperties = new Dictionary<string, string>
            {
                { "DefaultServicePort", "Network port for the service" },
                { "DefaultServiceAddress", "Network address for the service" },
                // Add more required properties as needed
            };

            foreach (var property in requiredProperties)
            {
                if (!root.TryGetProperty(property.Key, out _))
                {
                    results.Add(new ValidationResult
                    {
                        Severity = ValidationSeverity.Warning,
                        PropertyName = property.Key,
                        Message = $"Required property '{property.Key}' is missing",
                        RecommendedAction = $"Add the '{property.Key}' property ({property.Value})"
                    });
                }
            }
        }

        /// <summary>
        /// Validates property types and values in the configuration.
        /// </summary>
        /// <param name="root">The root element of the configuration JSON.</param>
        /// <param name="results">The list of validation results to add to.</param>
        private static void ValidatePropertyTypes(JsonElement root, List<ValidationResult> results)
        {
            // Validate DefaultServicePort
            if (root.TryGetProperty("DefaultServicePort", out var portElement))
            {
                if (portElement.ValueKind != JsonValueKind.Number)
                {
                    results.Add(new ValidationResult
                    {
                        Severity = ValidationSeverity.Error,
                        PropertyName = "DefaultServicePort",
                        Message = "DefaultServicePort must be a number",
                        CurrentValue = portElement.ToString(),
                        RecommendedAction = "Change DefaultServicePort to a valid port number (1-65535)"
                    });
                }
                else
                {
                    int port = portElement.GetInt32();
                    if (port <= 0 || port > 65535)
                    {
                        results.Add(new ValidationResult
                        {
                            Severity = ValidationSeverity.Error,
                            PropertyName = "DefaultServicePort",
                            Message = "DefaultServicePort must be between 1 and 65535",
                            CurrentValue = port,
                            RecommendedAction = "Change DefaultServicePort to a valid port number (1-65535)"
                        });
                    }
                    else if (port < 1024)
                    {
                        results.Add(new ValidationResult
                        {
                            Severity = ValidationSeverity.Warning,
                            PropertyName = "DefaultServicePort",
                            Message = "DefaultServicePort is in the privileged port range (1-1023)",
                            CurrentValue = port,
                            RecommendedAction = "Consider using a port number above 1023 to avoid permission issues"
                        });
                    }
                }
            }

            // Validate DefaultServiceAddress
            if (root.TryGetProperty("DefaultServiceAddress", out var addressElement))
            {
                if (addressElement.ValueKind != JsonValueKind.String)
                {
                    results.Add(new ValidationResult
                    {
                        Severity = ValidationSeverity.Error,
                        PropertyName = "DefaultServiceAddress",
                        Message = "DefaultServiceAddress must be a string",
                        CurrentValue = addressElement.ToString(),
                        RecommendedAction = "Change DefaultServiceAddress to a valid IP address or hostname"
                    });
                }
                else
                {
                    string address = addressElement.GetString() ?? string.Empty;
                    if (string.IsNullOrWhiteSpace(address))
                    {
                        results.Add(new ValidationResult
                        {
                            Severity = ValidationSeverity.Error,
                            PropertyName = "DefaultServiceAddress",
                            Message = "DefaultServiceAddress cannot be empty",
                            RecommendedAction = "Specify a valid IP address or hostname for DefaultServiceAddress"
                        });
                    }
                }
            }

            // Add more property validations as needed
        }

        /// <summary>
        /// Validates relationships between properties in the configuration.
        /// </summary>
        /// <param name="root">The root element of the configuration JSON.</param>
        /// <param name="results">The list of validation results to add to.</param>
        private static void ValidatePropertyRelationships(JsonElement root, List<ValidationResult> results)
        {
            // Example: Validate that HTTP and HTTPS ports are different
            if (root.TryGetProperty("DefaultHttpPort", out var httpPortElement) &&
                root.TryGetProperty("DefaultHttpsPort", out var httpsPortElement) &&
                root.TryGetProperty("UseHttps", out var useHttpsElement))
            {
                if (httpPortElement.ValueKind == JsonValueKind.Number &&
                    httpsPortElement.ValueKind == JsonValueKind.Number &&
                    useHttpsElement.ValueKind == JsonValueKind.True)
                {
                    int httpPort = httpPortElement.GetInt32();
                    int httpsPort = httpsPortElement.GetInt32();

                    if (httpPort == httpsPort)
                    {
                        results.Add(new ValidationResult
                        {
                            Severity = ValidationSeverity.Warning,
                            PropertyName = "DefaultHttpsPort",
                            Message = "HTTP and HTTPS ports are the same",
                            CurrentValue = $"HTTP: {httpPort}, HTTPS: {httpsPort}",
                            RecommendedAction = "Use different ports for HTTP and HTTPS (e.g., 8080 and 8443)"
                        });
                    }
                }
            }

            // Add more relationship validations as needed
        }
    }
}