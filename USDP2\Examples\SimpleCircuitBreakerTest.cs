using System;
using System.Threading;
using System.Threading.Tasks;

namespace USDP2.Examples
{
    /// <summary>
    /// Simple test to verify the circuit breaker implementation is working correctly.
    /// </summary>
    public static class SimpleCircuitBreakerTest
    {
        /// <summary>
        /// Runs a basic test of the circuit breaker implementation.
        /// </summary>
        public static async Task RunAsync()
        {
            Console.WriteLine("=== Simple Circuit Breaker Test ===");
            Console.WriteLine();

            try
            {
                // Test 1: Configure circuit breaker settings
                Console.WriteLine("1. Configuring Circuit Breaker Settings...");
                UsdpConfiguration.Instance.EnableCircuitBreakers = true;
                UsdpConfiguration.Instance.DefaultCircuitBreakerFailureThreshold = 2; // Fail after 2 attempts for quick testing
                UsdpConfiguration.Instance.DefaultCircuitBreakerOpenTimeout = TimeSpan.FromSeconds(5); // Short timeout for testing
                UsdpConfiguration.Instance.DefaultCircuitBreakerSuccessThreshold = 1;
                Console.WriteLine("✓ Circuit breaker settings configured");

                // Test 2: Test CoapToHttpTranslator circuit breaker
                Console.WriteLine("\n2. Testing CoapToHttpTranslator Circuit Breaker...");
                await TestCoapToHttpCircuitBreakerAsync();

                // Test 3: Test HttpNetworkSender circuit breaker
                Console.WriteLine("\n3. Testing HttpNetworkSender Circuit Breaker...");
                await TestHttpNetworkSenderCircuitBreakerAsync();

                // Test 4: Test UdpNetworkSender circuit breaker
                Console.WriteLine("\n4. Testing UdpNetworkSender Circuit Breaker...");
                await TestUdpNetworkSenderCircuitBreakerAsync();

                // Test 5: Test circuit breaker statistics
                Console.WriteLine("\n5. Testing Circuit Breaker Statistics...");
                await TestCircuitBreakerStatisticsAsync();

                Console.WriteLine("\n=== All Circuit Breaker Tests Completed Successfully ===");
                Console.WriteLine("✅ Circuit Breaker implementation is working correctly!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"\n❌ Test failed: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                throw;
            }
        }

        /// <summary>
        /// Tests CoapToHttpTranslator circuit breaker functionality.
        /// </summary>
        private static async Task TestCoapToHttpCircuitBreakerAsync()
        {
            using var translator = new CoapToHttpTranslator();
            var testData = System.Text.Encoding.UTF8.GetBytes("Test CoAP data");
            var unreachableAddress = "***************"; // Non-routable address
            var unreachablePort = 9999;

            Console.WriteLine($"  Testing with unreachable endpoint: {unreachableAddress}:{unreachablePort}");

            for (int i = 1; i <= 4; i++)
            {
                try
                {
                    Console.WriteLine($"    Attempt {i}...");
                    using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(1));
                    await translator.TranslateAsync(testData, "coap+udp", "http", unreachableAddress, unreachablePort, cts.Token);
                    Console.WriteLine($"      ✅ Success (unexpected)");
                }
                catch (CircuitBreakerOpenException)
                {
                    Console.WriteLine($"      🔴 Circuit breaker is OPEN - test successful!");
                    break;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"      ❌ Failed: {ex.GetType().Name}");
                }

                var circuitBreaker = translator.GetCircuitBreaker(unreachableAddress, unreachablePort);
                if (circuitBreaker != null)
                {
                    Console.WriteLine($"      Circuit state: {circuitBreaker.State}");
                }
            }

            Console.WriteLine("  ✓ CoapToHttpTranslator circuit breaker test completed");
        }

        /// <summary>
        /// Tests HttpNetworkSender circuit breaker functionality.
        /// </summary>
        private static async Task TestHttpNetworkSenderCircuitBreakerAsync()
        {
            using var httpSender = new HttpNetworkSender();
            var testData = System.Text.Encoding.UTF8.GetBytes("Test HTTP data");
            var unreachableAddress = "**************"; // Non-routable address
            var unreachablePort = 8888;

            Console.WriteLine($"  Testing with unreachable endpoint: {unreachableAddress}:{unreachablePort}");

            for (int i = 1; i <= 4; i++)
            {
                try
                {
                    Console.WriteLine($"    Attempt {i}...");
                    using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(1));
                    await httpSender.SendAsync(testData, unreachableAddress, unreachablePort, cts.Token);
                    Console.WriteLine($"      ✅ Success (unexpected)");
                }
                catch (CircuitBreakerOpenException)
                {
                    Console.WriteLine($"      🔴 Circuit breaker is OPEN - test successful!");
                    break;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"      ❌ Failed: {ex.GetType().Name}");
                }

                var circuitBreaker = httpSender.GetCircuitBreaker(unreachableAddress, unreachablePort);
                if (circuitBreaker != null)
                {
                    Console.WriteLine($"      Circuit state: {circuitBreaker.State}");
                }
            }

            Console.WriteLine("  ✓ HttpNetworkSender circuit breaker test completed");
        }

        /// <summary>
        /// Tests UdpNetworkSender circuit breaker functionality.
        /// </summary>
        private static async Task TestUdpNetworkSenderCircuitBreakerAsync()
        {
            using var udpSender = new UdpNetworkSender();
            var testData = System.Text.Encoding.UTF8.GetBytes("Test UDP data");
            var unreachableAddress = "**************"; // Non-routable address
            var unreachablePort = 7777;

            Console.WriteLine($"  Testing with unreachable endpoint: {unreachableAddress}:{unreachablePort}");

            // Note: UDP typically doesn't fail in the same way as TCP/HTTP, 
            // but the circuit breaker still provides protection
            for (int i = 1; i <= 3; i++)
            {
                try
                {
                    Console.WriteLine($"    Attempt {i}...");
                    await udpSender.SendAsync(testData, unreachableAddress, unreachablePort);
                    Console.WriteLine($"      ✅ Success (UDP typically succeeds even to unreachable addresses)");
                }
                catch (CircuitBreakerOpenException)
                {
                    Console.WriteLine($"      🔴 Circuit breaker is OPEN - test successful!");
                    break;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"      ❌ Failed: {ex.GetType().Name}");
                }

                var circuitBreaker = udpSender.GetCircuitBreaker(unreachableAddress, unreachablePort);
                if (circuitBreaker != null)
                {
                    Console.WriteLine($"      Circuit state: {circuitBreaker.State}");
                }
            }

            Console.WriteLine("  ✓ UdpNetworkSender circuit breaker test completed");
        }

        /// <summary>
        /// Tests circuit breaker statistics functionality.
        /// </summary>
        private static async Task TestCircuitBreakerStatisticsAsync()
        {
            using var httpSender = new HttpNetworkSender();
            var testData = System.Text.Encoding.UTF8.GetBytes("Test data");

            // Create some test traffic to generate statistics
            var testAddress = "127.0.0.1";
            var testPort = 9876;

            Console.WriteLine("  Generating test traffic for statistics...");

            for (int i = 0; i < 3; i++)
            {
                try
                {
                    using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(500));
                    await httpSender.SendAsync(testData, testAddress, testPort, cts.Token);
                }
                catch
                {
                    // Expected to fail, we're just generating statistics
                }
            }

            // Get and display statistics
            var statistics = httpSender.GetCircuitBreakerStatistics();
            Console.WriteLine($"  Circuit breaker statistics collected: {statistics.Count} endpoints");

            foreach (var kvp in statistics)
            {
                var stats = kvp.Value;
                Console.WriteLine($"    {kvp.Key}: {stats.State}, {stats.TotalOperations} operations, {(100 - stats.FailureRate):F1}% success");
            }

            // Test global circuit breaker health
            var healthSummary = CircuitBreakerManager.Instance.GetHealthSummary();
            Console.WriteLine($"  Global health: {healthSummary.TotalCircuitBreakers} total, {healthSummary.OpenCircuits} open");

            Console.WriteLine("  ✓ Circuit breaker statistics test completed");
        }
    }
}
