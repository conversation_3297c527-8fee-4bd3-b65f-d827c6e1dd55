using System;

namespace USDP2
{
    /// <summary>
    /// Represents a hierarchical, human-readable service identifier with a UUIDv7 for uniqueness.
    /// </summary>
    public sealed class ServiceIdentifier
    {
        /// <summary>
        /// Gets the namespace.
        /// </summary>
        /// <value>A <see cref="string"/></value>
        public string Namespace { get; set; } = string.Empty;
        /// <summary>
        /// Gets the name.
        /// </summary>
        /// <value>A <see cref="string"/></value>
        public string Name { get; set; } = string.Empty;
        /// <summary>
        /// Gets the uuid.
        /// </summary>
        /// <value>A <see cref="Guid"/></value>
        public Guid Uuid { get; set; }

        /// <summary>
        /// Gets the full name.
        /// </summary>
        /// <value>A <see cref="string"/></value>
        public string FullName => $"{Namespace}/{Name}";

        /// <summary>
        /// Initializes a new instance of the <see cref="ServiceIdentifier"/> class.
        /// </summary>
        /// <param name="ns">The ns.</param>
        /// <param name="name">The name.</param>
        /// <param name="uuid">The uuid.</param>
        public ServiceIdentifier(string ns, string name, Guid? uuid = null)
        {
            Namespace = ns ?? throw new ArgumentNullException(nameof(ns));
            Name = name ?? throw new ArgumentNullException(nameof(name));
            Uuid = uuid ?? Guid.NewGuid();
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="ServiceIdentifier"/> class for serialization.
        /// </summary>
        public ServiceIdentifier()
        {
            // Properties will be set by the deserializer
        }

        /// <summary>
        /// Converts to the string.
        /// </summary>
        /// <returns>A <see cref="string"/></returns>
        public override string ToString() => $"{FullName} ({Uuid})";

        // Delegated serialization/deserialization
        /// <summary>
        /// Converts to the json.
        /// </summary>
        /// <returns>A <see cref="string"/></returns>
        public string ToJson() => USDPSerializer.ToJson(this);
        /// <summary>
        /// Converts to the cbor.
        /// </summary>
        /// <returns>An array of bytes</returns>
        public byte[] ToCbor() => USDPSerializer.ToCbor(this);

        /// <summary>
        /// Froms the json.
        /// </summary>
        /// <param name="json">The json.</param>
        /// <returns>A <see cref="ServiceIdentifier"/></returns>
        public static ServiceIdentifier? FromJson(string json) => USDPSerializer.FromJson<ServiceIdentifier>(json);
        /// <summary>
        /// Froms the cbor.
        /// </summary>
        /// <param name="cbor">The cbor.</param>
        /// <returns>A <see cref="ServiceIdentifier"/></returns>
        public static ServiceIdentifier? FromCbor(byte[] cbor) => USDPSerializer.FromCbor<ServiceIdentifier>(cbor);
    }
}