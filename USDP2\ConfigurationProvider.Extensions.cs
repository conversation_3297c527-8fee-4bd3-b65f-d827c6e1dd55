using System;
using System.Collections.Generic;
using System.Text.Json;
using System.Threading.Tasks;

namespace USDP2
{
    /// <summary>
    /// Extension methods for the ConfigurationProvider class.
    /// </summary>
    public static class ConfigurationProviderExtensions
    {
        // Cached JsonSerializerOptions for better performance
        private static readonly JsonSerializerOptions _defaultSerializerOptions = new JsonSerializerOptions
        {
            WriteIndented = true
        };

        /// <summary>
        /// Loads configuration from a file and applies it to the UsdpConfiguration instance.
        /// </summary>
        /// <param name="provider">The configuration provider.</param>
        /// <param name="configInstance">Optional UsdpConfiguration instance to update. If null, uses UsdpConfiguration.Instance.</param>
        /// <returns>A task representing the asynchronous operation.</returns>
        public static async Task LoadAndApplyToUsdpConfigurationAsync(
            this ConfigurationProvider provider, 
            UsdpConfiguration? configInstance = null)
        {
            await provider.LoadAsync();
            var config = configInstance ?? UsdpConfiguration.Instance;
            
            // Apply loaded configuration to UsdpConfiguration properties
            // This allows for runtime configuration changes
            ApplyConfigurationToUsdp(provider, config);
        }

        /// <summary>
        /// Applies the configuration values from the provider to the UsdpConfiguration instance.
        /// </summary>
        /// <param name="provider">The configuration provider.</param>
        /// <param name="config">The UsdpConfiguration instance to update.</param>
        private static void ApplyConfigurationToUsdp(ConfigurationProvider provider, UsdpConfiguration config)
        {
            // Example of applying configuration values
            // Add more properties as needed
            
            // Network settings
            var defaultServicePort = provider.GetValue<int>("DefaultServicePort", config.DefaultServicePort);
            if (defaultServicePort > 0 && defaultServicePort < 65536)
            {
                config.DefaultServicePort = defaultServicePort;
            }
            
            var defaultServiceAddress = provider.GetValue<string>("DefaultServiceAddress", config.DefaultServiceAddress);
            if (!string.IsNullOrEmpty(defaultServiceAddress))
            {
                config.DefaultServiceAddress = defaultServiceAddress;
            }
            
            // Timeout settings
            var networkTimeoutMs = provider.GetValue<int>("NetworkTimeoutMs", (int)config.NetworkTimeout.TotalMilliseconds);
            if (networkTimeoutMs > 0)
            {
                config.NetworkTimeout = TimeSpan.FromMilliseconds(networkTimeoutMs);
            }
            
            // Logging settings
            var minLogLevel = provider.GetValue<string>("MinimumLogLevel", config.MinimumLogLevel.ToString());
            if (Enum.TryParse<Microsoft.Extensions.Logging.LogLevel>(minLogLevel, out var logLevel))
            {
                config.MinimumLogLevel = logLevel;
            }
            
            var logMode = provider.GetValue<string>("LogMode", config.LogMode.ToString());
            if (Enum.TryParse<UsdpConfiguration.LoggingMode>(logMode, out var mode))
            {
                config.LogMode = mode;
            }
            
            // Security settings
            var useHttps = provider.GetValue<bool>("UseHttps", config.UseHttps);
            config.UseHttps = useHttps;
            
            // Apply other configuration properties as needed
        }
        
        /// <summary>
        /// Saves the current UsdpConfiguration instance to the configuration provider.
        /// </summary>
        /// <param name="provider">The configuration provider.</param>
        /// <param name="configInstance">Optional UsdpConfiguration instance to save. If null, uses UsdpConfiguration.Instance.</param>
        /// <returns>A task representing the asynchronous operation.</returns>
        public static async Task SaveFromUsdpConfigurationAsync(
            this ConfigurationProvider provider, 
            UsdpConfiguration? configInstance = null)
        {
            var config = configInstance ?? UsdpConfiguration.Instance;
            
            // Save UsdpConfiguration properties to the provider
            SaveUsdpToConfiguration(config, provider);
            
            await provider.SaveAsync();
        }
        
        /// <summary>
        /// Saves the UsdpConfiguration values to the configuration provider.
        /// </summary>
        /// <param name="config">The UsdpConfiguration instance to save.</param>
        /// <param name="provider">The configuration provider to save to.</param>
        private static void SaveUsdpToConfiguration(UsdpConfiguration config, ConfigurationProvider provider)
        {
            // Example of saving configuration values
            // Add more properties as needed
            
            // Network settings
            provider.SetValue("DefaultServicePort", config.DefaultServicePort);
            provider.SetValue("DefaultServiceAddress", config.DefaultServiceAddress);
            
            // Timeout settings
            provider.SetValue("NetworkTimeoutMs", (int)config.NetworkTimeout.TotalMilliseconds);
            
            // Logging settings
            provider.SetValue("MinimumLogLevel", config.MinimumLogLevel.ToString());
            provider.SetValue("LogMode", config.LogMode.ToString());
            
            // Security settings
            provider.SetValue("UseHttps", config.UseHttps);
            
            // Save other configuration properties as needed
        }
    }
}