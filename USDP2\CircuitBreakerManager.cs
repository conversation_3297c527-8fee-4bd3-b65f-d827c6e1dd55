using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace USDP2
{
    /// <summary>
    /// Manages multiple circuit breakers for different network operations and endpoints.
    /// 
    /// This class provides centralized management of circuit breakers with:
    /// - Automatic circuit breaker creation and configuration
    /// - Health monitoring and statistics collection
    /// - Bulk operations for system-wide circuit breaker management
    /// - Integration with USDP2 configuration system
    /// </summary>
    public class CircuitBreakerManager : IDisposable
    {
        private readonly ConcurrentDictionary<string, CircuitBreaker> _circuitBreakers = new();
        private readonly Timer _healthCheckTimer;
        private readonly object _lock = new object();
        private bool _disposed;

        /// <summary>
        /// Singleton instance of the circuit breaker manager.
        /// </summary>
        public static CircuitBreakerManager Instance { get; } = new CircuitBreakerManager();

        /// <summary>
        /// Initializes a new instance of the CircuitBreakerManager class.
        /// </summary>
        private CircuitBreakerManager()
        {
            // Start health check timer to periodically log circuit breaker status
            _healthCheckTimer = new Timer(PerformHealthCheck, null, TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));
        }

        /// <summary>
        /// Gets or creates a circuit breaker for the specified operation.
        /// </summary>
        /// <param name="name">Unique name for the circuit breaker.</param>
        /// <param name="options">Optional configuration options. If null, uses default configuration.</param>
        /// <returns>A circuit breaker instance for the specified operation.</returns>
        public CircuitBreaker GetOrCreateCircuitBreaker(string name, CircuitBreakerOptions? options = null)
        {
            if (string.IsNullOrWhiteSpace(name))
                throw new ArgumentException("Circuit breaker name cannot be null or empty", nameof(name));

            return _circuitBreakers.GetOrAdd(name, _ =>
            {
                var circuitBreaker = new CircuitBreaker(name, options ?? CreateDefaultOptions(name));

                UsdpLogger.Log("CircuitBreaker.Created", new
                {
                    Name = name,
                    FailureThreshold = circuitBreaker.GetStatistics().FailureCount,
                    OpenTimeout = options?.OpenTimeout.TotalSeconds ?? 30
                });

                return circuitBreaker;
            });
        }

        /// <summary>
        /// Gets a circuit breaker by name if it exists.
        /// </summary>
        /// <param name="name">Name of the circuit breaker.</param>
        /// <returns>The circuit breaker if found, null otherwise.</returns>
        public CircuitBreaker? GetCircuitBreaker(string name)
        {
            return _circuitBreakers.TryGetValue(name, out var circuitBreaker) ? circuitBreaker : null;
        }

        /// <summary>
        /// Gets all circuit breakers currently managed by this instance.
        /// </summary>
        /// <returns>A dictionary of circuit breaker names and instances.</returns>
        public IReadOnlyDictionary<string, CircuitBreaker> GetAllCircuitBreakers()
        {
            return _circuitBreakers.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
        }

        /// <summary>
        /// Gets comprehensive health statistics for all circuit breakers.
        /// </summary>
        /// <returns>A dictionary of circuit breaker names and their statistics.</returns>
        public Dictionary<string, CircuitBreakerStatistics> GetHealthStatistics()
        {
            var statistics = new Dictionary<string, CircuitBreakerStatistics>();

            foreach (var kvp in _circuitBreakers)
            {
                try
                {
                    statistics[kvp.Key] = kvp.Value.GetStatistics();
                }
                catch (Exception ex)
                {
                    UsdpLogger.Log("CircuitBreaker.StatisticsError", new
                    {
                        Name = kvp.Key,
                        Error = ex.Message
                    });
                }
            }

            return statistics;
        }

        /// <summary>
        /// Removes a circuit breaker from management.
        /// </summary>
        /// <param name="name">Name of the circuit breaker to remove.</param>
        /// <returns>True if the circuit breaker was removed, false if it didn't exist.</returns>
        public bool RemoveCircuitBreaker(string name)
        {
            if (_circuitBreakers.TryRemove(name, out var circuitBreaker))
            {
                UsdpLogger.Log("CircuitBreaker.Removed", new { Name = name });
                return true;
            }
            return false;
        }

        /// <summary>
        /// Creates default circuit breaker options based on the operation name and USDP configuration.
        /// </summary>
        /// <param name="name">Name of the circuit breaker operation.</param>
        /// <returns>Configured circuit breaker options.</returns>
        private static CircuitBreakerOptions CreateDefaultOptions(string name)
        {
            var config = UsdpConfiguration.Instance;

            // Customize options based on operation type
            var options = new CircuitBreakerOptions();

            if (name.Contains("Http", StringComparison.OrdinalIgnoreCase))
            {
                // HTTP operations - more lenient due to network variability
                options.FailureThreshold = 5;
                options.OpenTimeout = TimeSpan.FromSeconds(30);
                options.OperationTimeout = config.NetworkTimeout;
                options.SuccessThreshold = 3;
            }
            else if (name.Contains("Udp", StringComparison.OrdinalIgnoreCase))
            {
                // UDP operations - faster failure detection
                options.FailureThreshold = 3;
                options.OpenTimeout = TimeSpan.FromSeconds(15);
                options.OperationTimeout = TimeSpan.FromSeconds(5);
                options.SuccessThreshold = 2;
            }
            else if (name.Contains("Tcp", StringComparison.OrdinalIgnoreCase))
            {
                // TCP operations - moderate settings
                options.FailureThreshold = 4;
                options.OpenTimeout = TimeSpan.FromSeconds(20);
                options.OperationTimeout = config.NetworkTimeout;
                options.SuccessThreshold = 2;
            }
            else if (name.Contains("Mdns", StringComparison.OrdinalIgnoreCase))
            {
                // mDNS operations - very lenient due to local network nature
                options.FailureThreshold = 7;
                options.OpenTimeout = TimeSpan.FromSeconds(45);
                options.OperationTimeout = TimeSpan.FromSeconds(10);
                options.SuccessThreshold = 3;
            }
            else if (name.Contains("Stun", StringComparison.OrdinalIgnoreCase))
            {
                // STUN operations - external dependency, more lenient
                options.FailureThreshold = 6;
                options.OpenTimeout = TimeSpan.FromMinutes(1);
                options.OperationTimeout = TimeSpan.FromSeconds(15);
                options.SuccessThreshold = 2;
            }
            else
            {
                // Default settings for unknown operation types
                options.FailureThreshold = 5;
                options.OpenTimeout = TimeSpan.FromSeconds(30);
                options.OperationTimeout = config.NetworkTimeout;
                options.SuccessThreshold = 3;
            }

            // Customize exception handling based on operation type
            options.ShouldHandleException = ex => ex switch
            {
                OperationCanceledException => false, // Don't count cancellations as failures
                TimeoutException => true,            // Count timeouts as failures
                System.Net.Http.HttpRequestException => true, // Count HTTP errors as failures
                System.Net.Sockets.SocketException => true,   // Count socket errors as failures
                _ => true // Count other exceptions as failures by default
            };

            return options;
        }

        /// <summary>
        /// Performs periodic health checks and logging for all circuit breakers.
        /// </summary>
        private void PerformHealthCheck(object? state)
        {
            if (_disposed) return;

            try
            {
                var statistics = GetHealthStatistics();
                var openCircuits = statistics.Where(kvp => kvp.Value.State == CircuitBreakerState.Open).ToList();
                var halfOpenCircuits = statistics.Where(kvp => kvp.Value.State == CircuitBreakerState.HalfOpen).ToList();

                // Log overall health summary
                UsdpLogger.Log("CircuitBreaker.HealthCheck", new
                {
                    TotalCircuitBreakers = statistics.Count,
                    OpenCircuits = openCircuits.Count,
                    HalfOpenCircuits = halfOpenCircuits.Count,
                    ClosedCircuits = statistics.Count - openCircuits.Count - halfOpenCircuits.Count
                });

                // Log details for problematic circuits
                foreach (var openCircuit in openCircuits)
                {
                    UsdpLogger.Log("CircuitBreaker.OpenCircuitAlert", new
                    {
                        Name = openCircuit.Key,
                        State = openCircuit.Value.State,
                        FailureCount = openCircuit.Value.FailureCount,
                        TimeUntilRetry = openCircuit.Value.TimeUntilRetry.TotalSeconds,
                        FailureRate = openCircuit.Value.FailureRate,
                        LastFailureTime = openCircuit.Value.LastFailureTime
                    });
                }

                foreach (var halfOpenCircuit in halfOpenCircuits)
                {
                    UsdpLogger.Log("CircuitBreaker.HalfOpenCircuitStatus", new
                    {
                        Name = halfOpenCircuit.Key,
                        State = halfOpenCircuit.Value.State,
                        SuccessCount = halfOpenCircuit.Value.SuccessCount,
                        FailureRate = halfOpenCircuit.Value.FailureRate
                    });
                }
            }
            catch (Exception ex)
            {
                UsdpLogger.Log("CircuitBreaker.HealthCheckError", new
                {
                    Error = ex.Message,
                    ExceptionType = ex.GetType().Name
                });
            }
        }

        /// <summary>
        /// Gets a summary of system-wide circuit breaker health.
        /// </summary>
        /// <returns>Health summary information.</returns>
        public CircuitBreakerHealthSummary GetHealthSummary()
        {
            var statistics = GetHealthStatistics();

            return new CircuitBreakerHealthSummary
            {
                TotalCircuitBreakers = statistics.Count,
                ClosedCircuits = statistics.Count(kvp => kvp.Value.State == CircuitBreakerState.Closed),
                OpenCircuits = statistics.Count(kvp => kvp.Value.State == CircuitBreakerState.Open),
                HalfOpenCircuits = statistics.Count(kvp => kvp.Value.State == CircuitBreakerState.HalfOpen),
                TotalOperations = statistics.Values.Sum(s => s.TotalOperations),
                TotalFailures = statistics.Values.Sum(s => s.TotalFailures),
                OverallFailureRate = statistics.Values.Sum(s => s.TotalOperations) > 0
                    ? (double)statistics.Values.Sum(s => s.TotalFailures) / statistics.Values.Sum(s => s.TotalOperations)
                    : 0,
                MostProblematicCircuits = statistics
                    .Where(kvp => kvp.Value.TotalOperations > 0)
                    .OrderByDescending(kvp => kvp.Value.FailureRate)
                    .Take(5)
                    .ToDictionary(kvp => kvp.Key, kvp => kvp.Value.FailureRate)
            };
        }

        /// <summary>
        /// Disposes the circuit breaker manager and stops health monitoring.
        /// </summary>
        public void Dispose()
        {
            if (!_disposed)
            {
                _healthCheckTimer?.Dispose();
                _disposed = true;

                UsdpLogger.Log("CircuitBreaker.ManagerDisposed", new
                {
                    TotalCircuitBreakers = _circuitBreakers.Count
                });
            }
            GC.SuppressFinalize(this);
        }
    }

    /// <summary>
    /// Summary of circuit breaker health across the system.
    /// </summary>
    public class CircuitBreakerHealthSummary
    {
        public int TotalCircuitBreakers { get; set; }
        public int ClosedCircuits { get; set; }
        public int OpenCircuits { get; set; }
        public int HalfOpenCircuits { get; set; }
        public long TotalOperations { get; set; }
        public long TotalFailures { get; set; }
        public double OverallFailureRate { get; set; }
        public Dictionary<string, double> MostProblematicCircuits { get; set; } = new();
    }
}
