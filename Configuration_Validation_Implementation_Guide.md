# Configuration Validation Implementation Guide

## Overview

The USDP2 project now includes a comprehensive configuration validation system that validates all configuration parameters at startup to prevent runtime failures and provide clear error messages for misconfigurations.

## 🎯 Benefits

### **Startup Reliability**
- **Prevents runtime failures** due to invalid configuration values
- **Early error detection** before services start
- **Clear error messages** with specific recommendations
- **Graceful startup failure** with detailed diagnostics

### **Developer Experience**
- **Immediate feedback** on configuration issues
- **Specific recommendations** for fixing problems
- **Comprehensive validation** of all configuration aspects
- **Environment-specific validation** rules

### **Operations & Monitoring**
- **Validation reports** for system health monitoring
- **Startup recommendations** for optimal configuration
- **Configuration snapshots** for audit trails
- **Configurable validation behavior** for different environments

## 🏗️ Architecture

### **Core Components**

#### **ValidateConfiguration Class**
- **Comprehensive validation** of all UsdpConfiguration properties
- **Severity-based results** (Critical, Error, Warning, Info)
- **Cross-property validation** for dependencies and conflicts
- **Detailed recommendations** for fixing issues

#### **StartupConfigurationValidator Class**
- **Startup integration** with configurable behavior
- **Custom validation rules** support
- **Validation reporting** and health monitoring
- **Environment-specific recommendations**

#### **ValidationResult Class**
- **Structured validation results** with severity levels
- **Property-specific feedback** with current values
- **Actionable recommendations** for issue resolution
- **Integration-friendly format** for logging and monitoring

## 🔧 Validation Categories

### **Network Configuration**
- **Multicast addresses** - IPv4 multicast range validation
- **Service addresses** - IP address format and accessibility
- **Port numbers** - Valid ranges and conflict detection
- **Endpoint paths** - URL path format and character validation
- **Port conflicts** - Ensures unique ports for different services

### **Timeout Configuration**
- **Network timeouts** - Reasonable ranges for network operations
- **Query timeouts** - Response timeout validation
- **TTL values** - Time-to-live range and relationship validation
- **Circuit breaker timeouts** - Resilience configuration validation
- **Cross-timeout relationships** - Logical timeout dependencies

### **Security Configuration**
- **Security protocols** - Valid protocol strings and recommendations
- **Authentication consistency** - Auth requirements vs security settings
- **HTTPS configuration** - SSL/TLS setup validation
- **TLS settings** - Protocol version and fallback validation

### **File Path Configuration**
- **Configuration paths** - File and directory existence validation
- **Log file paths** - Write permissions and directory validation
- **File name validation** - Invalid character detection
- **Path accessibility** - Runtime accessibility checks

### **Buffer and Limit Configuration**
- **Buffer sizes** - Memory allocation validation
- **Size relationships** - Default vs maximum size consistency
- **Circuit breaker thresholds** - Failure and success threshold validation
- **Performance recommendations** - Optimal size suggestions

### **Service Configuration**
- **Service identifiers** - DNS naming convention validation
- **Metadata configuration** - Service discovery metadata validation
- **Protocol compatibility** - Service protocol consistency

### **Logging Configuration**
- **Log modes** - Platform-specific logging validation
- **Event log names** - Windows event log validation
- **Syslog configuration** - Unix syslog setup validation
- **Log path validation** - File system permissions

### **Circuit Breaker Configuration**
- **Feature enablement** - Circuit breaker configuration validation
- **Monitoring settings** - Health monitoring configuration
- **Threshold validation** - Failure and success thresholds

### **Cross-Property Dependencies**
- **Timeout relationships** - Query vs network timeout consistency
- **Security consistency** - Authentication vs security protocol alignment
- **Service compatibility** - Protocol and security compatibility
- **Environment-specific rules** - Production vs development validation

## 🚀 Usage Examples

### **Basic Startup Validation**

```csharp
// Validate configuration at application startup
try
{
    StartupConfigurationValidator.ValidateAtStartup();
    Console.WriteLine("✓ Configuration validation passed");
}
catch (InvalidOperationException ex)
{
    Console.WriteLine($"✗ Configuration validation failed: {ex.Message}");
    Environment.Exit(1);
}
```

### **Validation with Different Behaviors**

```csharp
// Strict validation (fail on any errors)
await StartupConfigurationValidator.ValidateAtStartupAsync(
    null, 
    StartupConfigurationValidator.ValidationBehavior.Strict);

// Lenient validation (fail only on critical errors)
await StartupConfigurationValidator.ValidateAtStartupAsync(
    null, 
    StartupConfigurationValidator.ValidationBehavior.Lenient);

// Log-only validation (never fail startup)
await StartupConfigurationValidator.ValidateAtStartupAsync(
    null, 
    StartupConfigurationValidator.ValidationBehavior.LogOnly);
```

### **Custom Validation Rules**

```csharp
// Define custom business rules
Func<UsdpConfiguration, List<ValidationResult>> customRules = config =>
{
    var results = new List<ValidationResult>();
    
    // Custom rule: Production must use HTTPS
    if (Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") == "Production" 
        && !config.UseHttps)
    {
        results.Add(new ValidationResult
        {
            Severity = ValidationSeverity.Error,
            PropertyName = nameof(config.UseHttps),
            Message = "Production environment must use HTTPS",
            RecommendedAction = "Enable HTTPS for production deployments"
        });
    }
    
    return results;
};

// Apply custom validation
await StartupConfigurationValidator.ValidateAtStartupAsync(
    null, 
    StartupConfigurationValidator.ValidationBehavior.Strict,
    customRules);
```

### **Validation Reporting**

```csharp
// Generate comprehensive validation report
var report = StartupConfigurationValidator.CreateValidationReport();

Console.WriteLine($"Validation Status: {(report.IsValid ? "VALID" : "INVALID")}");
Console.WriteLine($"Total Issues: {report.TotalIssues}");
Console.WriteLine($"Critical: {report.CriticalIssues}");
Console.WriteLine($"Errors: {report.ErrorIssues}");
Console.WriteLine($"Warnings: {report.WarningIssues}");

// Get startup recommendations
var recommendations = StartupConfigurationValidator.GetStartupRecommendations();
foreach (var rec in recommendations.SecurityRecommendations)
{
    Console.WriteLine($"Security: {rec}");
}
```

### **Manual Validation**

```csharp
// Validate specific configuration
var results = ValidateConfiguration.ValidateAll(UsdpConfiguration.Instance);

// Filter by severity
var criticalIssues = results.Where(r => r.Severity == ValidationSeverity.Critical);
var errors = results.Where(r => r.Severity == ValidationSeverity.Error);

// Log results
ValidateConfiguration.LogValidationResults(results);

// Throw on errors
ValidateConfiguration.ValidateAndThrow(UsdpConfiguration.Instance);
```

## 📊 Validation Severity Levels

### **Critical (Application Cannot Start)**
- Invalid IP address formats
- Port numbers outside valid range (1-65535)
- Null or empty required configuration values
- Invalid file paths that prevent startup

### **Error (Application May Fail at Runtime)**
- Port conflicts between services
- Authentication required but security disabled
- Buffer size conflicts (default > maximum)
- TTL relationship violations (default > maximum)

### **Warning (Suboptimal Configuration)**
- Well-known ports requiring elevated privileges
- Security disabled in production environments
- HTTPS enabled but security set to 'none'
- Timeout relationships that may cause issues

### **Info (Valid but Notable Configuration)**
- Localhost addresses limiting network access
- Circuit breakers disabled reducing resilience
- Custom event log names requiring registration
- Valid configuration with optimization suggestions

## 🔄 Integration Patterns

### **ASP.NET Core Startup**

```csharp
public class Startup
{
    public void ConfigureServices(IServiceCollection services)
    {
        // Validate configuration before registering services
        StartupConfigurationValidator.ValidateAtStartup(
            null, 
            StartupConfigurationValidator.ValidationBehavior.Strict);
        
        // Register services...
    }
}
```

### **Console Application**

```csharp
class Program
{
    static async Task Main(string[] args)
    {
        try
        {
            // Validate configuration first
            await StartupConfigurationValidator.ValidateAtStartupAsync();
            
            // Start application...
        }
        catch (InvalidOperationException ex)
        {
            Console.WriteLine($"Configuration Error: {ex.Message}");
            Environment.Exit(1);
        }
    }
}
```

### **Dependency Injection**

```csharp
// Register validation as a service
services.AddSingleton<IConfigurationValidator>(provider =>
{
    return new ConfigurationValidator(
        StartupConfigurationValidator.ValidationBehavior.Lenient);
});

// Use in hosted service
public class ValidationHostedService : IHostedService
{
    public async Task StartAsync(CancellationToken cancellationToken)
    {
        await StartupConfigurationValidator.ValidateAtStartupAsync();
    }
}
```

## 🛠️ Configuration Examples

### **Development Environment**
```csharp
// Lenient validation for development
var behavior = StartupConfigurationValidator.ValidationBehavior.Lenient;

// Allow security disabled
config.DefaultSecurity = "none";  // Warning, not error

// Allow console logging
config.LogMode = UsdpConfiguration.LoggingMode.Console;
```

### **Production Environment**
```csharp
// Strict validation for production
var behavior = StartupConfigurationValidator.ValidationBehavior.Strict;

// Require security
config.DefaultSecurity = "cert-tls1.3";  // Must be secure
config.UseHttps = true;                   // HTTPS required
config.LogMode = UsdpConfiguration.LoggingMode.EventLog;
```

### **Testing Environment**
```csharp
// Log-only validation for testing
var behavior = StartupConfigurationValidator.ValidationBehavior.LogOnly;

// Never fail tests due to configuration
// All validation results are logged but don't stop execution
```

## 📈 Monitoring Integration

### **Health Checks**
```csharp
// Add configuration validation to health checks
services.AddHealthChecks()
    .AddCheck("configuration", () =>
    {
        var report = StartupConfigurationValidator.CreateValidationReport();
        return report.IsValid 
            ? HealthCheckResult.Healthy("Configuration is valid")
            : HealthCheckResult.Unhealthy($"Configuration has {report.ErrorIssues} errors");
    });
```

### **Metrics Collection**
```csharp
// Collect validation metrics
var report = StartupConfigurationValidator.CreateValidationReport();
metrics.RecordValue("config.validation.issues.total", report.TotalIssues);
metrics.RecordValue("config.validation.issues.critical", report.CriticalIssues);
metrics.RecordValue("config.validation.issues.errors", report.ErrorIssues);
```

This comprehensive configuration validation system ensures that USDP2 applications start with valid, well-configured settings and provides clear guidance for resolving any configuration issues.
