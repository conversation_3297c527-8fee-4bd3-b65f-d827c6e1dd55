using System;
using System.Threading;
using System.Threading.Tasks;
using USDP2.HealthCheck;

namespace USDP2.Examples
{
    /// <summary>
    /// Demonstrates the comprehensive health check system for USDP2 components.
    /// 
    /// This example shows:
    /// - How to configure and enable health monitoring
    /// - Setting up health checks for all major components
    /// - Monitoring system health in real-time
    /// - Handling health check events and alerts
    /// - Best practices for production health monitoring
    /// </summary>
    public static class HealthCheckExample
    {
        /// <summary>
        /// Runs a comprehensive demonstration of the health check system.
        /// </summary>
        public static async Task RunAsync()
        {
            Console.WriteLine("=== USDP2 Health Check System Example ===");
            Console.WriteLine();

            try
            {
                // Step 1: Configure health check settings
                await ConfigureHealthCheckSettingsAsync();

                // Step 2: Create and configure components
                var components = await CreateTestComponentsAsync();

                // Step 3: Set up comprehensive health monitoring
                var healthManager = await SetupHealthMonitoringAsync(components);

                // Step 4: Demonstrate real-time health monitoring
                await DemonstrateHealthMonitoringAsync(healthManager);

                // Step 5: Show health check events and alerting
                await DemonstrateHealthEventsAsync(healthManager);

                // Step 6: Test failure scenarios and recovery
                await TestFailureScenarios(healthManager);

                // Step 7: Show health reporting and analysis
                await DemonstrateHealthReportingAsync(healthManager);

                // Cleanup
                healthManager.Dispose();
                await DisposeComponentsAsync(components);

                Console.WriteLine("\n=== Health Check Example Complete ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Example failed: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// Configures health check settings for optimal monitoring.
        /// </summary>
        private static async Task ConfigureHealthCheckSettingsAsync()
        {
            Console.WriteLine("1. Configuring Health Check Settings");
            Console.WriteLine("===================================");

            // Enable comprehensive health monitoring
            UsdpConfiguration.Instance.HealthChecksEnabled = true;
            UsdpConfiguration.Instance.HealthCheckInterval = TimeSpan.FromSeconds(30); // More frequent for demo
            UsdpConfiguration.Instance.HealthCheckTimeout = TimeSpan.FromSeconds(15);
            UsdpConfiguration.Instance.LogHealthCheckResults = true;
            UsdpConfiguration.Instance.IncludeDetailedHealthData = true;

            // Enable all component health checks
            UsdpConfiguration.Instance.EnableChordNodeHealthCheck = true;
            UsdpConfiguration.Instance.EnableDirectoryNodeHealthCheck = true;
            UsdpConfiguration.Instance.EnableNetworkComponentsHealthCheck = true;
            UsdpConfiguration.Instance.EnableServiceCacheHealthCheck = true;

            // Configure thresholds
            UsdpConfiguration.Instance.HealthCheckFailureThreshold = 2; // Faster detection for demo
            UsdpConfiguration.Instance.HealthCheckSuccessThreshold = 1;

            Console.WriteLine($"✓ Health checks enabled: {UsdpConfiguration.Instance.HealthChecksEnabled}");
            Console.WriteLine($"✓ Check interval: {UsdpConfiguration.Instance.HealthCheckInterval.TotalSeconds} seconds");
            Console.WriteLine($"✓ Check timeout: {UsdpConfiguration.Instance.HealthCheckTimeout.TotalSeconds} seconds");
            Console.WriteLine($"✓ Failure threshold: {UsdpConfiguration.Instance.HealthCheckFailureThreshold}");
            Console.WriteLine($"✓ All component checks enabled");
            Console.WriteLine();

            await Task.Delay(100); // Brief pause for demonstration
        }

        /// <summary>
        /// Creates test components for health monitoring demonstration.
        /// </summary>
        private static async Task<TestComponents> CreateTestComponentsAsync()
        {
            Console.WriteLine("2. Creating Test Components");
            Console.WriteLine("===========================");

            // Create ChordNode
            var chordNode = new ChordNode("127.0.0.1", 8080);
            Console.WriteLine("✓ ChordNode created");

            // Create network components
            var udpSender = new UdpNetworkSender();
            var udpReceiver = new UdpNetworkReceiver(8081, false);
            Console.WriteLine("✓ UDP network components created");

            // Create DirectoryNode
            var directoryNode = new DirectoryNode(udpSender, udpReceiver);
            Console.WriteLine("✓ DirectoryNode created");

            // Create ServiceAdvertisementCache
            var serviceCache = new ServiceAdvertisementCache();
            Console.WriteLine("✓ ServiceAdvertisementCache created");

            // Add some test data to components
            await PopulateTestDataAsync(chordNode, serviceCache);

            Console.WriteLine("✓ Test data populated");
            Console.WriteLine();

            return new TestComponents
            {
                ChordNode = chordNode,
                DirectoryNode = directoryNode,
                UdpSender = udpSender,
                UdpReceiver = udpReceiver,
                ServiceCache = serviceCache
            };
        }

        /// <summary>
        /// Sets up comprehensive health monitoring for all components.
        /// </summary>
        private static async Task<HealthCheckManager> SetupHealthMonitoringAsync(TestComponents components)
        {
            Console.WriteLine("3. Setting Up Health Monitoring");
            Console.WriteLine("===============================");

            // Create comprehensive health check manager
            var healthManager = HealthCheckFactory.CreateComprehensiveHealthCheckManager(
                chordNode: components.ChordNode,
                directoryNode: components.DirectoryNode,
                networkSender: components.UdpSender,
                networkReceiver: components.UdpReceiver,
                serviceCache: components.ServiceCache
            );

            // Set up event handlers for real-time monitoring
            healthManager.HealthCheckCompleted += (sender, args) =>
            {
                var status = args.Result.Status;
                var icon = status switch
                {
                    HealthStatus.Healthy => "✅",
                    HealthStatus.Degraded => "⚠️",
                    HealthStatus.Unhealthy => "❌",
                    _ => "❓"
                };

                Console.WriteLine($"  {icon} {args.Name}: {status} ({args.Result.Duration.TotalMilliseconds:F0}ms)");

                if (!args.Result.IsHealthy)
                {
                    Console.WriteLine($"    Issue: {args.Result.Description}");
                }
            };

            healthManager.HealthReportGenerated += (sender, args) =>
            {
                var report = args.Report;
                Console.WriteLine($"\n📊 Health Report: {report.Status} ({report.Entries.Count} checks, {report.TotalDuration.TotalMilliseconds:F0}ms total)");
            };

            Console.WriteLine($"✓ Health manager created with {healthManager.GetHealthCheckNames().Count()} health checks");
            Console.WriteLine("✓ Event handlers configured for real-time monitoring");
            Console.WriteLine();

            await Task.Delay(100); // Brief pause
            return healthManager;
        }

        /// <summary>
        /// Demonstrates real-time health monitoring.
        /// </summary>
        private static async Task DemonstrateHealthMonitoringAsync(HealthCheckManager healthManager)
        {
            Console.WriteLine("4. Real-Time Health Monitoring");
            Console.WriteLine("==============================");

            Console.WriteLine("Running initial health check...");
            var initialReport = await healthManager.CheckHealthAsync();

            Console.WriteLine($"\nInitial System Health: {initialReport.Status}");
            Console.WriteLine($"Total checks: {initialReport.Entries.Count}");
            Console.WriteLine($"Healthy: {initialReport.Entries.Values.Count(r => r.IsHealthy)}");
            Console.WriteLine($"Degraded: {initialReport.Entries.Values.Count(r => r.IsDegraded)}");
            Console.WriteLine($"Unhealthy: {initialReport.Entries.Values.Count(r => r.IsUnhealthy)}");

            Console.WriteLine("\nRunning periodic health checks for 30 seconds...");
            using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(30));

            while (!cts.Token.IsCancellationRequested)
            {
                await Task.Delay(5000, cts.Token); // Check every 5 seconds for demo

                try
                {
                    await healthManager.CheckHealthAsync(cts.Token);
                }
                catch (OperationCanceledException)
                {
                    break;
                }
            }

            Console.WriteLine("✓ Real-time monitoring demonstration complete");
            Console.WriteLine();
        }

        /// <summary>
        /// Demonstrates health check events and alerting.
        /// </summary>
        private static async Task DemonstrateHealthEventsAsync(HealthCheckManager healthManager)
        {
            Console.WriteLine("5. Health Check Events and Alerting");
            Console.WriteLine("===================================");

            // Test individual health checks
            Console.WriteLine("Testing individual component health checks:");

            foreach (var checkName in healthManager.GetHealthCheckNames())
            {
                Console.WriteLine($"\nChecking {checkName}...");
                var result = await healthManager.CheckHealthAsync(checkName);

                if (result != null)
                {
                    Console.WriteLine($"  Status: {result.Status}");
                    Console.WriteLine($"  Duration: {result.Duration.TotalMilliseconds:F0}ms");

                    if (result.Data.Any())
                    {
                        Console.WriteLine("  Key metrics:");
                        foreach (var kvp in result.Data.Take(3))
                        {
                            Console.WriteLine($"    {kvp.Key}: {kvp.Value}");
                        }
                    }
                }
            }

            Console.WriteLine("\n✓ Individual health check demonstration complete");
            Console.WriteLine();
        }

        /// <summary>
        /// Tests failure scenarios and recovery monitoring.
        /// </summary>
        private static async Task TestFailureScenarios(HealthCheckManager healthManager)
        {
            Console.WriteLine("6. Testing Failure Scenarios");
            Console.WriteLine("============================");

            Console.WriteLine("Simulating component stress to test health monitoring...");

            // Run multiple health checks rapidly to test performance
            var tasks = new List<Task>();
            for (int i = 0; i < 10; i++)
            {
                tasks.Add(healthManager.CheckHealthAsync());
            }

            await Task.WhenAll(tasks);
            Console.WriteLine("✓ Stress test completed - health system remained stable");

            // Test timeout scenarios
            Console.WriteLine("\nTesting timeout handling...");
            using var shortTimeoutCts = new CancellationTokenSource(TimeSpan.FromMilliseconds(1));

            try
            {
                await healthManager.CheckHealthAsync(shortTimeoutCts.Token);
            }
            catch (OperationCanceledException)
            {
                Console.WriteLine("✓ Timeout handling working correctly");
            }

            Console.WriteLine();
        }

        /// <summary>
        /// Demonstrates health reporting and analysis.
        /// </summary>
        private static async Task DemonstrateHealthReportingAsync(HealthCheckManager healthManager)
        {
            Console.WriteLine("7. Health Reporting and Analysis");
            Console.WriteLine("================================");

            var finalReport = await healthManager.CheckHealthAsync();

            Console.WriteLine("Final Health Report:");
            Console.WriteLine($"  Overall Status: {finalReport.Status}");
            Console.WriteLine($"  Total Duration: {finalReport.TotalDuration.TotalMilliseconds:F0}ms");
            Console.WriteLine($"  Timestamp: {finalReport.Timestamp:yyyy-MM-dd HH:mm:ss}");

            Console.WriteLine("\nDetailed Component Status:");
            foreach (var entry in finalReport.Entries.OrderBy(e => e.Key))
            {
                var result = entry.Value;
                var statusIcon = result.Status switch
                {
                    HealthStatus.Healthy => "✅",
                    HealthStatus.Degraded => "⚠️",
                    HealthStatus.Unhealthy => "❌",
                    _ => "❓"
                };

                Console.WriteLine($"  {statusIcon} {entry.Key}: {result.Status} ({result.Duration.TotalMilliseconds:F0}ms)");

                if (!string.IsNullOrEmpty(result.Description) && !result.IsHealthy)
                {
                    Console.WriteLine($"      {result.Description}");
                }
            }

            // Show health trends
            Console.WriteLine("\nHealth Check Performance Summary:");
            var avgDuration = finalReport.Entries.Values.Average(r => r.Duration.TotalMilliseconds);
            var maxDuration = finalReport.Entries.Values.Max(r => r.Duration.TotalMilliseconds);
            var minDuration = finalReport.Entries.Values.Min(r => r.Duration.TotalMilliseconds);

            Console.WriteLine($"  Average check duration: {avgDuration:F1}ms");
            Console.WriteLine($"  Fastest check: {minDuration:F1}ms");
            Console.WriteLine($"  Slowest check: {maxDuration:F1}ms");

            Console.WriteLine();
        }

        /// <summary>
        /// Populates test data in components for realistic health monitoring.
        /// </summary>
        private static async Task PopulateTestDataAsync(ChordNode chordNode, ServiceAdvertisementCache serviceCache)
        {
            // Add test data to ChordNode
            for (int i = 0; i < 10; i++)
            {
                await chordNode.StoreAsync(new System.Numerics.BigInteger(i), $"test_value_{i}");
            }

            // Add test services to cache
            for (int i = 0; i < 5; i++)
            {
                var serviceId = new ServiceIdentifier("test", $"service-{i}");
                var endpoint = new TransportEndpoint
                {
                    Address = $"192.168.1.{100 + i}",
                    Port = 8000 + i,
                    Protocol = "tcp"
                };
                var advertisement = new ServiceAdvertisement(serviceId, endpoint);
                serviceCache.AddOrUpdate(advertisement);
            }
        }

        /// <summary>
        /// Disposes test components properly.
        /// </summary>
        private static async Task DisposeComponentsAsync(TestComponents components)
        {
            components.UdpSender?.Dispose();
            // Note: Other components don't implement IDisposable in the current implementation
            await Task.Delay(1); // Minimal async operation
        }

        /// <summary>
        /// Container for test components.
        /// </summary>
        private class TestComponents
        {
            public ChordNode? ChordNode { get; set; }
            public DirectoryNode? DirectoryNode { get; set; }
            public UdpNetworkSender? UdpSender { get; set; }
            public UdpNetworkReceiver? UdpReceiver { get; set; }
            public ServiceAdvertisementCache? ServiceCache { get; set; }
        }
    }
}
