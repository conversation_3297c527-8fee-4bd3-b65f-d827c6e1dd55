using System.Text.Json;

namespace USDP2
{
    /// <summary>
    /// The USDP serializer with automatic optimization based on configuration.
    /// </summary>
    public static class USDPSerializer
    {
        /// <summary>
        /// The serializer instance, automatically selected based on configuration.
        /// </summary>
        private static IUSDPSerializer _serializer = CreateDefaultSerializer();

        /// <summary>
        /// Creates the default serializer based on configuration settings.
        /// </summary>
        private static IUSDPSerializer CreateDefaultSerializer()
        {
            // Initialize cache configuration from UsdpConfiguration
            SerializationCacheManager.CacheConfig.CachingEnabled = UsdpConfiguration.Instance.SerializationCachingEnabled;
            SerializationCacheManager.CacheConfig.MaxJsonCacheSize = UsdpConfiguration.Instance.MaxJsonCacheSize;
            SerializationCacheManager.CacheConfig.MaxCborCacheSize = UsdpConfiguration.Instance.MaxCborCacheSize;
            SerializationCacheManager.CacheConfig.CacheTtl = UsdpConfiguration.Instance.SerializationCacheTtl;

            // Return optimized or default serializer based on configuration
            if (UsdpConfiguration.Instance.UseOptimizedSerializer)
            {
                UsdpLogger.Log("SerializerInitialization", new
                {
                    SerializerType = "OptimizedUSDPSerializer",
                    CachingEnabled = UsdpConfiguration.Instance.SerializationCachingEnabled,
                    MaxJsonCacheSize = UsdpConfiguration.Instance.MaxJsonCacheSize,
                    MaxCborCacheSize = UsdpConfiguration.Instance.MaxCborCacheSize,
                    CacheTtl = UsdpConfiguration.Instance.SerializationCacheTtl.TotalMinutes
                });
                return new OptimizedUSDPSerializer();
            }
            else
            {
                UsdpLogger.Log("SerializerInitialization", new
                {
                    SerializerType = "DefaultUSDPSerializer",
                    CachingEnabled = false,
                    Message = "Using default serializer without optimizations"
                });
                return new DefaultUSDPSerializer();
            }
        }

        /// <summary>
        /// Set the serializer.
        /// </summary>
        /// <param name="serializer">The serializer.</param>
        public static void SetSerializer(IUSDPSerializer serializer)
        {
            _serializer = serializer;
        }

        /// <summary>
        /// Converts an object to a JSON string.
        /// </summary>
        /// <typeparam name="T">The type of the object to serialize.</typeparam>
        /// <param name="obj">The object to serialize.</param>
        /// <returns>A JSON string representation of the object, or an empty string if serialization fails.</returns>
        public static string ToJson<T>(T obj) where T : class =>
            _serializer.SerializeToJsonLegacy(obj);

        /// <summary>
        /// Deserializes an object from a JSON string.
        /// </summary>
        /// <typeparam name="T">The type to deserialize to.</typeparam>
        /// <param name="json">The JSON string to deserialize.</param>
        /// <returns>The deserialized object, or null if deserialization fails.</returns>
        public static T? FromJson<T>(string json) where T : class =>
            _serializer.DeserializeFromJsonLegacy<T>(json);

        /// <summary>
        /// Converts an object to CBOR binary format.
        /// </summary>
        /// <typeparam name="T">The type of the object to serialize.</typeparam>
        /// <param name="obj">The object to serialize.</param>
        /// <returns>A byte array containing the CBOR representation of the object, or an empty array if serialization fails.</returns>
        public static byte[] ToCbor<T>(T obj) where T : class =>
            _serializer.SerializeToCborLegacy(obj);

        /// <summary>
        /// Deserializes an object from CBOR binary data.
        /// </summary>
        /// <typeparam name="T">The type to deserialize to.</typeparam>
        /// <param name="cbor">The CBOR binary data to deserialize.</param>
        /// <returns>The deserialized object, or null if deserialization fails.</returns>
        public static T? FromCbor<T>(byte[] cbor) where T : class =>
            _serializer.DeserializeFromCborLegacy<T>(cbor);

        /// <summary>
        /// Converts an object to a JSON string with detailed error information.
        /// </summary>
        /// <typeparam name="T">The type of the object to serialize.</typeparam>
        /// <param name="obj">The object to serialize.</param>
        /// <returns>A <see cref="SerializationResult{String}"/> containing the JSON string or error information.</returns>
        public static SerializationResult<string> ToJsonWithResult<T>(T obj) where T : class =>
            _serializer.SerializeToJson(obj);

        /// <summary>
        /// Deserializes an object from a JSON string with detailed error information.
        /// </summary>
        /// <typeparam name="T">The type to deserialize to.</typeparam>
        /// <param name="json">The JSON string to deserialize.</param>
        /// <returns>A <see cref="SerializationResult{T}"/> containing the deserialized object or error information.</returns>
        public static SerializationResult<T> FromJsonWithResult<T>(string json) where T : class =>
            _serializer.DeserializeFromJson<T>(json);

        /// <summary>
        /// Converts an object to CBOR binary format with detailed error information.
        /// </summary>
        /// <typeparam name="T">The type of the object to serialize.</typeparam>
        /// <param name="obj">The object to serialize.</param>
        /// <returns>A <see cref="SerializationResult{Byte[]}"/> containing the CBOR binary data or error information.</returns>
        public static SerializationResult<byte[]> ToCborWithResult<T>(T obj) where T : class =>
            _serializer.SerializeToCbor(obj);

        /// <summary>
        /// Deserializes an object from CBOR binary data with detailed error information.
        /// </summary>
        /// <typeparam name="T">The type to deserialize to.</typeparam>
        /// <param name="cbor">The CBOR binary data to deserialize.</param>
        /// <returns>A <see cref="SerializationResult{T}"/> containing the deserialized object or error information.</returns>
        public static SerializationResult<T> FromCborWithResult<T>(byte[] cbor) where T : class =>
            _serializer.DeserializeFromCbor<T>(cbor);
    }
}

/*
Example usage:

// Serialize
byte[] cbor = USDPSerializer.ToCbor(serviceAdvertisement);

// Deserialize
var adv = USDPSerializer.FromCbor<ServiceAdvertisement>(cbor);
*/