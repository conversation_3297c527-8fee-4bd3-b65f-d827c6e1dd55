using System;
using System.Security.Cryptography.X509Certificates;
using System.Threading.Tasks;

namespace USDP2
{
    /// <summary>
    /// Certificate-based authentication provider that validates X.509 certificates.
    /// </summary>
    public class CertificateAuthenticationProvider : IAuthenticationProvider
    {
        private readonly X509Certificate2Collection _trustedCertificates;
        private readonly bool _checkRevocation;
        private readonly bool _validateChain;

        /// <summary>
        /// Initializes a new instance of the <see cref="CertificateAuthenticationProvider"/> class.
        /// </summary>
        /// <param name="trustedCertificates">Collection of trusted certificates.</param>
        /// <param name="checkRevocation">Whether to check certificate revocation status.</param>
        /// <param name="validateChain">Whether to validate the certificate chain.</param>
        public CertificateAuthenticationProvider(
            X509Certificate2Collection trustedCertificates,
            bool checkRevocation = true,
            bool validateChain = true)
        {
            _trustedCertificates = trustedCertificates ?? throw new ArgumentNullException(nameof(trustedCertificates));
            _checkRevocation = checkRevocation;
            _validateChain = validateChain;
        }

        /// <summary>
        /// Authenticates a certificate by validating it against trusted certificates.
        /// </summary>
        /// <param name="tokenOrKey">Base64-encoded X.509 certificate.</param>
        /// <returns>True if the certificate is valid and trusted, false otherwise.</returns>
        public bool Authenticate(string tokenOrKey)
        {
            try
            {
                // Convert base64 string to certificate
                var certBytes = Convert.FromBase64String(tokenOrKey);
                var certificate = new X509Certificate2(certBytes);

                // Validate certificate
                return ValidateCertificate(certificate);
            }
            catch (Exception ex)
            {
                // Log the exception
                Diagnostics.Log("CertificateAuthError", new { message = $"Certificate authentication failed: {ex.Message}" });
                return false;
            }
        }

        /// <summary>
        /// Authenticates a certificate asynchronously.
        /// </summary>
        /// <param name="tokenOrKey">Base64-encoded X.509 certificate.</param>
        /// <returns>A task that represents the asynchronous operation. The value of the TResult parameter contains
        /// true if the certificate is valid and trusted, false otherwise.</returns>
        public Task<bool> AuthenticateAsync(string tokenOrKey)
        {
            // Certificate validation is typically CPU-bound, so we can use Task.FromResult
            return Task.FromResult(Authenticate(tokenOrKey));
        }

        private bool ValidateCertificate(X509Certificate2 certificate)
        {
            // Check if certificate is in trusted store
            foreach (var trustedCert in _trustedCertificates)
            {
                if (certificate.Thumbprint == trustedCert.Thumbprint)
                {
                    return true;
                }
            }

            // If validateChain is true, validate the certificate chain
            if (_validateChain)
            {
                var chain = new X509Chain();
                chain.ChainPolicy.RevocationMode = _checkRevocation
                    ? X509RevocationMode.Online
                    : X509RevocationMode.NoCheck;

                chain.ChainPolicy.VerificationFlags = X509VerificationFlags.NoFlag;
                chain.ChainPolicy.ExtraStore.AddRange(_trustedCertificates);

                bool isValid = chain.Build(certificate);

                // Additional checks can be added here

                return isValid;
            }

            return false;
        }
    }
}