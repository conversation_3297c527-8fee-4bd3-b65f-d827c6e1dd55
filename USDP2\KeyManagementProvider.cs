using System;
using System.Text.Json;
using System.IO;
using System.Runtime.InteropServices;
using System.Security.Cryptography;
using System.Threading.Tasks;


#if AZURE_KEYVAULT
using Azure.Identity;
using Azure.Security.KeyVault.Secrets;
#endif

namespace USDP2
{
    /// <summary>
    /// The keys management backends.
    /// </summary>
    public enum KeyManagementBackend
    {
        /// <summary>
        /// Use Windows Data Protection API (DPAPI) for key management (Windows only).
        /// </summary>
        WindowsDPAPI,
#if AZURE_KEYVAULT
        /// <summary>
        /// Use Azure Key Vault for key management.
        /// </summary>
        AzureKeyVault,
#endif
        /// <summary>
        /// Use HashiCorp Vault for key management.
        /// </summary>
        HashiCorpVault,
    }

    /// <summary>
    /// The key management provider.
    /// </summary>
    public static class KeyManagementProvider
    {
        // Unified key loader
        /// <summary>
        /// Load the key asynchronously.
        /// </summary>
        /// <param name="backend">The backend.</param>
        /// <param name="keyNameOrPath">The key name or path.</param>
        /// <param name="vaultUri">The vault uri.</param>
        /// <param name="hashicorpToken">The hashicorp token.</param>
        /// <param name="gcpProjectId">The gcp project id.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <exception cref="NotSupportedException"></exception>
        /// <returns>A <see cref="Task"/> of type an array of bytes</returns>
        public static async Task<byte[]> LoadKeyAsync(
            KeyManagementBackend backend,
            string keyNameOrPath,
            string? vaultUri = null,
            string? hashicorpToken = null,
            string? gcpProjectId = null)
        {
            ArgumentException.ThrowIfNullOrEmpty(keyNameOrPath);
            switch (backend)
            {
                case KeyManagementBackend.WindowsDPAPI:
                    return LoadKeyFromDPAPI(keyNameOrPath);

#if AZURE_KEYVAULT
				case KeyManagementBackend.AzureKeyVault:
					if (vaultUri == null) throw new ArgumentNullException(nameof(vaultUri));
                    return await LoadKeyFromAzureKeyVaultAsync(vaultUri, keyNameOrPath).ConfigureAwait(false);
#endif

                case KeyManagementBackend.HashiCorpVault:
                    ArgumentNullException.ThrowIfNull(hashicorpToken);
                    return await LoadKeyFromHashiCorpVaultAsync(keyNameOrPath, hashicorpToken);


                // GoogleCloudSecretManager not implemented in this build
                // case KeyManagementBackend.GoogleCloudSecretManager:
                //     if (gcpProjectId == null) throw new ArgumentNullException(nameof(gcpProjectId));
                //     return await LoadKeyFromGoogleCloudSecretManagerAsync(gcpProjectId, keyNameOrPath);

                default:
                    throw new NotSupportedException("Unknown key management backend.");
            }
        }

        // --- Windows DPAPI ---
        /// <summary>
        /// Load key from DPAPI.
        /// </summary>
        /// <param name="path">The path.</param>
        /// <exception cref="PlatformNotSupportedException"></exception>
        /// <returns>An array of bytes</returns>
        private static byte[] LoadKeyFromDPAPI(string path)
        {
            if (!RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            {
                throw new PlatformNotSupportedException("DPAPI is only supported on Windows.");
            }

            var protectedKey = File.ReadAllBytes(path);
            return ProtectedData.Unprotect(protectedKey, null, DataProtectionScope.CurrentUser);
        }

        /// <summary>
        /// Save key converts to DPAPI.
        /// </summary>
        /// <param name="path">The path.</param>
        /// <param name="key">The key.</param>
        /// <exception cref="PlatformNotSupportedException"></exception>
        public static void SaveKeyToDPAPI(string path, byte[] key)
        {
            ArgumentException.ThrowIfNullOrEmpty(path);
            ArgumentNullException.ThrowIfNull(key);

            if (!RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            {
                throw new PlatformNotSupportedException("DPAPI is only supported on Windows.");
            }

            var protectedKey = ProtectedData.Protect(key, null, DataProtectionScope.CurrentUser);
            File.WriteAllBytes(path, protectedKey);
        }

#if AZURE_KEYVAULT
        // --- Azure Key Vault ---
        private static async Task<byte[]> LoadKeyFromAzureKeyVaultAsync(string vaultUri, string secretName)
        {
            var client = new SecretClient(new Uri(vaultUri), new DefaultAzureCredential());
            KeyVaultSecret secret = await client.GetSecretAsync(secretName);
            return Convert.FromBase64String(secret.Value);
        }
#endif

        // --- HashiCorp Vault ---
        /// <summary>
        /// Load key from hashi corp vault asynchronously.
        /// </summary>
        /// <param name="secretPath">The secret path.</param>
        /// <param name="token">The token.</param>
        /// <returns>A <see cref="Task"/> of type an array of bytes</returns>
        private static async Task<byte[]> LoadKeyFromHashiCorpVaultAsync(string secretPath, string token)
        {
            using var httpClient = new HttpClient();
            httpClient.DefaultRequestHeaders.Add("X-Vault-Token", token);
            var response = await httpClient.GetAsync(secretPath);
            response.EnsureSuccessStatusCode();
            var json = await response.Content.ReadAsStringAsync();
            using var doc = JsonDocument.Parse(json!);
            string? keyValue = null;
            if (doc.RootElement.TryGetProperty("data", out var dataProperty) &&
                dataProperty.TryGetProperty("data", out var innerDataProperty) &&
                innerDataProperty.TryGetProperty("value", out var valueProperty))
            {
                keyValue = valueProperty.GetString();
            }

            if (keyValue == null)
            {
                // Handle the case where the key is not found or is null.  
                throw new KeyNotFoundException("The 'value' property was not found or was null in the HashiCorp Vault response.");
            }

            return Convert.FromBase64String(keyValue);
        }

#if GOOGLE_SECRET_MANAGER
        // --- Google Cloud Secret Manager ---
        private static async Task<byte[]> LoadKeyFromGoogleCloudSecretManagerAsync(string projectId, string secretId)
        {
            SecretManagerServiceClient client = await SecretManagerServiceClient.CreateAsync();
            var secretVersionName = new SecretVersionName(projectId, secretId, "latest");
            var result = await client.AccessSecretVersionAsync(secretVersionName);
            return result.Payload.Data.ToByteArray();
        }
#endif
    }
};