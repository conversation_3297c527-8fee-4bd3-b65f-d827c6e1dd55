using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;

namespace USDP2
{
    /// <summary>
    /// Provides secure configuration management for the USDP2 protocol.
    /// </summary>
    public class ConfigurationProvider
    {
        private readonly Dictionary<string, object> _configuration;
        private readonly KeyManagementBackend _keyBackend;
        private readonly string _configPath;
        private readonly string _keyNameOrPath;
        private readonly string _vaultUri;
        private readonly string _hashicorpToken;
        private readonly string _gcpProjectId;

        // Cached JsonSerializerOptions for better performance
        private static readonly JsonSerializerOptions _serializerOptions = new JsonSerializerOptions
        {
            WriteIndented = true
        };

        /// <summary>
        /// Initializes a new instance of the <see cref="ConfigurationProvider"/> class.
        /// </summary>
        /// <param name="configPath">Path to the configuration file.</param>
        /// <param name="keyBackend">The key management backend to use.</param>
        /// <param name="keyNameOrPath">The key name or path.</param>
        /// <param name="vaultUri">The vault URI (for Azure Key Vault).</param>
        /// <param name="hashicorpToken">The HashiCorp Vault token.</param>
        /// <param name="gcpProjectId">The Google Cloud Project ID.</param>
        public ConfigurationProvider(
            string configPath,
            KeyManagementBackend keyBackend = KeyManagementBackend.WindowsDPAPI,
            string keyNameOrPath = "",
            string vaultUri = "",
            string hashicorpToken = "",
            string gcpProjectId = "")
        {
            _configPath = configPath ?? throw new ArgumentNullException(nameof(configPath));
            _keyBackend = keyBackend;
            _keyNameOrPath = keyNameOrPath;
            _vaultUri = vaultUri ?? string.Empty;
            _hashicorpToken = hashicorpToken ?? string.Empty;
            _gcpProjectId = gcpProjectId ?? string.Empty;
            _configuration = new Dictionary<string, object>();
        }

        /// <summary>
        /// Loads configuration from the specified path.
        /// </summary>
        /// <returns>A task representing the asynchronous operation.</returns>
        public async Task LoadAsync()
        {
            if (!File.Exists(_configPath))
            {
                throw new FileNotFoundException($"Configuration file not found: {_configPath}");
            }

            string json = await File.ReadAllTextAsync(_configPath);
            var config = JsonSerializer.Deserialize<Dictionary<string, object>>(json, _serializerOptions);

            // Merge with existing configuration
            if (config != null)
            {
                foreach (var kvp in config)
                {
                    _configuration[kvp.Key] = kvp.Value;
                }
            }
        }
        /// <summary>
        /// Saves the current configuration to the specified path.
        /// </summary>
        /// <returns>A task representing the asynchronous operation.</returns>
        public async Task SaveAsync()
        {
            string json = JsonSerializer.Serialize(_configuration, _serializerOptions);

            await File.WriteAllTextAsync(_configPath, json);
        }

        /// <summary>
        /// Gets a configuration value.
        /// </summary>
        /// <typeparam name="T">The type of the configuration value.</typeparam>
        /// <param name="key">The configuration key.</param>
        /// <param name="defaultValue">The default value to return if the key is not found.</param>
        /// <returns>The configuration value, or the default value if not found.</returns>
        public T GetValue<T>(string key, T defaultValue = default)
        {
            if (_configuration.TryGetValue(key, out object? value))
            {
                if (value is JsonElement element)
                {
                    // Handle JsonElement conversion
                    return (T)ConvertJsonElement(element, typeof(T));
                }

                return (T)value;
            }

            return defaultValue;
        }

        /// <summary>
        /// Sets a configuration value.
        /// </summary>
        /// <typeparam name="T">The type of the configuration value.</typeparam>
        /// <param name="key">The configuration key.</param>
        /// <param name="value">The configuration value.</param>
        public void SetValue<T>(string key, T value)
        {
            _configuration[key] = (object?)value;
        }

        /// <summary>
        /// Gets a secure configuration value from the key management backend.
        /// </summary>
        /// <param name="secretName">The name of the secret.</param>
        /// <returns>A task that represents the asynchronous operation. The value of the TResult parameter contains the secret value.</returns>
        public async Task<string> GetSecretAsync(string secretName)
        {
            try
            {
                byte[] keyBytes = await KeyManagementProvider.LoadKeyAsync(
                    _keyBackend,
                    secretName,
                    _vaultUri,
                    _hashicorpToken,
                    _gcpProjectId);

                return Convert.ToBase64String(keyBytes);
            }
            catch (Exception ex)
            {
                Diagnostics.Log("ConfigurationError", new { secretName, message = ex.Message });
                throw;
            }
        }

        /// <summary>
        /// Rotates a key in the key management backend.
        /// </summary>
        /// <param name="secretName">The name of the secret.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        public Task RotateKeyAsync(string secretName)
        {
            // Implementation depends on the key management backend
            // This is a placeholder for key rotation logic
            throw new NotImplementedException("Key rotation is not implemented for this backend.");
        }

        private static object ConvertJsonElement(JsonElement element, Type targetType)
        {
            if (targetType == typeof(string))
            {
                return element.GetString() ?? string.Empty;
            }
            else if (targetType == typeof(int))
            {
                return element.GetInt32();
            }
            else if (targetType == typeof(bool))
            {
                return element.GetBoolean();
            }
            else if (targetType == typeof(double))
            {
                return element.GetDouble();
            }
            // Add more type conversions as needed

            // For unknown types, try to get the raw value or return a default
            return element.ValueKind switch
            {
                JsonValueKind.String => element.GetString() ?? string.Empty,
                JsonValueKind.Number => element.GetDouble(),
                JsonValueKind.True => true,
                JsonValueKind.False => false,
                JsonValueKind.Null => null!,
                _ => Activator.CreateInstance(targetType) ?? throw new InvalidOperationException($"Cannot convert JsonElement to {targetType.Name}")
            };
        }
    }
}