using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text.RegularExpressions;

namespace USDP2
{
    /// <summary>
    /// Provides input validation methods for external inputs.
    /// </summary>
    public static class InputValidator
    {
        /// <summary>
        /// Validates that a string is not null or empty.
        /// </summary>
        /// <param name="input">The input string to validate.</param>
        /// <param name="paramName">The parameter name for the exception message.</param>
        /// <exception cref="ArgumentNullException">Thrown when the input is null.</exception>
        /// <exception cref="ArgumentException">Thrown when the input is empty.</exception>
        public static void ValidateNotNullOrEmpty(string input, string paramName)
        {
            if (input == null)
            {
                throw new ArgumentNullException(paramName);
            }

            if (string.IsNullOrWhiteSpace(input))
            {
                throw new ArgumentException("Value cannot be empty or whitespace.", paramName);
            }
        }

        /// <summary>
        /// Validates that a port number is within the valid range.
        /// </summary>
        /// <param name="port">The port number to validate.</param>
        /// <param name="paramName">The parameter name for the exception message.</param>
        /// <exception cref="ArgumentOutOfRangeException">Thrown when the port is outside the valid range.</exception>
        public static void ValidatePort(int port, string paramName)
        {
            if (port < IPEndPoint.MinPort || port > IPEndPoint.MaxPort)
            {
                throw new ArgumentOutOfRangeException(paramName, port,
                    $"Port must be between {IPEndPoint.MinPort} and {IPEndPoint.MaxPort}.");
            }
        }

        /// <summary>
        /// Validates that an IP address is valid.
        /// </summary>
        /// <param name="address">The IP address to validate.</param>
        /// <param name="paramName">The parameter name for the exception message.</param>
        /// <exception cref="ArgumentException">Thrown when the address is invalid.</exception>
        public static void ValidateIpAddress(string address, string paramName)
        {
            ValidateNotNullOrEmpty(address, paramName);

            if (!IPAddress.TryParse(address, out _))
            {
                throw new ArgumentException("Invalid IP address format.", paramName);
            }
        }

        /// <summary>
        /// Validates that a multicast IP address is valid.
        /// </summary>
        /// <param name="address">The multicast IP address to validate.</param>
        /// <param name="paramName">The parameter name for the exception message.</param>
        /// <exception cref="ArgumentException">Thrown when the address is not a valid multicast address.</exception>
        public static void ValidateMulticastAddress(string address, string paramName)
        {
            ValidateIpAddress(address, paramName);

            if (IPAddress.TryParse(address, out IPAddress? ipAddress) && ipAddress != null)
            {
                if (ipAddress.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork)
                {
                    // IPv4 multicast: ********* to ***************
                    byte firstByte = ipAddress.GetAddressBytes()[0];
                    if (firstByte < 224 || firstByte > 239)
                    {
                        throw new ArgumentException("Invalid IPv4 multicast address. Must be in range ********* to ***************.", paramName);
                    }
                }
                else
                {
                    // IPv6 multicast: ff00::/8
                    byte firstByte = ipAddress.GetAddressBytes()[0];
                    if (firstByte != 0xff)
                    {
                        throw new ArgumentException("Invalid IPv6 multicast address. Must start with ff00::/8.", paramName);
                    }
                }
            }
        }

        /// <summary>
        /// Validates a service identifier.
        /// </summary>
        /// <param name="serviceId">The service identifier to validate.</param>
        /// <param name="paramName">The parameter name for the exception message.</param>
        /// <exception cref="ArgumentException">Thrown when the service identifier is invalid.</exception>
        public static void ValidateServiceIdentifier(ServiceIdentifier serviceId, string paramName)
        {
            if (serviceId == null)
            {
                throw new ArgumentNullException(paramName);
            }

            ValidateNotNullOrEmpty(serviceId.Namespace, $"{paramName}.Namespace");
            ValidateNotNullOrEmpty(serviceId.Name, $"{paramName}.Name");

            // Validate namespace format (e.g., "home/lighting")
            if (!Regex.IsMatch(serviceId.Namespace, @"^[a-zA-Z0-9]+(/[a-zA-Z0-9]+)*$"))
            {
                throw new ArgumentException(
                    "Namespace must be in format 'segment1/segment2/...' with alphanumeric segments.",
                    $"{paramName}.Namespace");
            }

            // Validate name format (e.g., "bulb1")
            if (!Regex.IsMatch(serviceId.Name, @"^[a-zA-Z0-9_-]+$"))
            {
                throw new ArgumentException(
                    "Name must contain only alphanumeric characters, underscores, and hyphens.",
                    $"{paramName}.Name");
            }
        }

        /// <summary>
        /// Validates a transport endpoint.
        /// </summary>
        /// <param name="endpoint">The transport endpoint to validate.</param>
        /// <param name="paramName">The parameter name for the exception message.</param>
        /// <exception cref="ArgumentException">Thrown when the transport endpoint is invalid.</exception>
        public static void ValidateTransportEndpoint(TransportEndpoint endpoint, string paramName)
        {
            if (endpoint == null)
            {
                throw new ArgumentNullException(paramName);
            }

            ValidateNotNullOrEmpty(endpoint.Protocol, $"{paramName}.Protocol");
            ValidateNotNullOrEmpty(endpoint.Address, $"{paramName}.Address");
            ValidatePort(endpoint.Port, $"{paramName}.Port");

            // Validate protocol format
            var validProtocols = new[] { "http", "https", "coap", "coap+udp", "coaps", "mqtt", "mqtts" };
            if (!validProtocols.Contains(endpoint.Protocol.ToLowerInvariant()))
            {
                throw new ArgumentException(
                    $"Protocol must be one of: {string.Join(", ", validProtocols)}.",
                    $"{paramName}.Protocol");
            }

            // Validate IP address or hostname
            if (!IPAddress.TryParse(endpoint.Address, out _) &&
                !Regex.IsMatch(endpoint.Address, @"^[a-zA-Z0-9]([a-zA-Z0-9\-\.]{0,61}[a-zA-Z0-9])?$"))
            {
                throw new ArgumentException(
                    "Address must be a valid IP address or hostname.",
                    $"{paramName}.Address");
            }
        }

        /// <summary>
        /// Validates metadata dictionary.
        /// </summary>
        /// <param name="metadata">The metadata dictionary to validate.</param>
        /// <param name="paramName">The parameter name for the exception message.</param>
        /// <exception cref="ArgumentException">Thrown when the metadata is invalid.</exception>
        public static void ValidateMetadata(Dictionary<string, object> metadata, string paramName)
        {
            if (metadata == null)
            {
                throw new ArgumentNullException(paramName);
            }

            foreach (var key in metadata.Keys)
            {
                if (string.IsNullOrWhiteSpace(key))
                {
                    throw new ArgumentException("Metadata keys cannot be null or empty.", paramName);
                }

                if (key.Length > 64)
                {
                    throw new ArgumentException("Metadata keys cannot exceed 64 characters.", paramName);
                }

                if (!Regex.IsMatch(key, @"^[a-zA-Z0-9_\-\.]+$"))
                {
                    throw new ArgumentException(
                        "Metadata keys must contain only alphanumeric characters, underscores, hyphens, and periods.",
                        paramName);
                }

                if (metadata[key] == null)
                {
                    throw new ArgumentException("Metadata values cannot be null.", paramName);
                }
            }
        }
    }
}