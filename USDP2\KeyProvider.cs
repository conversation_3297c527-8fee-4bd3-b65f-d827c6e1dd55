using System;
using System.IO;
using System.Runtime.Versioning;
using System.Security;
using System.Security.Cryptography;

namespace USDP2
{
    /// <summary>
    /// Utility for secure key storage and retrieval using DPAPI.
    /// </summary>
    public static class KeyProvider
    {
#if WINDOWS
        // Load a key from a protected file
        public static byte[] LoadProtectedKey(string path)
        {
            try
            {
                var protectedKey = File.ReadAllBytes(path);
                return ProtectedData.Unprotect(protectedKey, null, DataProtectionScope.CurrentUser);
            }
            catch (FileNotFoundException ex)
            {
                throw new KeyProviderException($"Key file not found at path: {path}", ex);
            }
            catch (IOException ex)
            {
                throw new KeyProviderException($"IO error while reading key file: {ex.Message}", ex);
            }
            catch (CryptographicException ex)
            {
                throw new KeyProviderException("Cryptographic error while decrypting key.", ex);
            }
            catch (SecurityException ex)
            {
                throw new KeyProviderException("Insufficient permissions to access the key file.", ex);

            }
        }

        // Save a key to a protected file
        public static void SaveProtectedKey(string path, byte[] key) => throw new PlatformNotSupportedException("DPAPI is only supported on Windows.");
        }
#else
        /// <summary>
        /// Load protected key.
        /// </summary>
        /// <param name="path">The path.</param>
        /// <returns>An array of bytes</returns>
        public static byte[] LoadProtectedKey(string path) =>
            throw new PlatformNotSupportedException("DPAPI is only supported on Windows.");

        /// <summary>
        /// Save protected key.
        /// </summary>
        /// <param name="path">The path.</param>
        /// <param name="key">The key.</param>
        public static void SaveProtectedKey(string path, byte[] key) =>
            throw new PlatformNotSupportedException("DPAPI is only supported on Windows.");
#endif
    }
}