namespace USDP2
{
    /// <summary>
    /// Security framework placeholder for message signing and verification.
    /// </summary>
    public static class Security
    {
        // TODO: Implement Ed25519 signing/verification, OAuth2/PSK, TLS/OSCORE integration.
        /// <summary>
        /// TODO: Add Summary.
        /// </summary>
        /// <param name="message">The message.</param>
        /// <param name="privateKey">The private key.</param>
        /// <returns>An array of bytes</returns>
        public static byte[] Sign(byte[] message, byte[] privateKey) => throw new System.NotImplementedException();
        /// <summary>
        /// TODO: Add Summary.
        /// </summary>
        /// <param name="message">The message.</param>
        /// <param name="signature">The signature.</param>
        /// <param name="publicKey">The public key.</param>
        /// <returns>A <see cref="bool"/></returns>
        public static bool Verify(byte[] message, byte[] signature, byte[] publicKey) => throw new System.NotImplementedException();
    }
}