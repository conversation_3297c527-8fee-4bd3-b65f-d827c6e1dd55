using System;
using System.Collections.Generic;

namespace USDP2.HealthCheck
{
    /// <summary>
    /// Represents the health status of a component.
    /// </summary>
    public enum HealthStatus
    {
        /// <summary>
        /// Component is healthy and functioning normally.
        /// </summary>
        Healthy = 0,

        /// <summary>
        /// Component is degraded but still functional.
        /// </summary>
        Degraded = 1,

        /// <summary>
        /// Component is unhealthy and may not be functioning properly.
        /// </summary>
        Unhealthy = 2,

        /// <summary>
        /// Component health status is unknown or cannot be determined.
        /// </summary>
        Unknown = 3
    }

    /// <summary>
    /// Represents the result of a health check operation.
    /// </summary>
    public class HealthCheckResult
    {
        /// <summary>
        /// Gets the health status of the component.
        /// </summary>
        public HealthStatus Status { get; }

        /// <summary>
        /// Gets a description of the health check result.
        /// </summary>
        public string Description { get; }

        /// <summary>
        /// Gets additional data associated with the health check.
        /// </summary>
        public IReadOnlyDictionary<string, object> Data { get; }

        /// <summary>
        /// Gets the exception that occurred during the health check, if any.
        /// </summary>
        public Exception? Exception { get; }

        /// <summary>
        /// Gets the duration of the health check operation.
        /// </summary>
        public TimeSpan Duration { get; }

        /// <summary>
        /// Gets the timestamp when the health check was performed.
        /// </summary>
        public DateTimeOffset Timestamp { get; }

        /// <summary>
        /// Initializes a new instance of the <see cref="HealthCheckResult"/> class.
        /// </summary>
        /// <param name="status">The health status.</param>
        /// <param name="description">The description of the result.</param>
        /// <param name="duration">The duration of the health check.</param>
        /// <param name="exception">The exception that occurred, if any.</param>
        /// <param name="data">Additional data associated with the health check.</param>
        public HealthCheckResult(
            HealthStatus status,
            string description,
            TimeSpan duration,
            Exception? exception = null,
            IReadOnlyDictionary<string, object>? data = null)
        {
            Status = status;
            Description = description ?? throw new ArgumentNullException(nameof(description));
            Duration = duration;
            Exception = exception;
            Data = data ?? new Dictionary<string, object>();
            Timestamp = DateTimeOffset.UtcNow;
        }

        /// <summary>
        /// Creates a healthy result.
        /// </summary>
        /// <param name="description">The description of the result.</param>
        /// <param name="duration">The duration of the health check.</param>
        /// <param name="data">Additional data associated with the health check.</param>
        /// <returns>A healthy <see cref="HealthCheckResult"/>.</returns>
        public static HealthCheckResult Healthy(string description, TimeSpan duration, IReadOnlyDictionary<string, object>? data = null)
        {
            return new HealthCheckResult(HealthStatus.Healthy, description, duration, null, data);
        }

        /// <summary>
        /// Creates a degraded result.
        /// </summary>
        /// <param name="description">The description of the result.</param>
        /// <param name="duration">The duration of the health check.</param>
        /// <param name="data">Additional data associated with the health check.</param>
        /// <returns>A degraded <see cref="HealthCheckResult"/>.</returns>
        public static HealthCheckResult Degraded(string description, TimeSpan duration, IReadOnlyDictionary<string, object>? data = null)
        {
            return new HealthCheckResult(HealthStatus.Degraded, description, duration, null, data);
        }

        /// <summary>
        /// Creates an unhealthy result.
        /// </summary>
        /// <param name="description">The description of the result.</param>
        /// <param name="duration">The duration of the health check.</param>
        /// <param name="exception">The exception that caused the unhealthy status.</param>
        /// <param name="data">Additional data associated with the health check.</param>
        /// <returns>An unhealthy <see cref="HealthCheckResult"/>.</returns>
        public static HealthCheckResult Unhealthy(string description, TimeSpan duration, Exception? exception = null, IReadOnlyDictionary<string, object>? data = null)
        {
            return new HealthCheckResult(HealthStatus.Unhealthy, description, duration, exception, data);
        }

        /// <summary>
        /// Creates an unknown result.
        /// </summary>
        /// <param name="description">The description of the result.</param>
        /// <param name="duration">The duration of the health check.</param>
        /// <param name="exception">The exception that caused the unknown status.</param>
        /// <param name="data">Additional data associated with the health check.</param>
        /// <returns>An unknown <see cref="HealthCheckResult"/>.</returns>
        public static HealthCheckResult Unknown(string description, TimeSpan duration, Exception? exception = null, IReadOnlyDictionary<string, object>? data = null)
        {
            return new HealthCheckResult(HealthStatus.Unknown, description, duration, exception, data);
        }

        /// <summary>
        /// Gets a value indicating whether the health check result represents a healthy state.
        /// </summary>
        public bool IsHealthy => Status == HealthStatus.Healthy;

        /// <summary>
        /// Gets a value indicating whether the health check result represents a degraded state.
        /// </summary>
        public bool IsDegraded => Status == HealthStatus.Degraded;

        /// <summary>
        /// Gets a value indicating whether the health check result represents an unhealthy state.
        /// </summary>
        public bool IsUnhealthy => Status == HealthStatus.Unhealthy;

        /// <summary>
        /// Gets a value indicating whether the health check result represents an unknown state.
        /// </summary>
        public bool IsUnknown => Status == HealthStatus.Unknown;

        /// <summary>
        /// Returns a string representation of the health check result.
        /// </summary>
        /// <returns>A string representation of the health check result.</returns>
        public override string ToString()
        {
            var result = $"{Status}: {Description} (Duration: {Duration.TotalMilliseconds:F2}ms)";
            if (Exception != null)
            {
                result += $" - Exception: {Exception.Message}";
            }
            return result;
        }
    }

    /// <summary>
    /// Represents a collection of health check results.
    /// </summary>
    public class HealthReport
    {
        /// <summary>
        /// Gets the overall health status of the system.
        /// </summary>
        public HealthStatus Status { get; }

        /// <summary>
        /// Gets the individual health check results.
        /// </summary>
        public IReadOnlyDictionary<string, HealthCheckResult> Entries { get; }

        /// <summary>
        /// Gets the total duration of all health checks.
        /// </summary>
        public TimeSpan TotalDuration { get; }

        /// <summary>
        /// Gets the timestamp when the health report was generated.
        /// </summary>
        public DateTimeOffset Timestamp { get; }

        /// <summary>
        /// Initializes a new instance of the <see cref="HealthReport"/> class.
        /// </summary>
        /// <param name="entries">The individual health check results.</param>
        public HealthReport(IReadOnlyDictionary<string, HealthCheckResult> entries)
        {
            Entries = entries ?? throw new ArgumentNullException(nameof(entries));
            Timestamp = DateTimeOffset.UtcNow;

            // Calculate overall status and total duration
            var totalDuration = TimeSpan.Zero;
            var overallStatus = HealthStatus.Healthy;

            foreach (var entry in entries.Values)
            {
                totalDuration = totalDuration.Add(entry.Duration);

                // Determine overall status (worst case wins)
                if (entry.Status > overallStatus)
                {
                    overallStatus = entry.Status;
                }
            }

            TotalDuration = totalDuration;
            Status = overallStatus;
        }

        /// <summary>
        /// Gets a value indicating whether the overall health status is healthy.
        /// </summary>
        public bool IsHealthy => Status == HealthStatus.Healthy;

        /// <summary>
        /// Gets a value indicating whether the overall health status is degraded.
        /// </summary>
        public bool IsDegraded => Status == HealthStatus.Degraded;

        /// <summary>
        /// Gets a value indicating whether the overall health status is unhealthy.
        /// </summary>
        public bool IsUnhealthy => Status == HealthStatus.Unhealthy;

        /// <summary>
        /// Gets a value indicating whether the overall health status is unknown.
        /// </summary>
        public bool IsUnknown => Status == HealthStatus.Unknown;

        /// <summary>
        /// Returns a string representation of the health report.
        /// </summary>
        /// <returns>A string representation of the health report.</returns>
        public override string ToString()
        {
            return $"Overall Status: {Status}, Checks: {Entries.Count}, Total Duration: {TotalDuration.TotalMilliseconds:F2}ms";
        }
    }
}
