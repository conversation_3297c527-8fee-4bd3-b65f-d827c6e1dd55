using Microsoft.Extensions.Logging;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Text.Json;

namespace USDP2
{
    /// <summary>
    /// USDP2 logging service that wraps Microsoft.Extensions.Logging and provides structured logging
    /// with backward compatibility for the existing Diagnostics.Log system.
    /// 
    /// <para>
    /// This service provides a centralized logging solution that supports:
    /// - Structured logging with Microsoft.Extensions.Logging
    /// - Automatic log level detection based on event types
    /// - JSON serialization of complex log data
    /// - Performance metrics collection
    /// - Configurable log providers (Console, Debug, File, etc.)
    /// - Thread-safe operations
    /// </para>
    /// 
    /// <para><strong>Features:</strong></para>
    /// <list type="bullet">
    /// <item><description><strong>Structured Logging:</strong> Supports complex objects and structured data</description></item>
    /// <item><description><strong>Auto Log Levels:</strong> Automatically determines log levels based on event names</description></item>
    /// <item><description><strong>Performance Tracking:</strong> Built-in metrics collection and reporting</description></item>
    /// <item><description><strong>Backward Compatible:</strong> Drop-in replacement for Diagnostics.Log</description></item>
    /// <item><description><strong>Configurable:</strong> Supports multiple log providers and custom formatting</description></item>
    /// </list>
    /// </summary>
    public static class UsdpLogger
    {
        private static ILoggerFactory? _loggerFactory;
        private static readonly ConcurrentDictionary<string, ILogger> _loggers = new();
        private static readonly ConcurrentDictionary<string, long> _eventCounts = new();
        private static readonly object _initializationLock = new();
        private static bool _isInitialized;
        
        // Cached JsonSerializerOptions for better performance in logging
        private static readonly JsonSerializerOptions _logSerializerOptions = new JsonSerializerOptions 
        { 
            WriteIndented = false 
        };

        /// <summary>
        /// Gets or sets whether performance metrics collection is enabled.
        /// When enabled, tracks event counts and timing information.
        /// </summary>
        public static bool EnableMetrics { get; set; } = true;

        /// <summary>
        /// Gets or sets the minimum log level for events.
        /// Events below this level will be filtered out.
        /// </summary>
        public static LogLevel MinimumLogLevel { get; set; } = LogLevel.Information;

        /// <summary>
        /// Gets or sets whether to use console output as a last resort fallback when logging fails.
        ///
        /// Default: false (no console fallback - appropriate for class libraries)
        ///
        /// When enabled, if the logging system completely fails, error messages will be written
        /// to the console as a last resort. This should generally be disabled for class libraries
        /// and only enabled for applications where console output is appropriate.
        /// </summary>
        public static bool EnableConsoleFallback { get; set; }

        /// <summary>
        /// Initializes the USDP logger with the specified logger factory.
        /// </summary>
        /// <param name="loggerFactory">
        /// The logger factory to use for creating loggers. If null, a default factory will be created.
        /// </param>
        /// <remarks>
        /// This method is thread-safe and can be called multiple times. Subsequent calls will
        /// replace the existing logger factory.
        /// </remarks>
        public static void Initialize(ILoggerFactory? loggerFactory = null)
        {
            lock (_initializationLock)
            {
                // Dispose existing factory if we're replacing it
                if (_isInitialized && _loggerFactory != null)
                {
                    _loggerFactory.Dispose();
                    _loggers.Clear();
                }

                _loggerFactory = loggerFactory ?? CreateDefaultLoggerFactory();
                _isInitialized = true;
            }
        }

        /// <summary>
        /// Logs an event with the specified event type and data.
        /// This method provides backward compatibility with the existing Diagnostics.Log system.
        /// </summary>
        /// <param name="eventType">
        /// The type/category of the event (e.g., "ServiceDiscovered", "TlsError", "BloomFilter.ServiceAdded").
        /// The event type is used to determine the appropriate log level and logger category.
        /// </param>
        /// <param name="data">
        /// The data to log. Can be any object - will be serialized to JSON for structured logging.
        /// Anonymous objects, complex types, and primitives are all supported.
        /// </param>
        /// <remarks>
        /// <para><strong>Log Level Detection:</strong></para>
        /// <para>The log level is automatically determined based on the event type:</para>
        /// <list type="bullet">
        /// <item><description><strong>Error:</strong> Event types containing "Error", "Exception", "Failed", "Failure"</description></item>
        /// <item><description><strong>Warning:</strong> Event types containing "Warning", "Warn", "Timeout", "Retry"</description></item>
        /// <item><description><strong>Debug:</strong> Event types containing "Debug", "Trace", "Verbose"</description></item>
        /// <item><description><strong>Information:</strong> All other event types (default)</description></item>
        /// </list>
        /// 
        /// <para><strong>Logger Category:</strong></para>
        /// <para>The logger category is derived from the event type. For example:</para>
        /// <list type="bullet">
        /// <item><description>"BloomFilter.ServiceAdded" → Category: "USDP2.BloomFilter"</description></item>
        /// <item><description>"TlsError" → Category: "USDP2.Tls"</description></item>
        /// <item><description>"ServiceDiscovered" → Category: "USDP2.Service"</description></item>
        /// </list>
        /// </remarks>
        /// <example>
        /// <code>
        /// // Simple logging
        /// UsdpLogger.Log("ServiceDiscovered", new { ServiceId = "home/lighting", Address = "*************" });
        /// 
        /// // Error logging
        /// UsdpLogger.Log("TlsConnectionError", new { Host = "example.com", Error = ex.Message });
        /// 
        /// // Complex object logging
        /// UsdpLogger.Log("BloomFilter.ServiceAdded", new { 
        ///     ServiceId = advertisement.ServiceId,
        ///     FilterSize = filter.Size,
        ///     EstimatedFalsePositiveRate = 0.01
        /// });
        /// </code>
        /// </example>
        public static void Log(string eventType, object? data = null)
        {
            if (!_isInitialized)
            {
                Initialize();
            }

            try
            {
                // Update metrics if enabled
                if (EnableMetrics)
                {
                    _eventCounts.AddOrUpdate(eventType, 1, (key, value) => value + 1);
                }

                // Determine log level based on event type
                var logLevel = DetermineLogLevel(eventType);

                // Skip if below minimum level
                if (logLevel < MinimumLogLevel)
                {
                    return;
                }

                // Get or create logger for this category
                var category = DetermineLoggerCategory(eventType);
                var logger = GetLogger(category);

                // Create structured log entry
                var logData = new Dictionary<string, object?>
                {
                    ["EventType"] = eventType,
                    ["Timestamp"] = DateTimeOffset.UtcNow,
                    ["Data"] = data
                };

                // Log with appropriate level
                logger.Log(logLevel, "USDP Event: {EventType} - {Data}",
                    eventType,
                    data != null ? JsonSerializer.Serialize(data, _logSerializerOptions) : null);
            }
            catch (Exception ex)
            {
                // Fallback logging - don't let logging failures break the application
                try
                {
                    var fallbackLogger = GetLogger("USDP2.Logging");
                    fallbackLogger.LogError(ex, "Failed to log event: {EventType}", eventType);
                }
                catch
                {
                    // Last resort - write to console only if enabled (disabled by default for class libraries)
                    if (EnableConsoleFallback)
                    {
                        Console.WriteLine($"[USDP2 Logging Error] Failed to log event '{eventType}': {ex.Message}");
                    }
                    // Otherwise, silently fail - appropriate for class libraries
                }
            }
        }

        /// <summary>
        /// Gets performance metrics for logged events.
        /// </summary>
        /// <returns>
        /// A dictionary containing event types as keys and their occurrence counts as values.
        /// Returns an empty dictionary if metrics are disabled.
        /// </returns>
        public static IReadOnlyDictionary<string, long> GetMetrics()
        {
            return EnableMetrics ? _eventCounts.ToArray().ToDictionary(kvp => kvp.Key, kvp => kvp.Value) :
                new Dictionary<string, long>();
        }

        /// <summary>
        /// Clears all collected performance metrics.
        /// </summary>
        public static void ClearMetrics()
        {
            _eventCounts.Clear();
        }

        /// <summary>
        /// Gets a logger for the specified category.
        /// </summary>
        /// <param name="category">The logger category name.</param>
        /// <returns>An ILogger instance for the specified category.</returns>
        public static ILogger GetLogger(string category)
        {
            if (!_isInitialized)
            {
                Initialize();
            }

            return _loggers.GetOrAdd(category, cat => _loggerFactory!.CreateLogger(cat));
        }

        /// <summary>
        /// Gets a logger for the specified type.
        /// </summary>
        /// <typeparam name="T">The type to create a logger for.</typeparam>
        /// <returns>An ILogger instance for the specified type.</returns>
        public static ILogger<T> GetLogger<T>()
        {
            if (!_isInitialized)
            {
                Initialize();
            }

            return _loggerFactory!.CreateLogger<T>();
        }

        /// <summary>
        /// Disposes the logger factory and clears all cached loggers.
        /// </summary>
        public static void Dispose()
        {
            lock (_initializationLock)
            {
                _loggerFactory?.Dispose();
                _loggers.Clear();
                _eventCounts.Clear();
                _isInitialized = false;
            }
        }

        /// <summary>
        /// Creates a default logger factory with providers based on configuration.
        ///
        /// Uses intelligent platform-specific provider selection with automatic fallback
        /// to file logging when system logging services are unavailable.
        /// </summary>
        private static ILoggerFactory CreateDefaultLoggerFactory()
        {
            var config = UsdpConfiguration.Instance;

            return config.LogMode switch
            {
                UsdpConfiguration.LoggingMode.Auto =>
                    PlatformLoggingProvider.CreatePlatformLoggerFactory(MinimumLogLevel, config.CustomLogFilePath),

                UsdpConfiguration.LoggingMode.Console =>
                    LoggerFactory.Create(builder => builder
                        .SetMinimumLevel(MinimumLogLevel)
                        .AddConsole(options =>
                        {
                            options.IncludeScopes = true;
                            options.TimestampFormat = "yyyy-MM-dd HH:mm:ss.fff ";
                        })
                        .AddDebug()),

                UsdpConfiguration.LoggingMode.File =>
                    PlatformLoggingProvider.CreatePlatformLoggerFactory(MinimumLogLevel, config.CustomLogFilePath),

                UsdpConfiguration.LoggingMode.Both =>
                    LoggerFactory.Create(builder => builder
                        .SetMinimumLevel(MinimumLogLevel)
                        .AddConsole(options =>
                        {
                            options.IncludeScopes = true;
                            options.TimestampFormat = "yyyy-MM-dd HH:mm:ss.fff ";
                        })
                        .AddDebug()),

                UsdpConfiguration.LoggingMode.EventLog =>
                    CreateEventLogFactory(),

                UsdpConfiguration.LoggingMode.Syslog =>
                    CreateSyslogFactory(),

                UsdpConfiguration.LoggingMode.None =>
                    LoggerFactory.Create(builder => builder
                        .SetMinimumLevel(LogLevel.Critical)
                        .AddDebug()),

                _ =>
                    PlatformLoggingProvider.CreatePlatformLoggerFactory(MinimumLogLevel, config.CustomLogFilePath)
            };
        }

        /// <summary>
        /// Creates a logger factory specifically configured for Windows Event Log.
        /// </summary>
        private static ILoggerFactory CreateEventLogFactory()
        {
            try
            {
                var config = UsdpConfiguration.Instance;
                return LoggerFactory.Create(builder => builder
                    .SetMinimumLevel(MinimumLogLevel)
                    .AddEventLog(options =>
                    {
                        options.SourceName = config.EventLogSourceName;
                        options.LogName = config.EventLogName;
                    })
                    .AddDebug());
            }
            catch
            {
                // Fallback to platform provider if Event Log is not available
                return PlatformLoggingProvider.CreatePlatformLoggerFactory(MinimumLogLevel, UsdpConfiguration.Instance.CustomLogFilePath);
            }
        }

        /// <summary>
        /// Creates a logger factory specifically configured for Syslog.
        /// </summary>
        private static ILoggerFactory CreateSyslogFactory()
        {
            try
            {
                // Use the platform provider which handles file-based logging
                // Applications can add syslog integration if needed
                var config = UsdpConfiguration.Instance;
                return PlatformLoggingProvider.CreatePlatformLoggerFactory(MinimumLogLevel, config.CustomLogFilePath);
            }
            catch
            {
                // Fallback to basic file logging
                return LoggerFactory.Create(builder => builder
                    .SetMinimumLevel(MinimumLogLevel)
                    .AddDebug());
            }
        }

        /// <summary>
        /// Determines the appropriate log level based on the event type.
        /// </summary>
        private static LogLevel DetermineLogLevel(string eventType)
        {
            var lowerEventType = eventType.ToLowerInvariant();

            // Error level indicators
            if (lowerEventType.Contains("error") ||
                lowerEventType.Contains("exception") ||
                lowerEventType.Contains("failed") ||
                lowerEventType.Contains("failure"))
            {
                return LogLevel.Error;
            }

            // Warning level indicators
            if (lowerEventType.Contains("warning") ||
                lowerEventType.Contains("warn") ||
                lowerEventType.Contains("timeout") ||
                lowerEventType.Contains("retry"))
            {
                return LogLevel.Warning;
            }

            // Debug level indicators
            if (lowerEventType.Contains("debug") ||
                lowerEventType.Contains("trace") ||
                lowerEventType.Contains("verbose"))
            {
                return LogLevel.Debug;
            }

            // Default to Information
            return LogLevel.Information;
        }

        /// <summary>
        /// Determines the logger category based on the event type.
        /// </summary>
        private static string DetermineLoggerCategory(string eventType)
        {
            // Handle dotted event types (e.g., "BloomFilter.ServiceAdded")
            if (eventType.Contains('.'))
            {
                var parts = eventType.Split('.');
                return $"USDP2.{parts[0]}";
            }

            // Extract category from event type
            var lowerEventType = eventType.ToLowerInvariant();

            if (lowerEventType.Contains("bloom")) return "USDP2.BloomFilter";
            if (lowerEventType.Contains("tls")) return "USDP2.Tls";
            if (lowerEventType.Contains("service")) return "USDP2.Service";
            if (lowerEventType.Contains("network") || lowerEventType.Contains("udp") || lowerEventType.Contains("http")) return "USDP2.Network";
            if (lowerEventType.Contains("chord")) return "USDP2.Chord";
            if (lowerEventType.Contains("dns")) return "USDP2.Dns";
            if (lowerEventType.Contains("auth") || lowerEventType.Contains("certificate")) return "USDP2.Authentication";
            if (lowerEventType.Contains("config")) return "USDP2.Configuration";
            if (lowerEventType.Contains("serializ")) return "USDP2.Serialization";

            // Default category
            return "USDP2.General";
        }
    }
}
