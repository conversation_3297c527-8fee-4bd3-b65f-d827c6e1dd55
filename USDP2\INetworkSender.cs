using System.Threading;
using System.Threading.Tasks;

namespace USDP2
{
    public interface INetworkSender
    {
        /// <summary>
        /// Sends the asynchronous.
        /// </summary>
        /// <param name="data">The data.</param>
        /// <param name="address">The address.</param>
        /// <param name="port">The port.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns></returns>
        Task SendAsync(byte[] data, string address, int port, CancellationToken cancellationToken = default);
    }
}