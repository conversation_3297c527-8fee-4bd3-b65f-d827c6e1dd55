using System.Threading;
using System.Threading.Tasks;

namespace USDP2
{
    /// <summary>
    /// Interface for secure transport implementations (e.g., TLS).
    /// </summary>
    public interface ISecureTransport
    {
        /// <summary>
        /// Secures the send asynchronous.
        /// </summary>
        /// <param name="data">The data.</param>
        /// <param name="address">The address.</param>
        /// <param name="port">The port.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns></returns>
        Task<byte[]> SecureSendAsync(byte[] data, string address, int port, CancellationToken cancellationToken = default);
        /// <summary>
        /// Secures the receive asynchronous.
        /// </summary>
        /// <param name="port">The port.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns></returns>
        Task<byte[]> SecureReceiveAsync(int port, CancellationToken cancellationToken = default);
    }
}