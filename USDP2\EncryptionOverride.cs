using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace USDP2
{
    /// <summary>
    /// Encryption algorithm options for the EncryptionOverride class.
    /// </summary>
    public enum EncryptionAlgorithm
    {
        /// <summary>
        /// AES-128-GCM: Lightweight, secure, good performance.
        /// Recommended for most use cases where performance is important.
        /// </summary>
        AES128GCM,

        /// <summary>
        /// AES-256-GCM: Maximum security, slightly higher overhead.
        /// Recommended for high-security environments or compliance requirements.
        /// </summary>
        AES256GCM
    }

    /// <summary>
    /// Provides optional encryption capabilities for persistent storage and configuration files.
    ///
    /// ⚠️  REMOVABLE CLASS NOTICE ⚠️
    /// This class is designed to be easily removable from the codebase if optional
    /// encryption functionality is not needed. It is completely isolated from
    /// core USDP2 functionality and only activated when EnableEncryptionOverride
    /// is set to true in the configuration.
    ///
    /// To remove this functionality:
    /// 1. Delete this file (EncryptionOverride.cs)
    /// 2. Remove references to EncryptionOverride in other classes
    /// 3. Set EnableEncryptionOverride to false in UsdpConfiguration.cs
    ///
    /// This class provides AES-GCM encryption with algorithm options:
    /// - AES-128-GCM: Lightweight, secure, good performance (recommended default)
    /// - AES-256-GCM: Maximum security, slightly higher overhead
    ///
    /// Supported data types:
    /// - Persistent service storage data
    /// - Configuration files containing sensitive information
    /// - Any other data that requires at-rest encryption
    ///
    /// The encryption uses the existing USDP2 key management infrastructure
    /// (KeyManagementProvider) for secure key storage and retrieval.
    /// </summary>
    public static class EncryptionOverride
    {
        private const string ENCRYPTION_KEY_NAME_128 = "USDP2_DataEncryption_Key_AES128";
        private const string ENCRYPTION_KEY_NAME_256 = "USDP2_DataEncryption_Key_AES256";
        private const int KEY_SIZE_128_BYTES = 16; // 128 bits
        private const int KEY_SIZE_256_BYTES = 32; // 256 bits
        private const int NONCE_SIZE_BYTES = 12; // 96 bits for GCM
        private const int TAG_SIZE_BYTES = 16; // 128 bits for GCM

        /// <summary>
        /// Gets the key size in bytes for the specified encryption algorithm.
        /// </summary>
        /// <param name="algorithm">The encryption algorithm.</param>
        /// <returns>The key size in bytes.</returns>
        private static int GetKeySize(EncryptionAlgorithm algorithm)
        {
            return algorithm switch
            {
                EncryptionAlgorithm.AES128GCM => KEY_SIZE_128_BYTES,
                EncryptionAlgorithm.AES256GCM => KEY_SIZE_256_BYTES,
                _ => throw new ArgumentException($"Unsupported encryption algorithm: {algorithm}")
            };
        }

        /// <summary>
        /// Gets the key name for the specified encryption algorithm.
        /// </summary>
        /// <param name="algorithm">The encryption algorithm.</param>
        /// <returns>The key name for storage.</returns>
        private static string GetKeyName(EncryptionAlgorithm algorithm)
        {
            return algorithm switch
            {
                EncryptionAlgorithm.AES128GCM => ENCRYPTION_KEY_NAME_128,
                EncryptionAlgorithm.AES256GCM => ENCRYPTION_KEY_NAME_256,
                _ => throw new ArgumentException($"Unsupported encryption algorithm: {algorithm}")
            };
        }

        /// <summary>
        /// Encrypts data using AES-GCM with a key from the configured key management backend.
        /// </summary>
        /// <param name="plaintext">The data to encrypt.</param>
        /// <param name="algorithm">The encryption algorithm to use (default: AES-128-GCM).</param>
        /// <param name="keyBackend">The key management backend to use.</param>
        /// <param name="keyNameOrPath">The key name or path for the backend (optional, uses algorithm-specific default).</param>
        /// <param name="vaultUri">The vault URI (for Azure Key Vault).</param>
        /// <param name="hashicorpToken">The HashiCorp Vault token.</param>
        /// <param name="gcpProjectId">The Google Cloud Project ID.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>The encrypted data with algorithm identifier, nonce and tag prepended.</returns>
        /// <exception cref="ArgumentNullException">Thrown when plaintext is null.</exception>
        /// <exception cref="CryptographicException">Thrown when encryption fails.</exception>
        public static async Task<byte[]> EncryptDataAsync(
            byte[] plaintext,
            EncryptionAlgorithm algorithm = EncryptionAlgorithm.AES128GCM,
            KeyManagementBackend keyBackend = KeyManagementBackend.WindowsDPAPI,
            string keyNameOrPath = "",
            string vaultUri = "",
            string hashicorpToken = "",
            string gcpProjectId = "",
            CancellationToken cancellationToken = default)
        {
            ArgumentNullException.ThrowIfNull(plaintext);

            try
            {
                // Use algorithm-specific key name if not provided
                var actualKeyName = string.IsNullOrEmpty(keyNameOrPath) ? GetKeyName(algorithm) : keyNameOrPath;

                // Get or create encryption key
                var encryptionKey = await GetOrCreateEncryptionKeyAsync(
                    algorithm, keyBackend, actualKeyName, vaultUri, hashicorpToken, gcpProjectId);

                // Generate random nonce
                var nonce = new byte[NONCE_SIZE_BYTES];
                using (var rng = RandomNumberGenerator.Create())
                {
                    rng.GetBytes(nonce);
                }

                // Encrypt using AES-GCM
                // Note: AesGcm requires .NET Core 3.0+ or .NET Framework 4.8+
                byte[] ciphertext;
                byte[] tag;

                try
                {
                    using var aes = new AesGcm(encryptionKey, TAG_SIZE_BYTES);
                    ciphertext = new byte[plaintext.Length];
                    tag = new byte[TAG_SIZE_BYTES];

                    aes.Encrypt(nonce, plaintext, ciphertext, tag);
                }
                catch (PlatformNotSupportedException ex)
                {
                    throw new CryptographicException("AES-GCM encryption is not supported on this platform. Requires .NET Core 3.0+ or .NET Framework 4.8+", ex);
                }

                // Combine algorithm identifier + nonce + tag + ciphertext for storage
                // Format: [1 byte algorithm] + [12 bytes nonce] + [16 bytes tag] + [N bytes ciphertext]
                var result = new byte[1 + NONCE_SIZE_BYTES + TAG_SIZE_BYTES + ciphertext.Length];
                result[0] = (byte)algorithm; // Store algorithm identifier
                Array.Copy(nonce, 0, result, 1, NONCE_SIZE_BYTES);
                Array.Copy(tag, 0, result, 1 + NONCE_SIZE_BYTES, TAG_SIZE_BYTES);
                Array.Copy(ciphertext, 0, result, 1 + NONCE_SIZE_BYTES + TAG_SIZE_BYTES, ciphertext.Length);

                UsdpLogger.Log("EncryptionOverride.DataEncrypted", new
                {
                    PlaintextSize = plaintext.Length,
                    EncryptedSize = result.Length,
                    Algorithm = algorithm.ToString(),
                    KeyBackend = keyBackend.ToString(),
                    Success = true
                });

                return result;
            }
            catch (Exception ex)
            {
                UsdpLogger.Log("EncryptionOverride.EncryptionFailed", new
                {
                    Error = ex.Message,
                    Algorithm = algorithm.ToString(),
                    KeyBackend = keyBackend.ToString(),
                    Success = false
                });
                throw new CryptographicException("Failed to encrypt data", ex);
            }
        }

        /// <summary>
        /// Decrypts data that was encrypted with EncryptDataAsync.
        /// The algorithm is automatically detected from the encrypted data.
        /// </summary>
        /// <param name="encryptedData">The encrypted data with algorithm identifier, nonce and tag prepended.</param>
        /// <param name="keyBackend">The key management backend to use.</param>
        /// <param name="keyNameOrPath">The key name or path for the backend (optional, uses algorithm-specific default).</param>
        /// <param name="vaultUri">The vault URI (for Azure Key Vault).</param>
        /// <param name="hashicorpToken">The HashiCorp Vault token.</param>
        /// <param name="gcpProjectId">The Google Cloud Project ID.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>The decrypted plaintext data.</returns>
        /// <exception cref="ArgumentNullException">Thrown when encryptedData is null.</exception>
        /// <exception cref="ArgumentException">Thrown when encryptedData is too short or has invalid format.</exception>
        /// <exception cref="CryptographicException">Thrown when decryption fails.</exception>
        public static async Task<byte[]> DecryptDataAsync(
            byte[] encryptedData,
            KeyManagementBackend keyBackend = KeyManagementBackend.WindowsDPAPI,
            string keyNameOrPath = "",
            string vaultUri = "",
            string hashicorpToken = "",
            string gcpProjectId = "",
            CancellationToken cancellationToken = default)
        {
            ArgumentNullException.ThrowIfNull(encryptedData);

            if (encryptedData.Length < 1 + NONCE_SIZE_BYTES + TAG_SIZE_BYTES)
            {
                throw new ArgumentException("Encrypted data is too short to contain algorithm identifier, nonce and tag", nameof(encryptedData));
            }

            try
            {
                // Extract algorithm identifier
                var algorithmByte = encryptedData[0];
                if (!Enum.IsDefined(typeof(EncryptionAlgorithm), (int)algorithmByte))
                {
                    throw new ArgumentException($"Invalid encryption algorithm identifier: {algorithmByte}", nameof(encryptedData));
                }

                var algorithm = (EncryptionAlgorithm)algorithmByte;

                // Use algorithm-specific key name if not provided
                var actualKeyName = string.IsNullOrEmpty(keyNameOrPath) ? GetKeyName(algorithm) : keyNameOrPath;

                // Get encryption key
                var encryptionKey = await GetOrCreateEncryptionKeyAsync(
                    algorithm, keyBackend, actualKeyName, vaultUri, hashicorpToken, gcpProjectId);

                // Extract nonce, tag, and ciphertext
                var nonce = new byte[NONCE_SIZE_BYTES];
                var tag = new byte[TAG_SIZE_BYTES];
                var ciphertext = new byte[encryptedData.Length - 1 - NONCE_SIZE_BYTES - TAG_SIZE_BYTES];

                Array.Copy(encryptedData, 1, nonce, 0, NONCE_SIZE_BYTES);
                Array.Copy(encryptedData, 1 + NONCE_SIZE_BYTES, tag, 0, TAG_SIZE_BYTES);
                Array.Copy(encryptedData, 1 + NONCE_SIZE_BYTES + TAG_SIZE_BYTES, ciphertext, 0, ciphertext.Length);

                // Decrypt using AES-GCM
                // Note: AesGcm requires .NET Core 3.0+ or .NET Framework 4.8+
                byte[] plaintext;

                try
                {
                    using var aes = new AesGcm(encryptionKey, TAG_SIZE_BYTES);
                    plaintext = new byte[ciphertext.Length];

                    aes.Decrypt(nonce, ciphertext, tag, plaintext);
                }
                catch (PlatformNotSupportedException ex)
                {
                    throw new CryptographicException("AES-GCM decryption is not supported on this platform. Requires .NET Core 3.0+ or .NET Framework 4.8+", ex);
                }

                UsdpLogger.Log("EncryptionOverride.DataDecrypted", new
                {
                    EncryptedSize = encryptedData.Length,
                    PlaintextSize = plaintext.Length,
                    Algorithm = algorithm.ToString(),
                    KeyBackend = keyBackend.ToString(),
                    Success = true
                });

                return plaintext;
            }
            catch (Exception ex)
            {
                UsdpLogger.Log("EncryptionOverride.DecryptionFailed", new
                {
                    Error = ex.Message,
                    KeyBackend = keyBackend.ToString(),
                    Success = false
                });
                throw new CryptographicException("Failed to decrypt data", ex);
            }
        }

        /// <summary>
        /// Encrypts a string using UTF-8 encoding and returns the encrypted data.
        /// </summary>
        /// <param name="plaintext">The string to encrypt.</param>
        /// <param name="algorithm">The encryption algorithm to use (default: AES-128-GCM).</param>
        /// <param name="keyBackend">The key management backend to use.</param>
        /// <param name="keyNameOrPath">The key name or path for the backend (optional, uses algorithm-specific default).</param>
        /// <param name="vaultUri">The vault URI (for Azure Key Vault).</param>
        /// <param name="hashicorpToken">The HashiCorp Vault token.</param>
        /// <param name="gcpProjectId">The Google Cloud Project ID.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>The encrypted data.</returns>
        public static async Task<byte[]> EncryptStringAsync(
            string plaintext,
            EncryptionAlgorithm algorithm = EncryptionAlgorithm.AES128GCM,
            KeyManagementBackend keyBackend = KeyManagementBackend.WindowsDPAPI,
            string keyNameOrPath = "",
            string vaultUri = "",
            string hashicorpToken = "",
            string gcpProjectId = "",
            CancellationToken cancellationToken = default)
        {
            ArgumentNullException.ThrowIfNull(plaintext);

            var plaintextBytes = Encoding.UTF8.GetBytes(plaintext);
            return await EncryptDataAsync(plaintextBytes, algorithm, keyBackend, keyNameOrPath, vaultUri, hashicorpToken, gcpProjectId, cancellationToken);
        }

        /// <summary>
        /// Decrypts data and returns it as a UTF-8 string.
        /// The algorithm is automatically detected from the encrypted data.
        /// </summary>
        /// <param name="encryptedData">The encrypted data.</param>
        /// <param name="keyBackend">The key management backend to use.</param>
        /// <param name="keyNameOrPath">The key name or path for the backend (optional, uses algorithm-specific default).</param>
        /// <param name="vaultUri">The vault URI (for Azure Key Vault).</param>
        /// <param name="hashicorpToken">The HashiCorp Vault token.</param>
        /// <param name="gcpProjectId">The Google Cloud Project ID.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>The decrypted string.</returns>
        public static async Task<string> DecryptStringAsync(
            byte[] encryptedData,
            KeyManagementBackend keyBackend = KeyManagementBackend.WindowsDPAPI,
            string keyNameOrPath = "",
            string vaultUri = "",
            string hashicorpToken = "",
            string gcpProjectId = "",
            CancellationToken cancellationToken = default)
        {
            var plaintextBytes = await DecryptDataAsync(encryptedData, keyBackend, keyNameOrPath, vaultUri, hashicorpToken, gcpProjectId, cancellationToken);
            return Encoding.UTF8.GetString(plaintextBytes);
        }

        /// <summary>
        /// Encrypts a file in place, replacing the original with the encrypted version.
        /// </summary>
        /// <param name="filePath">The path to the file to encrypt.</param>
        /// <param name="algorithm">The encryption algorithm to use (default: AES-128-GCM).</param>
        /// <param name="keyBackend">The key management backend to use.</param>
        /// <param name="keyNameOrPath">The key name or path for the backend (optional, uses algorithm-specific default).</param>
        /// <param name="vaultUri">The vault URI (for Azure Key Vault).</param>
        /// <param name="hashicorpToken">The HashiCorp Vault token.</param>
        /// <param name="gcpProjectId">The Google Cloud Project ID.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>A task representing the asynchronous operation.</returns>
        public static async Task EncryptFileAsync(
            string filePath,
            EncryptionAlgorithm algorithm = EncryptionAlgorithm.AES128GCM,
            KeyManagementBackend keyBackend = KeyManagementBackend.WindowsDPAPI,
            string keyNameOrPath = "",
            string vaultUri = "",
            string hashicorpToken = "",
            string gcpProjectId = "",
            CancellationToken cancellationToken = default)
        {
            ArgumentException.ThrowIfNullOrEmpty(filePath);

            if (!File.Exists(filePath))
            {
                throw new FileNotFoundException($"File not found: {filePath}");
            }

            try
            {
                var plaintext = await File.ReadAllBytesAsync(filePath, cancellationToken);
                var encrypted = await EncryptDataAsync(plaintext, algorithm, keyBackend, keyNameOrPath, vaultUri, hashicorpToken, gcpProjectId, cancellationToken);
                await File.WriteAllBytesAsync(filePath, encrypted, cancellationToken);

                UsdpLogger.Log("EncryptionOverride.FileEncrypted", new
                {
                    FilePath = filePath,
                    OriginalSize = plaintext.Length,
                    EncryptedSize = encrypted.Length,
                    Algorithm = algorithm.ToString(),
                    Success = true
                });
            }
            catch (Exception ex)
            {
                UsdpLogger.Log("EncryptionOverride.FileEncryptionFailed", new
                {
                    FilePath = filePath,
                    Error = ex.Message,
                    Success = false
                });
                throw;
            }
        }

        /// <summary>
        /// Decrypts a file in place, replacing the encrypted version with the plaintext.
        /// The algorithm is automatically detected from the encrypted file data.
        /// </summary>
        /// <param name="filePath">The path to the encrypted file to decrypt.</param>
        /// <param name="keyBackend">The key management backend to use.</param>
        /// <param name="keyNameOrPath">The key name or path for the backend (optional, uses algorithm-specific default).</param>
        /// <param name="vaultUri">The vault URI (for Azure Key Vault).</param>
        /// <param name="hashicorpToken">The HashiCorp Vault token.</param>
        /// <param name="gcpProjectId">The Google Cloud Project ID.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>A task representing the asynchronous operation.</returns>
        public static async Task DecryptFileAsync(
            string filePath,
            KeyManagementBackend keyBackend = KeyManagementBackend.WindowsDPAPI,
            string keyNameOrPath = "",
            string vaultUri = "",
            string hashicorpToken = "",
            string gcpProjectId = "",
            CancellationToken cancellationToken = default)
        {
            ArgumentException.ThrowIfNullOrEmpty(filePath);

            if (!File.Exists(filePath))
            {
                throw new FileNotFoundException($"File not found: {filePath}");
            }

            try
            {
                var encrypted = await File.ReadAllBytesAsync(filePath, cancellationToken);
                var plaintext = await DecryptDataAsync(encrypted, keyBackend, keyNameOrPath, vaultUri, hashicorpToken, gcpProjectId, cancellationToken);
                await File.WriteAllBytesAsync(filePath, plaintext, cancellationToken);

                UsdpLogger.Log("EncryptionOverride.FileDecrypted", new
                {
                    FilePath = filePath,
                    EncryptedSize = encrypted.Length,
                    PlaintextSize = plaintext.Length,
                    Success = true
                });
            }
            catch (Exception ex)
            {
                UsdpLogger.Log("EncryptionOverride.FileDecryptionFailed", new
                {
                    FilePath = filePath,
                    Error = ex.Message,
                    Success = false
                });
                throw;
            }
        }

        /// <summary>
        /// Checks if the encryption functionality is available and properly configured.
        /// </summary>
        /// <param name="algorithm">The encryption algorithm to check (default: AES-128-GCM).</param>
        /// <param name="keyBackend">The key management backend to check.</param>
        /// <param name="keyNameOrPath">The key name or path for the backend (optional, uses algorithm-specific default).</param>
        /// <param name="vaultUri">The vault URI (for Azure Key Vault).</param>
        /// <param name="hashicorpToken">The HashiCorp Vault token.</param>
        /// <param name="gcpProjectId">The Google Cloud Project ID.</param>
        /// <returns>True if encryption is available and configured, false otherwise.</returns>
        public static async Task<bool> IsEncryptionAvailableAsync(
            EncryptionAlgorithm algorithm = EncryptionAlgorithm.AES128GCM,
            KeyManagementBackend keyBackend = KeyManagementBackend.WindowsDPAPI,
            string keyNameOrPath = "",
            string vaultUri = "",
            string hashicorpToken = "",
            string gcpProjectId = "")
        {
            try
            {
                // Use algorithm-specific key name if not provided
                var actualKeyName = string.IsNullOrEmpty(keyNameOrPath) ? GetKeyName(algorithm) : keyNameOrPath;
                var expectedKeySize = GetKeySize(algorithm);

                // Try to get or create an encryption key
                var key = await GetOrCreateEncryptionKeyAsync(algorithm, keyBackend, actualKeyName, vaultUri, hashicorpToken, gcpProjectId);
                return key != null && key.Length == expectedKeySize;
            }
            catch (Exception ex)
            {
                UsdpLogger.Log("EncryptionOverride.AvailabilityCheckFailed", new
                {
                    Algorithm = algorithm.ToString(),
                    KeyBackend = keyBackend.ToString(),
                    Error = ex.Message,
                    Available = false
                });
                return false;
            }
        }

        /// <summary>
        /// Rotates the encryption key by generating a new one and storing it.
        /// </summary>
        /// <param name="algorithm">The encryption algorithm to rotate the key for (default: AES-128-GCM).</param>
        /// <param name="keyBackend">The key management backend to use.</param>
        /// <param name="keyNameOrPath">The key name or path for the backend (optional, uses algorithm-specific default).</param>
        /// <param name="vaultUri">The vault URI (for Azure Key Vault).</param>
        /// <param name="hashicorpToken">The HashiCorp Vault token.</param>
        /// <param name="gcpProjectId">The Google Cloud Project ID.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>A task representing the asynchronous operation.</returns>
        public static Task RotateEncryptionKeyAsync(
            EncryptionAlgorithm algorithm = EncryptionAlgorithm.AES128GCM,
            KeyManagementBackend keyBackend = KeyManagementBackend.WindowsDPAPI,
            string keyNameOrPath = "",
            string vaultUri = "",
            string hashicorpToken = "",
            string gcpProjectId = "",
            CancellationToken cancellationToken = default)
        {
            try
            {
                // Use algorithm-specific key name if not provided
                var actualKeyName = string.IsNullOrEmpty(keyNameOrPath) ? GetKeyName(algorithm) : keyNameOrPath;
                var keySize = GetKeySize(algorithm);

                // Generate new encryption key
                var newKey = new byte[keySize];
                using (var rng = RandomNumberGenerator.Create())
                {
                    rng.GetBytes(newKey);
                }

                // Store the new key (only DPAPI is currently supported for saving)
                if (keyBackend == KeyManagementBackend.WindowsDPAPI)
                {
                    KeyManagementProvider.SaveKeyToDPAPI(actualKeyName, newKey);
                }
                else
                {
                    throw new NotSupportedException($"Saving keys is not currently supported for backend: {keyBackend}");
                }

                UsdpLogger.Log("EncryptionOverride.KeyRotated", new
                {
                    Algorithm = algorithm.ToString(),
                    KeyBackend = keyBackend.ToString(),
                    KeyName = actualKeyName,
                    KeySize = keySize,
                    Success = true
                });

                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                UsdpLogger.Log("EncryptionOverride.KeyRotationFailed", new
                {
                    Algorithm = algorithm.ToString(),
                    KeyBackend = keyBackend.ToString(),
                    KeyName = keyNameOrPath,
                    Error = ex.Message,
                    Success = false
                });
                return Task.FromException(ex);
            }
        }

        #region Private Helper Methods

        /// <summary>
        /// Gets an existing encryption key or creates a new one if it doesn't exist.
        /// </summary>
        /// <param name="algorithm">The encryption algorithm.</param>
        /// <param name="keyBackend">The key management backend to use.</param>
        /// <param name="keyNameOrPath">The key name or path for the backend.</param>
        /// <param name="vaultUri">The vault URI (for Azure Key Vault).</param>
        /// <param name="hashicorpToken">The HashiCorp Vault token.</param>
        /// <param name="gcpProjectId">The Google Cloud Project ID.</param>
        /// <returns>The encryption key.</returns>
        private static async Task<byte[]> GetOrCreateEncryptionKeyAsync(
            EncryptionAlgorithm algorithm,
            KeyManagementBackend keyBackend,
            string keyNameOrPath,
            string vaultUri,
            string hashicorpToken,
            string gcpProjectId)
        {
            try
            {
                // Try to load existing key
                return await KeyManagementProvider.LoadKeyAsync(keyBackend, keyNameOrPath, vaultUri, hashicorpToken, gcpProjectId);
            }
            catch (Exception)
            {
                // Key doesn't exist, create a new one
                var keySize = GetKeySize(algorithm);
                var newKey = new byte[keySize];
                using (var rng = RandomNumberGenerator.Create())
                {
                    rng.GetBytes(newKey);
                }

                // Store the new key (only DPAPI is currently supported for saving)
                if (keyBackend == KeyManagementBackend.WindowsDPAPI)
                {
                    KeyManagementProvider.SaveKeyToDPAPI(keyNameOrPath, newKey);
                }
                else
                {
                    throw new NotSupportedException($"Creating new keys is not currently supported for backend: {keyBackend}");
                }

                UsdpLogger.Log("EncryptionOverride.NewKeyCreated", new
                {
                    Algorithm = algorithm.ToString(),
                    KeyBackend = keyBackend.ToString(),
                    KeyName = keyNameOrPath,
                    KeySize = keySize,
                    Success = true
                });

                return newKey;
            }
        }

        #endregion
    }
}
