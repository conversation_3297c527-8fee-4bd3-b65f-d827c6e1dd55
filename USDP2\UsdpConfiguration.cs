using System;
using System.IO;
using Microsoft.Extensions.Logging;

namespace USDP2
{
    /// <summary>
    /// Centralized configuration management for the USDP2 (Universal Service Discovery Protocol) application.
    ///
    /// This class serves as the single source of truth for all configurable values throughout the USDP2 system,
    /// including network settings, timeouts, security parameters, and default values. By centralizing these
    /// configurations, we achieve better maintainability, consistency, and flexibility across the entire codebase.
    ///
    /// The configuration is implemented as a singleton to ensure consistent settings across all components
    /// and to provide a global access point for configuration values. All settings have sensible defaults
    /// that work out-of-the-box while allowing customization for specific deployment scenarios.
    ///
    /// Usage Examples:
    /// - var timeout = UsdpConfiguration.Instance.NetworkTimeout;
    /// - var port = UsdpConfiguration.Instance.DefaultMulticastPort;
    /// - var security = UsdpConfiguration.Instance.DefaultSecurity;
    ///
    /// Configuration Categories:
    /// 1. Logging Configuration - Controls diagnostic output and logging behavior
    /// 2. Service Advertisement Configuration - Settings for service announcements and TTL
    /// 3. Security Configuration - Default security protocols and authentication settings
    /// 4. TLS Configuration - Advanced TLS/SSL management and fallback mechanisms
    /// 5. Network Configuration - Ports, addresses, endpoints, and protocol settings
    /// 6. Protocol Configuration - Default protocols and service endpoint settings
    /// 7. Timeout Configuration - Various timeout values for network operations
    /// 8. File and Path Configuration - Configuration file paths and names
    /// 9. Buffer and Performance Configuration - Memory management and performance tuning
    /// 10. Example and Default Values - Default values used in examples and tests
    /// 11. Extensibility - Custom settings support for advanced scenarios
    ///
    /// TLS Security Features:
    /// - OS-Managed TLS: Delegates cipher suite selection to the operating system
    /// - Graceful Fallback: Automatic TLS version fallback with security boundaries
    /// - Manual Override: Advanced TLS configuration (removable module)
    /// - Comprehensive Logging: Detailed TLS connection monitoring and security events
    /// </summary>
    public sealed class UsdpConfiguration
    {
        /// <summary>
        /// Defines the available logging modes for the USDP2 diagnostic system.
        ///
        /// This enum supports various logging destinations and services commonly used
        /// in enterprise environments, development, and production deployments.
        /// </summary>
        public enum LoggingMode
        {
            /// <summary>
            /// Automatically select the best logging provider for the current platform.
            /// Windows: Event Log with file fallback
            /// Linux/Unix: Syslog with file fallback
            /// macOS: Unified Logging with file fallback
            /// Default for class libraries - appropriate for all environments
            /// </summary>
            Auto,

            /// <summary>No logging output (completely silent)</summary>
            None,

            /// <summary>Log to console/standard output only (suitable for development and console applications)</summary>
            Console,

            /// <summary>Log to files only (recommended for production file-based logging)</summary>
            File,

            /// <summary>Log to both console and files (useful for debugging production issues)</summary>
            Both,

            /// <summary>Log to Windows Event Log (Windows-specific, suitable for Windows services)</summary>
            EventLog,

            /// <summary>Log to Syslog (Unix/Linux standard, suitable for Linux services and containers)</summary>
            Syslog,

            /// <summary>Log to structured logging providers (JSON, supports ELK stack, Splunk, etc.)</summary>
            Structured,

            /// <summary>Log to Azure Application Insights (cloud-based telemetry and logging)</summary>
            ApplicationInsights,

            /// <summary>Log to AWS CloudWatch (Amazon cloud logging service)</summary>
            CloudWatch,

            /// <summary>Log to Google Cloud Logging (Google Cloud Platform logging service)</summary>
            CloudLogging,

            /// <summary>Log to OpenTelemetry providers (modern observability standard)</summary>
            OpenTelemetry,

            /// <summary>Log to custom providers configured by the application</summary>
            Custom
        }

        // Singleton implementation using Lazy<T> for thread-safe initialization
        // This ensures only one instance exists throughout the application lifecycle
        private static readonly Lazy<UsdpConfiguration> _instance = new(() => new UsdpConfiguration());

        /// <summary>
        /// Gets the singleton instance of the USDP configuration.
        ///
        /// This property provides thread-safe access to the global configuration instance.
        /// The instance is created lazily on first access and reused for all subsequent calls.
        ///
        /// Example usage:
        /// var config = UsdpConfiguration.Instance;
        /// var port = config.DefaultMulticastPort;
        /// </summary>
        public static UsdpConfiguration Instance => _instance.Value;

        #region Logging Configuration
        /*
         * ═══════════════════════════════════════════════════════════════════════════════════════
         * LOGGING CONFIGURATION
         * ═══════════════════════════════════════════════════════════════════════════════════════
         *
         * Controls how the USDP2 system outputs diagnostic information, error messages, and
         * operational logs. Proper logging configuration is crucial for debugging, monitoring,
         * and maintaining USDP2 deployments in production environments.
         *
         * Considerations:
         * - Console logging is ideal for development and debugging
         * - File logging is recommended for production deployments
         * - Both modes can be useful for troubleshooting production issues
         * - Log output includes network events, errors, performance metrics, and security events
         */

        /// <summary>
        /// Controls where diagnostic and operational logs are output.
        ///
        /// Default: Auto (automatically selects the best logging provider for the platform)
        ///
        /// Auto mode behavior:
        /// - Windows: Uses Event Log with file fallback to %TEMP%\USDP2\logs\
        /// - Linux/Unix: Uses Syslog with file fallback to /tmp/usdp2/logs/
        /// - macOS: Uses Unified Logging with file fallback to /tmp/usdp2/logs/
        /// - Containers: Uses structured logging with file fallback
        ///
        /// This setting controls both the legacy Diagnostics.Log output behavior and the
        /// default provider selection for the Microsoft.Extensions.Logging system (UsdpLogger).
        ///
        /// Available modes:
        /// - Auto: Automatically select best provider for platform (recommended)
        /// - None: No logging output (completely silent)
        /// - Console: Outputs logs to standard output (suitable for console applications)
        /// - File: Writes logs to disk using configurable file path
        /// - EventLog: Windows Event Log (Windows only)
        /// - Syslog: Unix/Linux syslog (Unix/Linux only)
        /// - Both: Console + File output (useful for debugging)
        ///
        /// Class library benefits of Auto mode:
        /// - No unwanted console output that pollutes application output
        /// - Uses platform-appropriate logging infrastructure
        /// - Graceful fallback to file logging if system logging unavailable
        /// - Integrates well with system monitoring and log aggregation
        /// - Respects platform conventions and administrator expectations
        ///
        /// The logging system captures:
        /// - Network connection events and errors
        /// - Service discovery operations (announcements, queries, responses)
        /// - Security events (authentication attempts, key operations)
        /// - Performance metrics (latency, throughput, resource usage)
        /// - Configuration changes and system state transitions
        /// </summary>
        public LoggingMode LogMode { get; set; } = LoggingMode.Auto;

        /// <summary>
        /// Controls the minimum log level for Microsoft.Extensions.Logging integration.
        ///
        /// Default: LogLevel.Information (balanced verbosity for production)
        /// Development recommendation: LogLevel.Debug or LogLevel.Trace
        ///
        /// This setting determines which log events are processed and output by the logging system.
        /// Events below this level are filtered out to reduce noise and improve performance.
        ///
        /// Log levels (from most to least verbose):
        /// - Trace: Very detailed logs, including data dumps and internal state
        /// - Debug: Detailed information for debugging, including method entry/exit
        /// - Information: General operational messages, service discoveries, connections
        /// - Warning: Potentially harmful situations, timeouts, retries, fallbacks
        /// - Error: Error events that don't stop the application, failed operations
        /// - Critical: Very serious errors that may cause application termination
        /// - None: Disables all logging (not recommended)
        ///
        /// Performance impact:
        /// - Lower levels (Trace/Debug) have higher overhead due to more log events
        /// - Higher levels (Warning/Error) have minimal performance impact
        /// - Consider the balance between observability and performance
        /// </summary>
        public LogLevel MinimumLogLevel { get; set; } = LogLevel.Information;

        /// <summary>
        /// Controls whether performance metrics collection is enabled in the logging system.
        ///
        /// Default: true (metrics enabled for monitoring and diagnostics)
        ///
        /// When enabled, the logging system collects performance metrics including:
        /// - Event occurrence counts by type
        /// - Timing information for operations
        /// - Resource usage statistics
        /// - Error rates and patterns
        ///
        /// Metrics are useful for:
        /// - Monitoring system health and performance
        /// - Identifying bottlenecks and optimization opportunities
        /// - Tracking error rates and reliability metrics
        /// - Capacity planning and scaling decisions
        ///
        /// Performance considerations:
        /// - Minimal overhead when enabled (thread-safe counters)
        /// - Metrics are stored in memory (consider memory usage for long-running applications)
        /// - Can be disabled in performance-critical scenarios
        /// - Metrics can be exported to monitoring systems (Prometheus, etc.)
        /// </summary>
        public bool EnableLoggingMetrics { get; set; } = true;

        /// <summary>
        /// Controls whether structured logging with JSON serialization is enabled.
        ///
        /// Default: true (structured logging enabled for better log analysis)
        ///
        /// Structured logging provides several benefits:
        /// - Machine-readable log format for automated analysis
        /// - Consistent data structure across all log events
        /// - Better integration with log aggregation systems (ELK, Splunk, etc.)
        /// - Easier filtering and searching of log data
        /// - Support for complex data types and nested objects
        ///
        /// When enabled:
        /// - Log data is serialized to JSON format
        /// - Complex objects are properly structured
        /// - Timestamps and metadata are consistently formatted
        /// - Log events can be easily parsed by external tools
        ///
        /// When disabled:
        /// - Simple string-based logging (faster but less structured)
        /// - Better human readability in console output
        /// - Reduced serialization overhead
        /// - May be preferred for development and debugging
        /// </summary>
        public bool EnableStructuredLogging { get; set; } = true;

        /// <summary>
        /// Controls whether console output is used as a last resort fallback when logging fails.
        ///
        /// Default: false (no console fallback - appropriate for class libraries)
        /// Recommendation: Enable only for applications, not for class libraries
        ///
        /// This setting controls the behavior when the logging system itself encounters errors.
        /// When enabled, critical logging failures will be written to the console as a last resort.
        ///
        /// Class library considerations:
        /// - Class libraries should NOT write directly to console
        /// - Console output may not be visible in many hosting environments
        /// - Applications should control their own output destinations
        /// - Silent failure is often preferable for libraries
        ///
        /// Application considerations:
        /// - May be useful for debugging logging configuration issues
        /// - Provides visibility when logging infrastructure fails
        /// - Should be disabled in production services/web applications
        /// - Appropriate for console applications and development tools
        ///
        /// Note: This setting is automatically configured by UsdpLogger.EnableConsoleFallback
        /// and is provided here for documentation and future configuration file support.
        /// </summary>
        public bool EnableConsoleFallback { get; set; }

        #region Logging Provider Configuration
        /*
         * ═══════════════════════════════════════════════════════════════════════════════════════
         * LOGGING PROVIDER CONFIGURATION
         * ═══════════════════════════════════════════════════════════════════════════════════════
         *
         * Configuration settings for various logging providers and services. These settings
         * allow USDP2 to integrate with different logging infrastructures commonly used in
         * enterprise environments, cloud platforms, and development scenarios.
         *
         * The Auto mode automatically selects the best provider for the current platform:
         * - Windows: Event Log with file fallback
         * - Linux/Unix: File logging (syslog integration can be added by applications)
         * - macOS: File logging (unified logging integration can be added by applications)
         * - Containers: Structured file logging
         */

        /// <summary>
        /// Custom log file path for file-based logging modes.
        ///
        /// Default: null (uses platform-appropriate default paths)
        ///
        /// When specified, this path overrides the default log file location.
        /// If null, the system uses platform-appropriate defaults:
        /// - Windows: %ProgramData%\USDP2\Logs\usdp2.log
        /// - Unix/Linux: /var/log/usdp2/usdp2.log (with fallback to /tmp/usdp2/logs/)
        ///
        /// The log file supports automatic rotation (daily) and retention (7 days)
        /// with a maximum file size of 10MB per file.
        /// </summary>
        public string? CustomLogFilePath { get; set; }

        /// <summary>
        /// Windows Event Log source name for EventLog mode.
        ///
        /// Default: "USDP2"
        ///
        /// This is the source name that appears in the Windows Event Log.
        /// The source must be registered with Windows before use, or the
        /// application must run with administrator privileges to auto-register.
        /// </summary>
        public string EventLogSourceName { get; set; } = "USDP2";

        /// <summary>
        /// Windows Event Log name for EventLog mode.
        ///
        /// Default: "Application"
        ///
        /// This specifies which Windows Event Log to write to. Common values:
        /// - "Application" (default, most common)
        /// - "System" (requires admin privileges)
        /// - Custom log name (requires registration)
        /// </summary>
        public string EventLogName { get; set; } = "Application";
        #endregion

        #region Circuit Breaker Configuration
        /*
         * ═══════════════════════════════════════════════════════════════════════════════════════
         * CIRCUIT BREAKER CONFIGURATION
         * ═══════════════════════════════════════════════════════════════════════════════════════
         *
         * Configuration settings for circuit breaker pattern implementation. Circuit breakers
         * provide resilience against cascading failures by monitoring operation success/failure
         * rates and temporarily blocking operations when failure thresholds are exceeded.
         *
         * Benefits:
         * - Prevents cascading failures in distributed systems
         * - Provides fast failure response during outages
         * - Allows automatic recovery testing
         * - Improves system resilience and user experience
         */

        /// <summary>
        /// Enables circuit breaker protection for network operations.
        ///
        /// Default: true (circuit breaker protection enabled)
        ///
        /// When enabled, network operations are protected by circuit breakers that monitor
        /// failure rates and provide fast-fail behavior during outages. This prevents
        /// cascading failures and improves system resilience.
        ///
        /// Circuit breaker benefits:
        /// - Automatic failure detection and fast-fail behavior
        /// - Prevents resource exhaustion during outages
        /// - Automatic recovery testing when services become available
        /// - Improved user experience with predictable failure modes
        /// - Comprehensive monitoring and statistics collection
        ///
        /// When disabled, network operations proceed without circuit breaker protection,
        /// which may lead to longer timeouts and cascading failures during network issues.
        /// </summary>
        public bool EnableCircuitBreakers { get; set; } = true;

        /// <summary>
        /// Default failure threshold for circuit breakers.
        ///
        /// Default: 5 failures
        ///
        /// This is the number of consecutive failures required to open a circuit breaker.
        /// Individual circuit breakers may override this value based on their specific
        /// requirements (e.g., UDP operations may use lower thresholds).
        ///
        /// Considerations:
        /// - Lower values: Faster failure detection, may be too sensitive
        /// - Higher values: More tolerance for transient failures, slower detection
        /// - Network type: Local networks may use higher thresholds than external services
        /// </summary>
        public int DefaultCircuitBreakerFailureThreshold { get; set; } = 5;

        /// <summary>
        /// Default open timeout for circuit breakers.
        ///
        /// Default: 30 seconds
        ///
        /// This is the duration to keep a circuit breaker open before transitioning
        /// to half-open state to test if the service has recovered.
        ///
        /// Considerations:
        /// - Shorter timeouts: Faster recovery testing, may overwhelm recovering services
        /// - Longer timeouts: More time for services to recover, slower user experience
        /// - Service type: External services may need longer timeouts than local services
        /// </summary>
        public TimeSpan DefaultCircuitBreakerOpenTimeout { get; set; } = TimeSpan.FromSeconds(30);

        /// <summary>
        /// Default success threshold for circuit breakers in half-open state.
        ///
        /// Default: 3 successes
        ///
        /// This is the number of successful operations required in half-open state
        /// to close the circuit breaker and resume normal operation.
        ///
        /// Considerations:
        /// - Lower values: Faster recovery, may close prematurely
        /// - Higher values: More confidence in recovery, slower to resume normal operation
        /// - Operation criticality: Critical operations may require higher thresholds
        /// </summary>
        public int DefaultCircuitBreakerSuccessThreshold { get; set; } = 3;

        /// <summary>
        /// Enables detailed circuit breaker monitoring and health checks.
        ///
        /// Default: true (monitoring enabled)
        ///
        /// When enabled, the circuit breaker manager performs periodic health checks
        /// and logs detailed statistics about circuit breaker states and performance.
        /// This provides valuable insights for system monitoring and troubleshooting.
        ///
        /// Monitoring includes:
        /// - Periodic health check summaries
        /// - Alerts for open circuits
        /// - Performance statistics and failure rates
        /// - State transition logging
        ///
        /// When disabled, only basic circuit breaker events are logged, reducing
        /// log volume but providing less visibility into system health.
        /// </summary>
        public bool EnableCircuitBreakerMonitoring { get; set; } = true;
        #endregion
        #endregion

        #region Service Advertisement Configuration
        /*
         * ═══════════════════════════════════════════════════════════════════════════════════════
         * SERVICE ADVERTISEMENT CONFIGURATION
         * ═══════════════════════════════════════════════════════════════════════════════════════
         *
         * Controls how services are advertised and discovered within the USDP2 network.
         * These settings affect service lifetime, cache behavior, and security requirements
         * for service announcements.
         *
         * Key concepts:
         * - TTL (Time-To-Live) determines how long service advertisements remain valid
         * - Authentication requirements control who can announce services
         * - Proper TTL values balance network efficiency with service availability
         */

        /// <summary>
        /// Default Time-To-Live (TTL) for service advertisements when not explicitly specified.
        ///
        /// Default: 5 minutes (300 seconds)
        /// Range: 1 second to MaxTtl (365 days)
        ///
        /// TTL determines how long a service advertisement remains valid in the network cache
        /// before it expires and needs to be refreshed. This value affects:
        ///
        /// Network Efficiency:
        /// - Shorter TTL = More frequent updates, higher network traffic, fresher data
        /// - Longer TTL = Less network traffic, potential stale data, better performance
        ///
        /// Use Cases:
        /// - Dynamic services (mobile devices): 1-5 minutes
        /// - Stable services (servers, infrastructure): 15-60 minutes
        /// - Static services (printers, fixed devices): 2-24 hours
        ///
        /// Setting to null means no default TTL is applied, requiring explicit TTL on each advertisement.
        /// </summary>
        public TimeSpan? DefaultTtl { get; set; } = TimeSpan.FromMinutes(5);

        /// <summary>
        /// Maximum allowed TTL for any service advertisement to prevent indefinite caching.
        ///
        /// Default: 365 days (1 year)
        /// Minimum: 1 second
        ///
        /// This limit prevents services from setting excessively long TTL values that could:
        /// - Cause memory leaks in service caches
        /// - Prevent proper service updates and removals
        /// - Lead to stale service information persisting indefinitely
        ///
        /// The system will reject any service advertisement with a TTL exceeding this value.
        /// For most deployments, the default of 365 days provides a reasonable upper bound
        /// while allowing for long-lived infrastructure services.
        /// </summary>
        public TimeSpan MaxTtl { get; set; } = TimeSpan.FromDays(365);

        /// <summary>
        /// Controls whether service announcements require authentication before being accepted.
        ///
        /// Default: true (authentication required)
        ///
        /// When enabled, all service announcements must be authenticated using the configured
        /// authentication provider (PSK, certificate, etc.) before being added to the service
        /// directory. This prevents unauthorized services from being announced and helps
        /// maintain network security.
        ///
        /// Security implications:
        /// - true: Higher security, prevents rogue service announcements, requires auth setup
        /// - false: Lower security, allows any service to announce, simpler deployment
        ///
        /// Recommended settings:
        /// - Production networks: true (with proper authentication infrastructure)
        /// - Development/testing: false (for easier testing and development)
        /// - Public networks: true (essential for security)
        /// - Private/trusted networks: configurable based on security requirements
        /// </summary>
        public bool RequireAuthentication { get; set; } = true;
        #endregion

        #region Security Configuration
        /*
         * ═══════════════════════════════════════════════════════════════════════════════════════
         * SECURITY CONFIGURATION
         * ═══════════════════════════════════════════════════════════════════════════════════════
         *
         * Defines the default security protocols and mechanisms used throughout the USDP2 system.
         * Security is critical for preventing unauthorized access, ensuring data integrity,
         * and maintaining trust in the service discovery network.
         *
         * Supported security protocols:
         * - "psk-tls1.3": Pre-Shared Key with TLS 1.3 (recommended for most deployments)
         * - "ed25519": Ed25519 digital signatures (high security, modern cryptography)
         * - "certificate": X.509 certificate-based authentication
         * - "none": No security (only for development/testing)
         *
         * TLS Configuration Features:
         * The USDP2 system includes advanced TLS configuration management with:
         * - OS-managed TLS for optimal security and compatibility
         * - Graceful fallback mechanisms for legacy system support
         * - Manual override capabilities for specialized requirements
         * - Comprehensive security logging and monitoring
         *
         * See the TLS Configuration section below for detailed TLS-specific settings.
         */

        /// <summary>
        /// Default security protocol used for service authentication and communication encryption.
        ///
        /// Default: "psk-tls1.3" (Pre-Shared Key with TLS 1.3)
        ///
        /// This setting determines the default security mechanism used when no explicit security
        /// protocol is specified for a service or communication channel. The security protocol
        /// affects both authentication (verifying identity) and encryption (protecting data).
        ///
        /// Available protocols:
        ///
        /// "psk-tls1.3" (Recommended):
        /// - Uses pre-shared keys for authentication
        /// - TLS 1.3 for encryption and integrity
        /// - Good balance of security and performance
        /// - Suitable for most enterprise deployments
        /// - Requires shared secret distribution
        ///
        /// "ed25519":
        /// - Modern elliptic curve digital signatures
        /// - Excellent security properties
        /// - Fast signature generation and verification
        /// - Requires public key infrastructure
        /// - Ideal for high-security environments
        ///
        /// "certificate":
        /// - X.509 certificate-based authentication
        /// - PKI infrastructure required
        /// - Industry standard for enterprise environments
        /// - Supports certificate revocation and hierarchies
        ///
        /// "none":
        /// - No security mechanisms applied
        /// - Only suitable for development and testing
        /// - Should NEVER be used in production
        /// - Provides no protection against attacks
        ///
        /// Security considerations:
        /// - Choose based on your threat model and infrastructure capabilities
        /// - Ensure proper key/certificate management procedures
        /// - Regularly rotate keys and update certificates
        /// - Monitor for security events and anomalies
        /// </summary>
        public string DefaultSecurity { get; set; } = "psk-tls1.3";
        #endregion

        #region Network Configuration
        /*
         * ═══════════════════════════════════════════════════════════════════════════════════════
         * NETWORK CONFIGURATION
         * ═══════════════════════════════════════════════════════════════════════════════════════
         *
         * Controls all network-related settings including ports, addresses, endpoints, and
         * protocol preferences. These settings determine how USDP2 components communicate
         * with each other across the network.
         *
         * Network architecture:
         * - Local discovery uses UDP multicast for efficiency within subnets
         * - Global discovery uses HTTP/HTTPS for internet-wide service discovery
         * - Different ports are used for different protocols to avoid conflicts
         * - Endpoint paths allow integration with existing web infrastructure
         */

        /// <summary>
        /// IPv4 multicast address used for local subnet service discovery.
        ///
        /// Default: "***************" (SSDP/UPnP standard multicast address)
        /// Range: ********* to *************** (IPv4 multicast range)
        ///
        /// This address is used for UDP multicast communication within local network segments.
        /// All USDP2 nodes on the same subnet will join this multicast group to send and
        /// receive service announcements and queries.
        ///
        /// Standard multicast addresses:
        /// - ***************: SSDP/UPnP (recommended, widely supported)
        /// - ***********: mDNS (alternative, but may conflict with Bonjour)
        /// - ***************: Custom USDP (if avoiding conflicts is needed)
        ///
        /// Network considerations:
        /// - Must be in the IPv4 multicast range (*********/4)
        /// - Should not conflict with other multicast services
        /// - Network infrastructure must support multicast routing
        /// - Firewalls must allow multicast traffic on this address
        /// - Some networks may block or filter multicast traffic
        ///
        /// Troubleshooting:
        /// - If local discovery fails, verify multicast support
        /// - Check firewall rules for multicast traffic
        /// - Ensure network switches support IGMP
        /// - Test with network diagnostic tools (ping, tcpdump)
        /// </summary>
        public string DefaultMulticastAddress { get; set; } = "***************";

        /// <summary>
        /// UDP port number used for multicast service discovery communication.
        ///
        /// Default: 5353 (mDNS standard port, widely supported)
        /// Range: 1024-65535 (avoid well-known ports below 1024)
        ///
        /// This port is used for both sending and receiving UDP multicast packets
        /// for local service discovery. All USDP2 nodes must use the same port
        /// to communicate effectively.
        ///
        /// Standard service discovery ports:
        /// - 5353: mDNS/Bonjour (recommended, industry standard)
        /// - 1900: SSDP/UPnP (alternative, but may conflict)
        /// - 5355: LLMNR (avoid, used by Windows)
        /// - Custom: Any available port above 1024
        ///
        /// Port selection considerations:
        /// - Avoid ports used by other services (check /etc/services)
        /// - Consider firewall rules and port availability
        /// - Higher ports (>10000) may have fewer conflicts
        /// - Document port usage for network administrators
        /// - Ensure consistent configuration across all nodes
        /// </summary>
        public int DefaultMulticastPort { get; set; } = 5353;

        /// <summary>
        /// TCP port number used for HTTP-based global service discovery.
        ///
        /// Default: 8080 (common alternative HTTP port)
        /// Range: 1024-65535 (avoid well-known ports below 1024)
        ///
        /// This port is used for HTTP communication when HTTPS is disabled or
        /// for fallback scenarios. HTTP provides broader compatibility but
        /// lacks encryption and should only be used in trusted environments.
        ///
        /// Common HTTP ports:
        /// - 80: Standard HTTP (requires root/admin privileges)
        /// - 8080: Alternative HTTP (recommended for applications)
        /// - 8000: Development HTTP (common for testing)
        /// - 3000: Node.js/React development (avoid in production)
        ///
        /// Security considerations:
        /// - HTTP traffic is unencrypted and visible to network observers
        /// - Suitable only for trusted networks or development environments
        /// - Consider using HTTPS (DefaultHttpsPort) for production deployments
        /// - Implement additional security measures if HTTP is required
        /// </summary>
        public int DefaultHttpPort { get; set; } = 8080;

        /// <summary>
        /// TCP port number used for HTTPS-based secure global service discovery.
        ///
        /// Default: 8443 (common alternative HTTPS port)
        /// Range: 1024-65535 (avoid well-known ports below 1024)
        ///
        /// This port is used for encrypted HTTP communication, providing security
        /// for service discovery traffic over untrusted networks. HTTPS is the
        /// recommended protocol for production deployments.
        ///
        /// Common HTTPS ports:
        /// - 443: Standard HTTPS (requires root/admin privileges)
        /// - 8443: Alternative HTTPS (recommended for applications)
        /// - 9443: Secondary HTTPS (if 8443 is unavailable)
        /// - 8080: HTTP over TLS (non-standard but sometimes used)
        ///
        /// TLS/SSL considerations:
        /// - Requires valid SSL/TLS certificates
        /// - Certificate management and renewal procedures needed
        /// - Performance impact from encryption/decryption
        /// - Firewall rules must allow HTTPS traffic
        /// - Consider certificate pinning for enhanced security
        /// </summary>
        public int DefaultHttpsPort { get; set; } = 8443;

        /// <summary>
        /// URL path component for HTTP-based USDP service discovery endpoints.
        ///
        /// Default: "/usdp" (clear, descriptive path)
        /// Format: Must start with "/" and contain valid URL characters
        ///
        /// This path is appended to HTTP URLs when sending service discovery
        /// requests. It allows USDP2 to coexist with other web services on
        /// the same server by using a dedicated endpoint path.
        ///
        /// Example URLs:
        /// - http://server:8080/usdp (service discovery endpoint)
        /// - http://server:8080/api (other application endpoints)
        /// - http://server:8080/health (health check endpoint)
        ///
        /// Path design considerations:
        /// - Should be descriptive and indicate USDP functionality
        /// - Avoid conflicts with existing application paths
        /// - Keep short to minimize URL length
        /// - Use lowercase for consistency
        /// - Consider versioning (e.g., "/usdp/v1", "/usdp/v2")
        ///
        /// Integration with web servers:
        /// - Configure reverse proxy rules if needed
        /// - Set up appropriate routing in web frameworks
        /// - Consider rate limiting and access controls
        /// - Implement proper error handling and responses
        /// </summary>
        public string HttpEndpointPath { get; set; } = "/usdp";

        /// <summary>
        /// URL path component for HTTPS-based USDP service discovery endpoints.
        ///
        /// Default: "/usdp" (same as HTTP for consistency)
        /// Format: Must start with "/" and contain valid URL characters
        ///
        /// This path is used for secure HTTPS communication. While it defaults
        /// to the same path as HTTP, it can be configured differently to
        /// implement different security policies or routing rules.
        ///
        /// Security-specific path considerations:
        /// - May use different paths for different security levels
        /// - Can implement path-based access controls
        /// - Allows separate monitoring and logging
        /// - Enables different rate limiting policies
        ///
        /// Example configurations:
        /// - Same path: HttpEndpointPath = HttpsEndpointPath = "/usdp"
        /// - Different paths: HttpEndpointPath = "/usdp", HttpsEndpointPath = "/secure/usdp"
        /// - Versioned paths: "/usdp/v1" vs "/usdp/v2"
        /// </summary>
        public string HttpsEndpointPath { get; set; } = "/usdp";

        /// <summary>
        /// Controls whether HTTPS is preferred over HTTP for network communication.
        ///
        /// Default: true (HTTPS preferred for security)
        ///
        /// When enabled, the HTTP network sender will use HTTPS by default,
        /// providing encryption and authentication for service discovery traffic.
        /// This setting affects the default behavior but can be overridden
        /// per-connection if needed.
        ///
        /// Security implications:
        /// true (HTTPS):
        /// - Encrypted communication protects against eavesdropping
        /// - Server authentication prevents man-in-the-middle attacks
        /// - Integrity protection ensures data is not tampered with
        /// - Required for compliance with security standards
        /// - Higher CPU usage due to encryption overhead
        /// - Requires certificate management
        ///
        /// false (HTTP):
        /// - Faster performance due to no encryption overhead
        /// - Simpler deployment without certificate requirements
        /// - Traffic is visible to network observers
        /// - Vulnerable to man-in-the-middle attacks
        /// - Only suitable for trusted networks
        ///
        /// Deployment recommendations:
        /// - Production: true (always use HTTPS)
        /// - Development: false (for simplicity, if network is trusted)
        /// - Testing: configurable based on test scenarios
        /// - Public networks: true (essential for security)
        /// </summary>
        public bool UseHttps { get; set; } = true;
        #endregion

        #region TLS Configuration
        /*
         * ═══════════════════════════════════════════════════════════════════════════════════════
         * TLS CONFIGURATION
         * ═══════════════════════════════════════════════════════════════════════════════════════
         *
         * Advanced TLS/SSL configuration management for secure communication. This section
         * provides comprehensive control over TLS behavior, from OS-managed defaults to
         * advanced manual configuration options.
         *
         * TLS Configuration Architecture:
         * 1. OS-Managed TLS (Default): Delegates cipher suite and TLS version selection
         *    to the operating system for optimal security and compatibility
         * 2. Graceful Fallback: Automatic TLS version fallback with configurable security boundaries
         * 3. Manual Override: Advanced configuration for specialized requirements (removable module)
         *
         * Key Features:
         * - Automatic security updates through OS patches
         * - Hardware acceleration when available (AES-NI, etc.)
         * - Platform-optimized cipher suite ordering
         * - Comprehensive security logging and monitoring
         * - Configurable fallback mechanisms for legacy compatibility
         * - Manual override capabilities for compliance requirements
         *
         * Platform-Specific Implementations:
         * - Windows: Uses Schannel with automatic cipher suite management
         * - Linux: Uses OpenSSL with distribution-maintained configurations
         * - macOS: Uses Secure Transport with Apple's security policies
         * - Cross-platform: .NET provides consistent abstraction layer
         *
         * Security Best Practices:
         * - Use OS-managed TLS for most deployments (UseOSManagedTls = true)
         * - Enable fallback for compatibility (EnableTlsFallback = true)
         * - Maintain security boundaries (AllowTlsDowngrade = false)
         * - Monitor TLS usage and fallback events
         * - Only enable manual override when absolutely necessary
         */

        /// <summary>
        /// Controls whether to use operating system managed TLS/SSL configuration.
        ///
        /// Default: true (OS-managed TLS for optimal compatibility and security)
        ///
        /// When enabled, the application delegates TLS version selection and cipher suite
        /// management to the operating system. This provides the best balance of security,
        /// compatibility, and maintainability as the OS vendors maintain up-to-date
        /// security configurations and handle platform-specific optimizations.
        ///
        /// Benefits of OS-managed TLS:
        /// - Automatic security updates through OS patches
        /// - Hardware acceleration when available
        /// - Platform-optimized cipher suite ordering
        /// - Compliance with current security standards
        /// - Global compatibility testing by OS vendors
        /// - Zero-configuration security
        ///
        /// OS-specific implementations:
        /// - Windows: Uses Schannel with automatic cipher suite management
        /// - Linux: Uses OpenSSL with distribution-maintained configurations
        /// - macOS: Uses Secure Transport with Apple's security policies
        /// - Cross-platform: .NET provides consistent abstraction layer
        ///
        /// When disabled, the application may use manual TLS configuration
        /// (if EnableManualTlsOverride is true) or basic .NET defaults.
        /// </summary>
        public bool UseOSManagedTls { get; set; } = true;

        /// <summary>
        /// Enables graceful TLS fallback mechanisms for enhanced compatibility.
        ///
        /// Default: true (fallback enabled for maximum compatibility)
        ///
        /// When enabled, the application will attempt to establish secure connections
        /// using the highest available TLS version, and gracefully fall back to older
        /// versions if the initial connection fails. This improves compatibility with
        /// legacy systems while maintaining security where possible.
        ///
        /// Fallback behavior:
        /// 1. Attempt connection with OS-preferred TLS version (typically TLS 1.3)
        /// 2. If connection fails, retry with TLS 1.2 (if allowed)
        /// 3. Log all fallback attempts for monitoring and analysis
        /// 4. Respect AllowTlsDowngrade setting for security boundaries
        ///
        /// Security considerations:
        /// - Fallback attempts are logged for security monitoring
        /// - AllowTlsDowngrade controls minimum acceptable TLS version
        /// - Failed connections are retried with progressively older versions
        /// - Each fallback attempt includes security event logging
        ///
        /// Compatibility benefits:
        /// - Improved connectivity with legacy systems
        /// - Reduced connection failures in mixed environments
        /// - Automatic adaptation to endpoint capabilities
        /// - Graceful handling of TLS version mismatches
        /// </summary>
        public bool EnableTlsFallback { get; set; } = true;

        /// <summary>
        /// Controls whether TLS version downgrade is permitted during fallback.
        ///
        /// Default: false (security-conscious default, no downgrade allowed)
        ///
        /// This setting provides fine-grained control over TLS fallback behavior.
        /// When false, the application will only attempt connections with TLS 1.2
        /// or higher, ensuring a minimum security baseline. When true, the application
        /// may fall back to older TLS versions for maximum compatibility.
        ///
        /// Security implications:
        /// false (recommended for production):
        /// - Maintains minimum TLS 1.2 security baseline
        /// - Prevents connections to systems with weak TLS implementations
        /// - Reduces attack surface from protocol downgrade attacks
        /// - Ensures compliance with modern security standards
        ///
        /// true (use with caution):
        /// - Allows connections to legacy systems with older TLS versions
        /// - May permit TLS 1.1 or 1.0 connections in extreme cases
        /// - Increases compatibility at the cost of security
        /// - Should only be used in controlled, trusted environments
        ///
        /// Monitoring recommendations:
        /// - Enable comprehensive logging when allowing downgrades
        /// - Monitor for actual downgrade events in production
        /// - Regular review of systems requiring TLS downgrades
        /// - Plan migration away from systems requiring weak TLS
        /// </summary>
        public bool AllowTlsDowngrade { get; set; }

        /// <summary>
        /// Specifies the TLS versions to attempt during fallback operations.
        ///
        /// Default: ["1.3", "1.2"] (modern TLS versions only)
        ///
        /// This array defines the TLS versions that the fallback mechanism will
        /// attempt, in order of preference. The application will try each version
        /// in sequence until a successful connection is established or all options
        /// are exhausted.
        ///
        /// Supported values:
        /// - "1.3": TLS 1.3 (recommended, highest security)
        /// - "1.2": TLS 1.2 (widely supported, good security)
        /// - "1.1": TLS 1.1 (deprecated, use only if absolutely necessary)
        /// - "1.0": TLS 1.0 (deprecated, significant security concerns)
        ///
        /// Security recommendations:
        /// - Production: ["1.3", "1.2"] (modern versions only)
        /// - Legacy compatibility: ["1.3", "1.2", "1.1"] (with monitoring)
        /// - High security: ["1.3"] (TLS 1.3 only)
        /// - Development: configurable based on test requirements
        ///
        /// The order in this array determines the preference order for fallback
        /// attempts. Always list the most secure/preferred versions first.
        /// </summary>
        public string[] FallbackTlsVersions { get; set; } = { "1.3", "1.2" };

        /// <summary>
        /// Enables manual TLS override functionality for advanced configuration.
        ///
        /// Default: false (manual override disabled for security and simplicity)
        ///
        /// When enabled, this setting allows the use of the TlsOverrideProvider class
        /// for manual TLS configuration. This provides advanced users with the ability
        /// to specify custom cipher suites, TLS versions, and other SSL/TLS parameters
        /// that override the default OS-managed configuration.
        ///
        /// Use cases for manual override:
        /// - Compliance requirements for specific cipher suites
        /// - Testing with particular TLS configurations
        /// - Integration with systems requiring specific TLS settings
        /// - Advanced security hardening scenarios
        /// - Debugging TLS connectivity issues
        ///
        /// Security considerations:
        /// - Manual configuration requires deep TLS/SSL expertise
        /// - Incorrect settings can weaken security significantly
        /// - Regular review and updates of manual configurations required
        /// - Should be used only when OS-managed TLS is insufficient
        /// - Comprehensive testing required when enabled
        ///
        /// Implementation note:
        /// The TlsOverrideProvider class is designed to be easily removable
        /// from the codebase if manual override functionality is not needed.
        /// When this setting is false, the override provider is not used,
        /// and the class can be safely deleted without affecting core functionality.
        /// </summary>
        public bool EnableManualTlsOverride { get; set; }

        /// <summary>
        /// Enables low security TLS fallback for compatibility with legacy systems.
        ///
        /// Default: false (low security TLS fallback disabled)
        /// ⚠️ SECURITY WARNING: This setting enables deprecated TLS versions (TLS 1.0/1.1)
        ///
        /// When enabled, allows fallback to deprecated TLS versions (TLS 1.0 and TLS 1.1)
        /// for compatibility with legacy systems that do not support modern TLS versions.
        /// This setting should only be enabled in controlled environments where legacy
        /// compatibility is absolutely required.
        ///
        /// Deprecated TLS versions enabled when this setting is true:
        /// - TLS 1.0 (RFC 2246) - Deprecated, multiple known vulnerabilities
        /// - TLS 1.1 (RFC 4346) - Deprecated, limited security improvements over TLS 1.0
        ///
        /// Security risks when enabled:
        /// - Vulnerable to BEAST attacks (TLS 1.0)
        /// - Weak cipher suites may be negotiated
        /// - Limited protection against downgrade attacks
        /// - Non-compliance with modern security standards
        /// - Potential regulatory compliance issues
        ///
        /// When to enable (use with extreme caution):
        /// - Legacy system integration that cannot be upgraded
        /// - Temporary compatibility during system migration
        /// - Controlled internal networks with additional security layers
        /// - Development/testing environments only
        ///
        /// Security mitigations when enabled:
        /// - Use only in isolated network segments
        /// - Implement additional application-layer security
        /// - Monitor all connections for security anomalies
        /// - Plan immediate migration to modern TLS versions
        /// - Regular security assessments and penetration testing
        ///
        /// Compliance considerations:
        /// - May violate PCI DSS requirements
        /// - May not meet HIPAA security standards
        /// - Check regulatory requirements before enabling
        /// - Document business justification for enabling
        ///
        /// Recommended: Keep disabled unless absolutely required for legacy compatibility
        /// </summary>
        public bool EnableLowSecurityTlsFallback { get; set; }

        /// <summary>
        /// Gets or sets whether optional encryption override functionality is enabled.
        ///
        /// Default: false (encryption override disabled)
        ///
        /// When enabled, the EncryptionOverride class provides AES-256-GCM encryption
        /// capabilities for persistent storage data and configuration files. This is
        /// an optional security enhancement that can be used when additional at-rest
        /// encryption is required beyond the standard security mechanisms.
        ///
        /// Features when enabled:
        /// - AES-256-GCM encryption for persistent service storage
        /// - Configuration file encryption for sensitive data
        /// - Integration with existing USDP2 key management infrastructure
        /// - Support for all configured key management backends
        /// - Automatic key generation and rotation capabilities
        ///
        /// Performance considerations:
        /// - Additional CPU overhead for encryption/decryption operations
        /// - Slight increase in storage size due to encryption metadata
        /// - Key management operations may introduce latency
        /// - Memory usage increase for cryptographic operations
        ///
        /// Security benefits:
        /// - Protection of service discovery data at rest
        /// - Encryption of sensitive configuration information
        /// - Defense against unauthorized file system access
        /// - Compliance with data protection regulations
        ///
        /// Implementation note:
        /// The EncryptionOverride class is designed to be easily removable
        /// from the codebase if optional encryption functionality is not needed.
        /// When this setting is false, the encryption override is not used,
        /// and the class can be safely deleted without affecting core functionality.
        ///
        /// To remove this functionality:
        /// 1. Set this property to false
        /// 2. Delete the EncryptionOverride.cs file
        /// 3. Remove any references to EncryptionOverride in other classes
        ///
        /// Recommended: Enable for deployments requiring enhanced data protection
        /// </summary>
        public bool EnableEncryptionOverride { get; set; }
        #endregion

        #region Protocol Configuration
        /*
         * ═══════════════════════════════════════════════════════════════════════════════════════
         * PROTOCOL CONFIGURATION
         * ═══════════════════════════════════════════════════════════════════════════════════════
         *
         * Defines default protocols and endpoint settings for services discovered through USDP2.
         * These settings determine how clients connect to discovered services and what
         * communication protocols are used by default.
         */

        /// <summary>
        /// Default communication protocol used by discovered services.
        ///
        /// Default: "coap+udp" (Constrained Application Protocol over UDP)
        ///
        /// This protocol string indicates how clients should communicate with discovered
        /// services. It follows the format "protocol+transport" to specify both the
        /// application protocol and underlying transport mechanism.
        ///
        /// Supported protocol formats:
        /// - "coap+udp": CoAP over UDP (recommended for IoT and constrained devices)
        /// - "http+tcp": HTTP over TCP (standard web services)
        /// - "https+tcp": HTTPS over TCP (secure web services)
        /// - "mqtt+tcp": MQTT over TCP (message queuing for IoT)
        /// - "websocket+tcp": WebSocket over TCP (real-time communication)
        /// - "grpc+tcp": gRPC over TCP (high-performance RPC)
        ///
        /// Protocol selection considerations:
        /// - CoAP: Lightweight, designed for constrained devices, UDP-based
        /// - HTTP/HTTPS: Universal compatibility, TCP-based, larger overhead
        /// - MQTT: Publish-subscribe messaging, ideal for sensor networks
        /// - WebSocket: Real-time bidirectional communication
        /// - gRPC: High-performance, strongly-typed RPC for microservices
        ///
        /// The protocol string is used by:
        /// - Service advertisements to indicate their communication method
        /// - Client applications to determine how to connect to services
        /// - Network infrastructure for routing and firewall configuration
        /// </summary>
        public string DefaultProtocol { get; set; } = "coap+udp";

        /// <summary>
        /// Default IP address used for service endpoints when not explicitly specified.
        ///
        /// Default: "127.0.0.1" (localhost/loopback)
        /// Format: IPv4 address in dotted decimal notation
        ///
        /// This address is used as a fallback when services don't specify their
        /// network address. The loopback address is safe for development and
        /// testing but should be changed for production deployments.
        ///
        /// Common address patterns:
        /// - "127.0.0.1": Localhost (development/testing only)
        /// - "0.0.0.0": All interfaces (listen on all network interfaces)
        /// - "*************": Specific private IP (typical LAN deployment)
        /// - "*********": Private network IP (enterprise deployment)
        /// - "***********": Docker/container network IP
        ///
        /// Address selection considerations:
        /// - Localhost (127.0.0.1): Only accessible from same machine
        /// - All interfaces (0.0.0.0): Accessible from any network interface
        /// - Specific IP: Accessible only through that network interface
        /// - Private IPs: Not routable over internet (RFC 1918)
        /// - Public IPs: Routable over internet (requires proper security)
        ///
        /// Security implications:
        /// - Localhost: Highest security, limited accessibility
        /// - Private networks: Good security within trusted networks
        /// - Public addresses: Requires careful security configuration
        /// - Consider firewall rules and network segmentation
        /// </summary>
        public string DefaultServiceAddress { get; set; } = "127.0.0.1";

        /// <summary>
        /// Default port number used by discovered services for communication.
        ///
        /// Default: 5683 (CoAP standard port)
        /// Range: 1024-65535 (avoid well-known ports below 1024)
        ///
        /// This port is used when services don't specify their communication port.
        /// The default aligns with the DefaultProtocol (CoAP) for consistency.
        ///
        /// Standard protocol ports:
        /// - 5683: CoAP (Constrained Application Protocol)
        /// - 80: HTTP (HyperText Transfer Protocol)
        /// - 443: HTTPS (HTTP over TLS/SSL)
        /// - 1883: MQTT (Message Queuing Telemetry Transport)
        /// - 8883: MQTT over TLS/SSL
        /// - 8080: Alternative HTTP (common for applications)
        ///
        /// Port selection guidelines:
        /// - Use standard ports for better interoperability
        /// - Avoid conflicts with other services on the same host
        /// - Consider firewall rules and network policies
        /// - Document port usage for network administrators
        /// - Use consistent ports across similar services
        ///
        /// Dynamic port considerations:
        /// - Services may use dynamic ports (assigned by OS)
        /// - Port 0 can be used to request any available port
        /// - Dynamic ports are typically in range 32768-65535
        /// - Useful for avoiding conflicts in development environments
        /// </summary>
        public int DefaultServicePort { get; set; } = 5683;
        #endregion

        #region Timeout Configuration
        /*
         * ═══════════════════════════════════════════════════════════════════════════════════════
         * TIMEOUT CONFIGURATION
         * ═══════════════════════════════════════════════════════════════════════════════════════
         *
         * Controls various timeout values throughout the USDP2 system. Proper timeout
         * configuration is crucial for system responsiveness, resource management, and
         * handling network failures gracefully.
         *
         * Timeout considerations:
         * - Too short: May cause premature failures on slow networks
         * - Too long: May cause poor user experience and resource exhaustion
         * - Network conditions vary significantly across deployments
         * - Different operations require different timeout strategies
         */

        /// <summary>
        /// Maximum time to wait for network operations to complete before timing out.
        ///
        /// Default: 30 seconds
        /// Range: 1 second to 5 minutes (recommended)
        ///
        /// This timeout applies to general network operations including:
        /// - TCP connection establishment
        /// - HTTP request/response cycles
        /// - TLS handshake completion
        /// - DNS resolution (when applicable)
        ///
        /// Timeout affects:
        /// - User experience (how long users wait for responses)
        /// - Resource usage (connections held open consume memory/handles)
        /// - Error detection (how quickly failures are detected)
        /// - System scalability (shorter timeouts allow higher throughput)
        ///
        /// Network environment considerations:
        /// - Local networks: 5-15 seconds (fast, reliable connections)
        /// - Internet: 30-60 seconds (variable latency and reliability)
        /// - Satellite/mobile: 60-120 seconds (high latency connections)
        /// - Constrained devices: 15-30 seconds (limited processing power)
        ///
        /// Failure scenarios handled:
        /// - Network partitions and connectivity loss
        /// - Server overload and unresponsiveness
        /// - Firewall blocking or filtering
        /// - DNS resolution failures
        /// - TLS certificate validation delays
        /// </summary>
        public TimeSpan NetworkTimeout { get; set; } = TimeSpan.FromSeconds(30);

        /// <summary>
        /// Maximum time to wait for responses to service discovery queries.
        ///
        /// Default: 2 seconds
        /// Range: 100 milliseconds to 10 seconds
        ///
        /// This shorter timeout is specifically for service discovery query responses,
        /// which should be fast since they typically occur within local networks
        /// using UDP multicast. Quick timeouts improve user experience by not
        /// waiting unnecessarily long for responses that may never come.
        ///
        /// Query response characteristics:
        /// - Usually local network communication (low latency)
        /// - UDP-based (no connection establishment overhead)
        /// - Multicast (responses from multiple services simultaneously)
        /// - Cache-based (responses from local service caches)
        ///
        /// Timeout tuning guidelines:
        /// - Fast networks (Gigabit Ethernet): 500ms - 1 second
        /// - Standard networks (100 Mbps): 1-2 seconds
        /// - Wireless networks: 2-5 seconds
        /// - Congested networks: 5-10 seconds
        ///
        /// Trade-offs:
        /// - Shorter: Faster user experience, may miss slow responses
        /// - Longer: More complete results, slower user experience
        /// - Consider implementing progressive timeouts (quick first, longer retry)
        /// </summary>
        public TimeSpan QueryResponseTimeout { get; set; } = TimeSpan.FromSeconds(2);

        /// <summary>
        /// Delay between mDNS (Multicast DNS) operations to prevent network flooding.
        ///
        /// Default: 10 milliseconds
        /// Range: 1 millisecond to 1 second
        ///
        /// This small delay is inserted between consecutive mDNS operations to:
        /// - Prevent overwhelming the network with rapid multicast packets
        /// - Allow other network traffic to be processed
        /// - Reduce the likelihood of packet collisions
        /// - Comply with mDNS rate limiting recommendations
        ///
        /// mDNS operation types:
        /// - Service announcements (when services start/stop)
        /// - Query broadcasts (when searching for services)
        /// - Response multicasts (when answering queries)
        /// - Cache refresh operations (periodic updates)
        ///
        /// Network impact considerations:
        /// - Multicast traffic affects all devices on the network segment
        /// - Rapid multicast can cause network congestion
        /// - Some network equipment may drop excessive multicast packets
        /// - Battery-powered devices benefit from reduced network activity
        ///
        /// Delay tuning:
        /// - High-performance networks: 1-5 milliseconds
        /// - Standard networks: 10-50 milliseconds
        /// - Constrained/IoT networks: 100-1000 milliseconds
        /// - Battery-powered devices: Consider longer delays to save power
        /// </summary>
        public TimeSpan MdnsOperationDelay { get; set; } = TimeSpan.FromMilliseconds(10);

        /// <summary>
        /// Delay used in test environments to allow system components to initialize.
        ///
        /// Default: 100 milliseconds
        /// Range: 10 milliseconds to 5 seconds
        ///
        /// This delay is primarily used in testing scenarios to ensure that:
        /// - Network listeners have time to start and bind to ports
        /// - Service caches are initialized and ready
        /// - Background threads have started processing
        /// - System resources are allocated and available
        ///
        /// Testing scenarios where this delay is useful:
        /// - Unit tests that involve network operations
        /// - Integration tests with multiple components
        /// - Performance tests that need stable baselines
        /// - Automated test suites with timing dependencies
        ///
        /// Environment-specific considerations:
        /// - Fast development machines: 10-50 milliseconds
        /// - Standard test environments: 100-500 milliseconds
        /// - Slow/virtualized environments: 500-2000 milliseconds
        /// - CI/CD systems: May need longer delays due to resource contention
        ///
        /// Production impact:
        /// - This delay should NOT be used in production code paths
        /// - Only for test initialization and setup scenarios
        /// - Consider using proper synchronization instead of fixed delays
        /// - Use event-based coordination when possible
        /// </summary>
        public TimeSpan StartupDelay { get; set; } = TimeSpan.FromMilliseconds(100);
        #endregion

        #region File and Path Configuration
        /*
         * ═══════════════════════════════════════════════════════════════════════════════════════
         * FILE AND PATH CONFIGURATION
         * ═══════════════════════════════════════════════════════════════════════════════════════
         *
         * Defines file names and paths used by the USDP2 system for configuration,
         * logging, and data storage. Proper file management is essential for
         * deployment, maintenance, and troubleshooting.
         */

        /// <summary>
        /// Name of the default configuration file used to store USDP2 settings.
        ///
        /// Default: "usdp_config.json"
        /// Format: Valid filename with appropriate extension
        ///
        /// This file contains persistent configuration settings that can be
        /// modified without recompiling the application. The JSON format
        /// provides human-readable configuration that can be easily edited.
        ///
        /// Configuration file contents typically include:
        /// - Authentication keys and certificates
        /// - Network addresses and port overrides
        /// - Security policy settings
        /// - Service-specific parameters
        /// - Environment-specific overrides
        ///
        /// File naming considerations:
        /// - Use descriptive names that indicate purpose
        /// - Include version numbers if multiple configs exist
        /// - Consider environment suffixes (e.g., "usdp_config_prod.json")
        /// - Avoid special characters that may cause filesystem issues
        ///
        /// Security considerations:
        /// - Configuration files may contain sensitive information
        /// - Implement appropriate file permissions (read-only for application)
        /// - Consider encryption for sensitive configuration data
        /// - Exclude from version control if containing secrets
        /// - Use secure storage mechanisms in production environments
        /// </summary>
        public string DefaultConfigFileName { get; set; } = "usdp_config.json";

        /// <summary>
        /// Full path to the default configuration file, computed relative to application directory.
        ///
        /// This property combines the application's base directory with the configuration
        /// filename to provide a complete path for configuration file access.
        ///
        /// Path resolution:
        /// - Uses AppDomain.CurrentDomain.BaseDirectory as the base path
        /// - Combines with DefaultConfigFileName using Path.Combine()
        /// - Results in platform-appropriate path separators
        /// - Handles relative and absolute path scenarios correctly
        ///
        /// Example paths:
        /// - Windows: "C:\Program Files\MyApp\usdp_config.json"
        /// - Linux: "/opt/myapp/usdp_config.json"
        /// - macOS: "/Applications/MyApp.app/Contents/MacOS/usdp_config.json"
        /// - Development: "C:\Source\MyProject\bin\Debug\usdp_config.json"
        ///
        /// Deployment considerations:
        /// - Ensure application has read access to the configuration directory
        /// - Consider write access if configuration updates are needed
        /// - Plan for configuration file distribution and updates
        /// - Implement fallback mechanisms if configuration file is missing
        /// - Consider using environment variables for path overrides
        /// </summary>
        public string DefaultConfigPath => Path.Combine(AppDomain.CurrentDomain.BaseDirectory, DefaultConfigFileName);
        #endregion

        #region Buffer and Performance Configuration
        /*
         * ═══════════════════════════════════════════════════════════════════════════════════════
         * BUFFER AND PERFORMANCE CONFIGURATION
         * ═══════════════════════════════════════════════════════════════════════════════════════
         *
         * Controls memory allocation, buffer management, and performance-related settings.
         * These settings directly impact memory usage, network throughput, and system
         * scalability under load.
         */

        /// <summary>
        /// Default size for network I/O buffers in bytes.
        ///
        /// Default: 1024 bytes (1 KB)
        /// Range: 256 bytes to MaxBufferSize
        ///
        /// This buffer size is used for initial memory allocation when reading
        /// network data. The size affects both memory usage and performance:
        ///
        /// Performance implications:
        /// - Smaller buffers: Lower memory usage, more system calls, potential fragmentation
        /// - Larger buffers: Higher memory usage, fewer system calls, better throughput
        /// - Optimal size depends on typical message sizes and available memory
        ///
        /// Typical message sizes in USDP2:
        /// - Service announcements: 200-500 bytes
        /// - Service queries: 100-300 bytes
        /// - Service responses: 300-800 bytes
        /// - Bulk operations: 1KB-64KB
        ///
        /// Buffer sizing guidelines:
        /// - IoT/constrained devices: 256-512 bytes
        /// - Standard applications: 1024-4096 bytes
        /// - High-throughput servers: 4096-16384 bytes
        /// - Bulk data transfer: 16384-65536 bytes
        ///
        /// Memory considerations:
        /// - Each active connection may allocate multiple buffers
        /// - Buffer pools help reduce garbage collection pressure
        /// - Consider total memory available and concurrent connection limits
        /// - Monitor memory usage patterns in production environments
        /// </summary>
        public int DefaultBufferSize { get; set; } = 1024;

        /// <summary>
        /// Maximum allowed buffer size to prevent excessive memory allocation.
        ///
        /// Default: 65536 bytes (64 KB)
        /// Range: DefaultBufferSize to 1 MB (recommended maximum)
        ///
        /// This limit prevents individual operations from allocating excessively
        /// large buffers that could lead to memory exhaustion or denial-of-service
        /// attacks. The limit is enforced when dynamically sizing buffers based
        /// on incoming data size.
        ///
        /// Security considerations:
        /// - Prevents memory exhaustion attacks via large message sizes
        /// - Limits impact of malformed or malicious network packets
        /// - Helps maintain system stability under adverse conditions
        /// - Should be set based on legitimate maximum message sizes
        ///
        /// Performance considerations:
        /// - Larger limits allow handling of bigger legitimate messages
        /// - Smaller limits provide better protection but may reject valid data
        /// - Consider the largest legitimate USDP2 message in your deployment
        /// - Balance security protection with functional requirements
        ///
        /// Typical maximum message sizes:
        /// - Simple service discovery: 1-4 KB
        /// - Complex service metadata: 4-16 KB
        /// - Bulk service listings: 16-64 KB
        /// - File transfer/large payloads: 64 KB+ (consider streaming)
        ///
        /// Error handling:
        /// - Messages exceeding this limit should be rejected gracefully
        /// - Log security events when limits are exceeded
        /// - Provide clear error messages for legitimate oversized requests
        /// - Consider implementing progressive size limits based on authentication
        /// </summary>
        public int MaxBufferSize { get; set; } = 65536; // 64KB

        /// <summary>
        /// Port number offset used when creating response receivers on different ports.
        ///
        /// Default: 1 (use next port number)
        /// Range: 1-1000 (avoid large offsets that might conflict)
        ///
        /// When USDP2 needs to create separate receivers for responses (to avoid
        /// conflicts with the main service discovery port), this offset is added
        /// to the base port number to calculate the response port.
        ///
        /// Example usage:
        /// - Main multicast port: 5353
        /// - Response receiver port: 5353 + 1 = 5354
        /// - This allows simultaneous listening on both ports
        ///
        /// Port conflict avoidance:
        /// - Offset should be small to stay within expected port ranges
        /// - Check that offset ports don't conflict with other services
        /// - Consider using dynamic port allocation (port 0) instead
        /// - Document port usage for network administrators
        ///
        /// Network considerations:
        /// - Firewall rules may need to allow both base and offset ports
        /// - Network monitoring should account for multiple ports per service
        /// - Load balancers may need configuration for additional ports
        /// - Consider using port ranges in security policies
        ///
        /// Alternative approaches:
        /// - Use ephemeral ports (let OS assign available ports)
        /// - Implement port pooling for better resource management
        /// - Use single port with connection multiplexing
        /// - Consider using different network interfaces instead of ports
        /// </summary>
        public int ResponsePortOffset { get; set; } = 1;
        #endregion

        #region Example and Default Values
        /*
         * ═══════════════════════════════════════════════════════════════════════════════════════
         * EXAMPLE AND DEFAULT VALUES CONFIGURATION
         * ═══════════════════════════════════════════════════════════════════════════════════════
         *
         * Provides default values used in examples, documentation, and testing scenarios.
         * These values help ensure consistency across examples and provide sensible
         * defaults for development and demonstration purposes.
         */

        /// <summary>
        /// Default service type used in examples and demonstrations.
        ///
        /// Default: "home/lighting" (IoT lighting service example)
        /// Format: "category/subcategory" hierarchical naming
        ///
        /// Service types provide a way to categorize and organize discovered services.
        /// The hierarchical format allows for logical grouping and filtering of services
        /// based on their functionality and domain.
        ///
        /// Example service type hierarchies:
        /// - "home/lighting": Smart home lighting devices
        /// - "home/security": Security cameras, sensors, alarms
        /// - "home/climate": Thermostats, HVAC systems
        /// - "office/printing": Network printers and scanners
        /// - "industrial/sensors": Industrial monitoring equipment
        /// - "medical/devices": Medical monitoring and diagnostic equipment
        ///
        /// Naming conventions:
        /// - Use lowercase for consistency
        /// - Separate categories with forward slashes
        /// - Keep names descriptive but concise
        /// - Avoid special characters that might cause parsing issues
        /// - Consider standardization across your organization
        /// </summary>
        public string DefaultServiceType { get; set; } = "home/lighting";

        /// <summary>
        /// Default service instance identifier used in examples.
        ///
        /// Default: "bulb1" (first lighting device instance)
        /// Format: Alphanumeric identifier, unique within service type
        ///
        /// Service instances distinguish between multiple services of the same type.
        /// Each instance should have a unique identifier within its service type
        /// to enable specific service selection and management.
        ///
        /// Instance naming strategies:
        /// - Sequential numbering: "bulb1", "bulb2", "bulb3"
        /// - Location-based: "kitchen-light", "bedroom-lamp"
        /// - MAC address suffix: "device-a1b2c3"
        /// - UUID-based: "device-550e8400-e29b-41d4-a716-************"
        /// - Descriptive names: "main-entrance-light", "conference-room-projector"
        ///
        /// Considerations:
        /// - Keep identifiers human-readable when possible
        /// - Ensure uniqueness within the service type scope
        /// - Consider persistence across device reboots
        /// - Avoid personally identifiable information
        /// - Plan for device replacement and identifier reuse
        /// </summary>
        public string DefaultServiceInstance { get; set; } = "bulb1";

        /// <summary>
        /// Default metadata type value used in service examples.
        ///
        /// Default: "lighting" (matches the service type category)
        ///
        /// Metadata provides additional descriptive information about services
        /// that can be used for filtering, categorization, and service selection.
        /// The type metadata typically describes the primary function or category
        /// of the service.
        ///
        /// Common metadata type values:
        /// - "lighting": Light bulbs, fixtures, controllers
        /// - "sensor": Temperature, humidity, motion sensors
        /// - "actuator": Motors, valves, switches
        /// - "display": Monitors, TVs, digital signage
        /// - "audio": Speakers, microphones, audio systems
        /// - "storage": Network storage devices, databases
        ///
        /// Metadata usage:
        /// - Enables fine-grained service filtering
        /// - Supports user interface categorization
        /// - Facilitates automated service discovery
        /// - Helps with service compatibility checking
        /// - Provides context for service capabilities
        /// </summary>
        public string DefaultMetadataType { get; set; } = "lighting";

        /// <summary>
        /// Default metadata location value used in service examples.
        ///
        /// Default: "room=101" (room-based location identifier)
        /// Format: "key=value" pairs for structured location data
        ///
        /// Location metadata helps users and applications identify where
        /// services are physically located. This is particularly important
        /// for IoT and smart building applications where physical location
        /// affects service relevance and usage.
        ///
        /// Location format examples:
        /// - Room-based: "room=101", "room=kitchen", "room=conference-a"
        /// - Building-based: "building=main", "floor=2", "wing=east"
        /// - Geographic: "lat=40.7128", "lon=-74.0060", "city=newyork"
        /// - Zone-based: "zone=production", "area=warehouse", "sector=a1"
        /// - Hierarchical: "campus=main/building=a/floor=3/room=301"
        ///
        /// Location considerations:
        /// - Use consistent location naming schemes
        /// - Consider hierarchical location structures
        /// - Plan for location changes (device mobility)
        /// - Balance precision with privacy requirements
        /// - Support both human-readable and machine-readable formats
        /// - Consider integration with building management systems
        /// </summary>
        public string DefaultMetadataLocation { get; set; } = "room=101";
        #endregion

        #region Bloom Filter Configuration
        /*
         * ═══════════════════════════════════════════════════════════════════════════════════════
         * BLOOM FILTER CONFIGURATION
         * ═══════════════════════════════════════════════════════════════════════════════════════
         *
         * Bloom filters are probabilistic data structures used to optimize service discovery
         * by quickly eliminating non-matching services before performing detailed queries.
         * This significantly improves performance in large networks with many services.
         *
         * Key Benefits:
         * - Fast membership testing (O(k) where k is number of hash functions)
         * - Memory efficient (uses bit arrays instead of storing actual data)
         * - No false negatives (if filter says "no", item is definitely not present)
         * - Configurable false positive rate (trade-off between memory and accuracy)
         *
         * Configuration Guidelines:
         * - Enable for networks with >1000 services for best performance gains
         * - Larger filter sizes reduce false positives but use more memory
         * - More hash functions reduce false positives but increase computation
         * - Auto-tuning calculates optimal parameters based on expected service count
         *
         * Performance Considerations:
         * - Filter size: ~125KB for 1M bits (good for 10K services)
         * - Hash functions: 7 functions optimal for 1% false positive rate
         * - Refresh interval: Balance between accuracy and network overhead
         * - Memory usage: Linear with filter size, independent of service count
         */

        /// <summary>
        /// Gets or sets whether Bloom filters are enabled for service discovery optimization.
        ///
        /// When enabled, Bloom filters are used to quickly eliminate non-matching services
        /// before performing detailed queries, significantly improving performance in large networks.
        ///
        /// Benefits of enabling:
        /// - Faster service discovery queries (especially with many services)
        /// - Reduced network traffic for negative queries
        /// - Lower CPU usage for service matching
        /// - Scalable performance as network grows
        ///
        /// Considerations:
        /// - Small memory overhead for filter storage
        /// - Periodic refresh network traffic
        /// - Minimal false positive rate (configurable)
        ///
        /// Recommended: Enable for networks with >100 services
        /// Default: true
        /// </summary>
        public bool EnableBloomFilters { get; set; } = true;

        /// <summary>
        /// Gets or sets the size of the Bloom filter bit array.
        ///
        /// Larger sizes reduce false positive rates but use more memory.
        /// The actual size will be adjusted to the next prime number for better hash distribution.
        ///
        /// Size Guidelines:
        /// - 100,000 bits (~12.5 KB): Good for up to 1,000 services
        /// - 1,000,000 bits (~125 KB): Good for up to 10,000 services
        /// - 10,000,000 bits (~1.25 MB): Good for up to 100,000 services
        ///
        /// Memory Usage:
        /// - Each bit uses 1 bit of memory (8 bits = 1 byte)
        /// - 1,000,000 bits = 125,000 bytes = ~125 KB
        /// - Maximum allowed: 100,000,000 bits (~12.5 MB)
        ///
        /// Performance Impact:
        /// - Larger filters: Lower false positive rate, more memory
        /// - Smaller filters: Higher false positive rate, less memory
        /// - Hash computation time is independent of filter size
        ///
        /// Range: 1,000 to 100,000,000
        /// Default: 1,000,000 (optimal for ~10,000 services)
        /// </summary>
        public int BloomFilterSize { get; set; } = 1_000_000;

        /// <summary>
        /// Gets or sets the number of hash functions used in the Bloom filter.
        ///
        /// More hash functions reduce false positive rates but increase computation time.
        /// The optimal number depends on the expected number of elements and filter size.
        ///
        /// Mathematical Relationship:
        /// - Optimal k = (m/n) * ln(2)
        /// - Where m = filter size, n = expected number of elements
        /// - For 1M filter with 10K services: k = (1000000/10000) * 0.693 ≈ 7
        ///
        /// Performance Trade-offs:
        /// - More functions: Lower false positive rate, higher CPU usage
        /// - Fewer functions: Higher false positive rate, lower CPU usage
        /// - Each additional function requires one more hash computation
        ///
        /// Range: 1 to 20
        /// Default: 7 (optimal for 1% false positive rate)
        /// </summary>
        public int BloomFilterHashFunctions { get; set; } = 7;

        /// <summary>
        /// Gets or sets the expected number of services in the network.
        ///
        /// Used to calculate optimal Bloom filter parameters and estimate false positive rates.
        /// This helps in automatically tuning the filter for best performance.
        ///
        /// Range: 1 to 10,000,000
        /// Default: 10,000 (medium-large network)
        /// </summary>
        public int ExpectedServiceCount { get; set; } = 10_000;

        /// <summary>
        /// Gets or sets the maximum acceptable false positive rate for Bloom filters.
        ///
        /// When auto-tuning is enabled, filter parameters are adjusted to stay below this rate.
        /// Lower rates require larger filters or more hash functions.
        ///
        /// Range: 0.001 (0.1%) to 0.1 (10%)
        /// Default: 0.01 (1% - good balance of performance and accuracy)
        /// </summary>
        public double MaxFalsePositiveRate { get; set; } = 0.01;

        /// <summary>
        /// Gets or sets whether Bloom filter parameters should be automatically tuned
        /// based on the expected service count and desired false positive rate.
        /// When enabled, BloomFilterSize and BloomFilterHashFunctions may be adjusted.
        /// Default: true
        /// </summary>
        public bool AutoTuneBloomFilter { get; set; } = true;

        /// <summary>
        /// Gets or sets the interval for refreshing Bloom filters with updated service data.
        /// Shorter intervals provide more accurate filtering but increase network overhead.
        /// Set to TimeSpan.Zero to disable automatic refresh.
        /// Default: 5 minutes
        /// </summary>
        public TimeSpan BloomFilterRefreshInterval { get; set; } = TimeSpan.FromMinutes(5);

        #endregion

        #region Extensibility
        /*
         * ═══════════════════════════════════════════════════════════════════════════════════════
         * EXTENSIBILITY CONFIGURATION
         * ═══════════════════════════════════════════════════════════════════════════════════════
         *
         * Configuration settings for extensibility features in the USDP2 system.
         * These settings allow for optional enhancements and customizations that
         * can be enabled or disabled based on specific deployment needs.
         */

        /// <summary>
        /// Enables UDP security override functionality for DTLS and datagram protection.
        ///
        /// Default: false (UDP security override disabled)
        ///
        /// When enabled, the UdpSecurityOverride class provides DTLS (Datagram TLS)
        /// and message authentication capabilities for UDP communications. This is
        /// an optional security enhancement that can be used when secure UDP
        /// communication is required.
        /// 
        /// Features when enabled:
        /// - Message authentication for UDP packets
        /// - Integration with existing USDP2 key management infrastructure
        /// - Automatic fallback to unsecured operation if security fails
        /// - Comprehensive security logging
        ///
        /// Security benefits:
        /// - Protection against UDP packet spoofing
        /// - Message integrity verification
        /// - Defense against replay attacks
        /// - Compliance with secure communication requirements
        ///
        /// Implementation note:
        /// The UdpSecurityOverride class is designed to be easily removable
        /// from the codebase if optional UDP security is not needed.
        /// When this setting is false, the UDP security override is not used,
        /// and the class can be safely deleted without affecting core functionality.
        ///
        /// To remove this functionality:
        /// 1. Set this property to false
        /// 2. Delete the UdpSecurityOverride.cs file
        /// 3. Remove any references to UdpSecurityOverride in other classes
        ///
        /// Recommended: Enable for deployments requiring secure UDP communications
        /// </summary>
        public bool EnableUdpSecurity { get; set; }
        #endregion

        #region Custom Configuration Settings
        /*
         * ═══════════════════════════════════════════════════════════════════════════════════════
         * CUSTOM CONFIGURATION SETTINGS
         * ═══════════════════════════════════════════════════════════════════════════════════════
         *
         * Provides mechanisms for extending the configuration system with custom
         * settings that are not covered by the standard configuration options.
         * This allows for deployment-specific customizations without modifying
         * the core configuration structure.
         */

        /// <summary>
        /// Container for custom configuration settings not covered by standard options.
        ///
        /// Default: null (no custom settings)
        /// Type: object (can hold any serializable data structure)
        ///
        /// This property provides an extension point for adding custom configuration
        /// settings without modifying the core UsdpConfiguration class. It can hold
        /// any serializable object, allowing for flexible configuration extensions.
        ///
        /// Usage patterns:
        ///
        /// Dictionary approach:
        /// <code>
        /// CustomSettings = new Dictionary&lt;string, object&gt;
        /// {
        ///     ["CustomTimeout"] = TimeSpan.FromMinutes(10),
        ///     ["SpecialMode"] = true,
        ///     ["VendorSettings"] = new { ApiKey = "...", Endpoint = "..." }
        /// };
        /// </code>
        ///
        /// Custom class approach:
        /// <code>
        /// public class MyCustomSettings
        /// {
        ///     public string ApiEndpoint { get; set; }
        ///     public int RetryCount { get; set; }
        ///     public bool EnableFeatureX { get; set; }
        /// }
        /// CustomSettings = new MyCustomSettings { ... };
        /// </code>
        ///
        /// JSON configuration approach:
        /// <code>
        /// CustomSettings = JObject.Parse(customConfigJson);
        /// </code>
        ///
        /// Access patterns:
        /// - Type casting: var settings = (MyCustomSettings)CustomSettings;
        /// - Dictionary access: var dict = (Dictionary&lt;string, object&gt;)CustomSettings;
        /// - Dynamic access: dynamic settings = CustomSettings;
        ///
        /// Serialization considerations:
        /// - Ensure custom objects are serializable if configuration persistence is needed
        /// - Consider using JSON.NET attributes for serialization control
        /// - Plan for version compatibility when custom settings evolve
        /// - Document custom settings structure for maintainability
        ///
        /// Best practices:
        /// - Use strongly-typed custom classes when possible
        /// - Provide default values for custom settings
        /// - Validate custom settings during application startup
        /// - Consider configuration schema validation
        /// - Document custom settings in deployment guides
        /// </summary>
        public object? CustomSettings { get; set; }
        #endregion

        #region Network Data Validation Configuration
        /*
         * ═══════════════════════════════════════════════════════════════════════════════════════
         * NETWORK DATA VALIDATION CONFIGURATION
         * ═══════════════════════════════════════════════════════════════════════════════════════
         *
         * Controls comprehensive input validation and sanitization for network data to prevent
         * security issues and ensure data integrity. This includes protection against DoS attacks,
         * malformed data, and malicious payloads.
         *
         * Security features:
         * - Size limits to prevent memory exhaustion attacks
         * - Content validation to detect malicious patterns
         * - Input sanitization to remove harmful content
         * - Comprehensive logging for security monitoring
         * - Configurable validation behavior for different environments
         *
         * The validation system provides multiple layers of protection:
         * 1. Raw data validation (size, format, suspicious patterns)
         * 2. Deserialization validation (structure, nesting depth)
         * 3. Semantic validation (business rules, field constraints)
         * 4. Sanitization (content cleaning, normalization)
         */

        /// <summary>
        /// Controls whether network data validation is enabled.
        ///
        /// Default: true (validation enabled for security)
        ///
        /// When enabled, all incoming network data is validated for security and integrity
        /// before processing. This includes size limits, content validation, and sanitization
        /// to prevent various types of attacks and ensure data quality.
        ///
        /// Security benefits when enabled:
        /// - Protection against DoS attacks through oversized payloads
        /// - Detection and blocking of malicious content patterns
        /// - Prevention of buffer overflow and injection attacks
        /// - Comprehensive security logging and monitoring
        /// - Input sanitization to remove harmful content
        ///
        /// Performance considerations:
        /// - Adds validation overhead to network operations
        /// - May slightly increase memory usage for validation buffers
        /// - Provides detailed logging that may increase log volume
        /// - Benefits significantly outweigh performance costs
        ///
        /// When to disable (not recommended):
        /// - Only in trusted, isolated development environments
        /// - When performance is absolutely critical and security is not a concern
        /// - During debugging when validation interferes with testing
        /// - Should NEVER be disabled in production environments
        /// </summary>
        public bool EnableNetworkDataValidation { get; set; } = true;

        /// <summary>
        /// Maximum allowed size for incoming network data in bytes.
        ///
        /// Default: 1048576 bytes (1 MB)
        /// Range: 1024 bytes to 10 MB (recommended maximum)
        ///
        /// This limit prevents DoS attacks through oversized payloads that could exhaust
        /// system memory or processing resources. The limit is applied before any
        /// deserialization or processing occurs.
        ///
        /// Attack scenarios prevented:
        /// - Memory exhaustion through large payloads
        /// - CPU exhaustion through complex data structures
        /// - Network bandwidth consumption attacks
        /// - Storage exhaustion in logging systems
        ///
        /// Size considerations:
        /// - USDP2 messages are typically small (less than 1KB for basic operations)
        /// - Bulk operations may require larger limits (up to 64KB)
        /// - File transfers should use streaming, not single large messages
        /// - Consider legitimate maximum message sizes in your deployment
        ///
        /// Environment-specific recommendations:
        /// - IoT/constrained devices: 4-16 KB
        /// - Standard applications: 64-256 KB
        /// - Enterprise systems: 256 KB - 1 MB
        /// - High-throughput systems: 1-4 MB (with careful monitoring)
        /// </summary>
        public int MaxNetworkDataSize { get; set; } = 1024 * 1024; // 1MB

        /// <summary>
        /// Maximum allowed size for JSON data in bytes.
        ///
        /// Default: 524288 bytes (512 KB)
        /// Range: 1024 bytes to MaxNetworkDataSize
        ///
        /// JSON data is typically larger than binary formats due to text encoding
        /// and metadata overhead. This limit specifically applies to JSON payloads
        /// to prevent JSON-specific attacks while allowing reasonable message sizes.
        ///
        /// JSON-specific considerations:
        /// - Text encoding increases size compared to binary formats
        /// - Nested objects can create exponential size growth
        /// - Pretty-printed JSON has additional whitespace overhead
        /// - Malicious JSON can exploit parser vulnerabilities
        ///
        /// Typical JSON message sizes in USDP2:
        /// - Service advertisements: 500-2000 bytes
        /// - Service queries: 200-800 bytes
        /// - Bulk service listings: 2-32 KB
        /// - Configuration data: 1-16 KB
        /// </summary>
        public int MaxJsonDataSize { get; set; } = 512 * 1024; // 512KB

        /// <summary>
        /// Maximum allowed size for CBOR data in bytes.
        ///
        /// Default: 524288 bytes (512 KB)
        /// Range: 1024 bytes to MaxNetworkDataSize
        ///
        /// CBOR (Concise Binary Object Representation) is more compact than JSON
        /// but can still be exploited for attacks. This limit provides protection
        /// while allowing efficient binary communication.
        ///
        /// CBOR advantages:
        /// - More compact than JSON (typically 20-50% smaller)
        /// - Faster parsing and generation
        /// - Better support for binary data
        /// - Standardized format (RFC 7049)
        ///
        /// Security considerations:
        /// - Binary formats can hide malicious content more easily
        /// - Parser vulnerabilities may be different from JSON
        /// - Validation tools may be less mature than JSON
        /// - Consider additional binary content scanning
        /// </summary>
        public int MaxCborDataSize { get; set; } = 512 * 1024; // 512KB

        /// <summary>
        /// Maximum allowed length for individual string fields.
        ///
        /// Default: 1024 characters (1 KB)
        /// Range: 64 characters to 64 KB
        ///
        /// This limit prevents buffer overflow attacks and ensures reasonable
        /// memory usage for string processing. It applies to individual string
        /// fields within messages, not the entire message size.
        ///
        /// String field types in USDP2:
        /// - Service names: typically 10-50 characters
        /// - Addresses: typically 7-45 characters (IPv4/IPv6)
        /// - Protocols: typically 3-20 characters
        /// - Metadata values: typically 10-200 characters
        /// - Error messages: typically 50-500 characters
        ///
        /// Attack prevention:
        /// - Buffer overflow in string processing
        /// - Memory exhaustion through long strings
        /// - Log injection through oversized fields
        /// - Display corruption in user interfaces
        /// </summary>
        public int MaxStringFieldLength { get; set; } = 1024; // 1KB

        /// <summary>
        /// Maximum allowed number of metadata entries.
        ///
        /// Default: 50 entries
        /// Range: 5 to 1000 entries
        ///
        /// This limit prevents resource exhaustion attacks through excessive
        /// metadata entries while allowing reasonable service description
        /// capabilities. Each entry consumes memory and processing time.
        ///
        /// Typical metadata usage:
        /// - Basic services: 2-5 entries (type, version, location)
        /// - Standard services: 5-15 entries (capabilities, configuration)
        /// - Complex services: 15-30 entries (detailed capabilities)
        /// - Enterprise services: 30-50 entries (full service description)
        ///
        /// Resource impact:
        /// - Memory usage scales with number of entries
        /// - Processing time increases with validation overhead
        /// - Network bandwidth affected by metadata size
        /// - Storage requirements for service caches
        /// </summary>
        public int MaxMetadataEntries { get; set; } = 50;

        /// <summary>
        /// Maximum allowed nesting depth for JSON/CBOR objects.
        ///
        /// Default: 10 levels
        /// Range: 3 to 50 levels
        ///
        /// This limit prevents stack overflow attacks through deeply nested
        /// data structures while allowing reasonable object complexity.
        /// Excessive nesting can exhaust parser stack space and cause crashes.
        ///
        /// Nesting examples:
        /// - Flat objects: 1-2 levels (simple key-value pairs)
        /// - Standard objects: 3-5 levels (nested configuration)
        /// - Complex objects: 5-10 levels (hierarchical data)
        /// - Malicious objects: 100+ levels (attack attempts)
        ///
        /// Parser considerations:
        /// - Recursive parsers are vulnerable to stack overflow
        /// - Each nesting level consumes stack space
        /// - Some parsers have built-in depth limits
        /// - Consider iterative parsing for deep structures
        /// </summary>
        public int MaxNestingDepth { get; set; } = 10;

        /// <summary>
        /// Controls whether to use the secure serializer by default.
        ///
        /// Default: true (secure serializer enabled)
        ///
        /// The secure serializer includes comprehensive input validation and
        /// sanitization beyond the basic serializer. It provides additional
        /// protection against various attack vectors and data integrity issues.
        ///
        /// Secure serializer features:
        /// - Pre-serialization validation of object properties
        /// - Post-deserialization sanitization and validation
        /// - Comprehensive security logging and monitoring
        /// - Protection against serialization-specific attacks
        /// - Automatic data sanitization and normalization
        ///
        /// Performance impact:
        /// - Additional validation overhead (typically 5-15%)
        /// - More detailed logging (increased log volume)
        /// - Memory overhead for validation buffers
        /// - Benefits significantly outweigh performance costs
        ///
        /// When to disable (not recommended):
        /// - Only in performance-critical, trusted environments
        /// - When custom validation is implemented elsewhere
        /// - During debugging when validation interferes
        /// - Should be carefully evaluated before disabling
        /// </summary>
        public bool UseSecureSerializer { get; set; } = true;

        /// <summary>
        /// Controls whether to log validation failures for security monitoring.
        ///
        /// Default: true (logging enabled)
        ///
        /// When enabled, all validation failures are logged with detailed information
        /// for security monitoring and incident response. This provides valuable
        /// insights into potential attacks and system issues.
        ///
        /// Logged information includes:
        /// - Source IP address and port
        /// - Validation failure type and details
        /// - Data size and content characteristics
        /// - Timestamp and system context
        /// - Recommended actions for resolution
        ///
        /// Security monitoring benefits:
        /// - Early detection of attack attempts
        /// - Forensic analysis capabilities
        /// - Trend analysis for security improvements
        /// - Compliance with security logging requirements
        /// - Integration with SIEM systems
        ///
        /// Privacy considerations:
        /// - Logs may contain sensitive information
        /// - Implement appropriate log retention policies
        /// - Consider log encryption for sensitive environments
        /// - Ensure compliance with data protection regulations
        /// </summary>
        public bool LogValidationFailures { get; set; } = true;

        /// <summary>
        /// Controls whether to reject data with suspicious binary patterns.
        ///
        /// Default: true (suspicious pattern detection enabled)
        ///
        /// When enabled, incoming binary data is analyzed for patterns that
        /// might indicate attack attempts, such as excessive null bytes,
        /// repeating patterns, or other anomalies.
        ///
        /// Suspicious patterns detected:
        /// - Excessive null bytes (potential buffer overflow attempts)
        /// - Repeating byte patterns (potential padding attacks)
        /// - Unusual byte distributions (potential obfuscated content)
        /// - Known malicious signatures (if signature database available)
        ///
        /// Detection benefits:
        /// - Early identification of potential attacks
        /// - Protection against unknown attack vectors
        /// - Reduced processing of obviously malicious data
        /// - Enhanced security monitoring capabilities
        ///
        /// False positive considerations:
        /// - Some legitimate data may trigger pattern detection
        /// - Compressed or encrypted data may appear suspicious
        /// - Consider whitelisting known legitimate patterns
        /// - Implement appeals process for false positives
        /// </summary>
        public bool RejectSuspiciousBinaryPatterns { get; set; } = true;
        #endregion

        /*
         * ═══════════════════════════════════════════════════════════════════════════════════════
         * CONSTRUCTOR AND SINGLETON IMPLEMENTATION
         * ═══════════════════════════════════════════════════════════════════════════════════════
         *
         * The UsdpConfiguration class implements the Singleton pattern to ensure
         * consistent configuration access throughout the application. The private
         * constructor prevents external instantiation, forcing all access through
         * the Instance property.
         *
         * Thread safety is provided by the Lazy<T> implementation, which ensures
         * that only one instance is created even in multi-threaded scenarios.
         * The instance is created on first access and reused for all subsequent
         * calls.
         *
         * Configuration lifecycle:
         * 1. First access to Instance property triggers lazy initialization
         * 2. Private constructor is called to create the single instance
         * 3. All properties are initialized with their default values
         * 4. The instance is cached and returned for all future access
         * 5. Configuration values can be modified at runtime if needed
         *
         * Usage recommendations:
         * - Access configuration through UsdpConfiguration.Instance
         * - Modify configuration early in application startup if needed
         * - Consider implementing configuration change notifications if runtime
         *   updates are required
         * - Cache frequently accessed configuration values in performance-critical
         *   code paths
         */

        /// <summary>
        /// Private constructor prevents external instantiation and enforces singleton pattern.
        ///
        /// This constructor is called only once during the application lifecycle when
        /// the singleton instance is first accessed. All configuration properties are
        /// initialized with their default values as defined in their declarations.
        ///
        /// The singleton pattern ensures that:
        /// - Only one configuration instance exists per application
        /// - Configuration is consistent across all components
        /// - Memory usage is minimized (single instance vs. multiple copies)
        /// - Configuration changes affect the entire application uniformly
        /// </summary>
        private UsdpConfiguration()
        {
            // All properties are initialized with their default values
            // as specified in their declarations above.
            // No additional initialization is required in the constructor.
        }

        /// <summary>
        /// Public constructor for testing purposes only.
        ///
        /// ⚠️ WARNING: This constructor should ONLY be used in unit tests.
        /// Production code should use UsdpConfiguration.Instance instead.
        ///
        /// This constructor allows tests to create isolated configuration instances
        /// without affecting the global singleton instance used by the application.
        /// </summary>
        /// <param name="forTesting">Must be true to use this constructor. This parameter
        /// serves as a safety check to prevent accidental use in production code.</param>
        /// <exception cref="ArgumentException">Thrown when forTesting is false.</exception>
        public UsdpConfiguration(bool forTesting)
        {
            if (!forTesting)
            {
                throw new ArgumentException("This constructor is only for testing. Use UsdpConfiguration.Instance for production code.", nameof(forTesting));
            }

            // All properties are initialized with their default values
            // as specified in their declarations above.
            // No additional initialization is required in the constructor.
        }
    }
}
