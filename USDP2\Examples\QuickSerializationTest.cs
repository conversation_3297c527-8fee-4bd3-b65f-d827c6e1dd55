using System;
using System.Threading.Tasks;

namespace USDP2.Examples
{
    /// <summary>
    /// Quick test to verify serialization optimization is working correctly.
    /// </summary>
    public static class QuickSerializationTest
    {
        /// <summary>
        /// Runs a quick test of the serialization optimization features.
        /// </summary>
        public static async Task RunAsync()
        {
            Console.WriteLine("=== Quick Serialization Optimization Test ===");
            Console.WriteLine();

            try
            {
                // Test 1: Basic serialization
                await TestBasicSerializationAsync();

                // Test 2: Cache effectiveness
                await TestCacheEffectivenessAsync();

                // Test 3: Pool statistics
                TestPoolStatistics();

                Console.WriteLine("✅ All serialization optimization tests passed!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Test failed: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }

            Console.WriteLine();
            Console.WriteLine("=== Test Complete ===");
        }

        /// <summary>
        /// Tests basic serialization functionality.
        /// </summary>
        private static async Task TestBasicSerializationAsync()
        {
            Console.WriteLine("1. Testing Basic Serialization");
            Console.WriteLine("------------------------------");

            // Create test object
            var serviceId = new ServiceIdentifier("test", "QuickTestService");
            var endpoint = new TransportEndpoint
            {
                Address = "127.0.0.1",
                Port = 8080,
                Protocol = "http"
            };

            var advertisement = new ServiceAdvertisement(serviceId, endpoint);

            // Test JSON serialization
            var jsonResult = advertisement.ToJsonWithResult();
            if (jsonResult.IsSuccess)
            {
                Console.WriteLine($"✓ JSON serialization successful ({jsonResult.Value.Length} chars)");
            }
            else
            {
                throw new InvalidOperationException($"JSON serialization failed: {jsonResult.ErrorMessage}");
            }

            // Test CBOR serialization
            var cborResult = advertisement.ToCborWithResult();
            if (cborResult.IsSuccess)
            {
                Console.WriteLine($"✓ CBOR serialization successful ({cborResult.Value.Length} bytes)");
            }
            else
            {
                throw new InvalidOperationException($"CBOR serialization failed: {cborResult.ErrorMessage}");
            }

            // Test deserialization
            var deserializedFromJson = ServiceAdvertisement.FromJsonWithResult(jsonResult.Value);
            if (deserializedFromJson.IsSuccess)
            {
                Console.WriteLine("✓ JSON deserialization successful");
            }
            else
            {
                throw new InvalidOperationException($"JSON deserialization failed: {deserializedFromJson.ErrorMessage}");
            }

            var deserializedFromCbor = ServiceAdvertisement.FromCborWithResult(cborResult.Value);
            if (deserializedFromCbor.IsSuccess)
            {
                Console.WriteLine("✓ CBOR deserialization successful");
            }
            else
            {
                throw new InvalidOperationException($"CBOR deserialization failed: {deserializedFromCbor.ErrorMessage}");
            }

            Console.WriteLine();
            await Task.Delay(10); // Brief pause
        }

        /// <summary>
        /// Tests cache effectiveness by serializing the same object multiple times.
        /// </summary>
        private static async Task TestCacheEffectivenessAsync()
        {
            Console.WriteLine("2. Testing Cache Effectiveness");
            Console.WriteLine("------------------------------");

            // Clear cache to start fresh
            SerializationCacheManager.ClearCache();

            // Create test object
            var serviceId = new ServiceIdentifier("cache", "CacheTestService");
            var endpoint = new TransportEndpoint
            {
                Address = "*************",
                Port = 9090,
                Protocol = "tcp"
            };

            var advertisement = new ServiceAdvertisement(serviceId, endpoint);

            // First serialization (should miss cache)
            var initialStats = SerializationCacheManager.GetStatistics();
            var result1 = advertisement.ToJsonWithResult();
            
            if (!result1.IsSuccess)
            {
                throw new InvalidOperationException($"First serialization failed: {result1.ErrorMessage}");
            }

            // Second serialization (should hit cache)
            var result2 = advertisement.ToJsonWithResult();
            
            if (!result2.IsSuccess)
            {
                throw new InvalidOperationException($"Second serialization failed: {result2.ErrorMessage}");
            }

            // Check cache statistics
            var finalStats = SerializationCacheManager.GetStatistics();
            
            Console.WriteLine($"✓ Cache entries: {finalStats.JsonCacheSize}");
            Console.WriteLine($"✓ Cache hits: {finalStats.JsonCacheHits}");
            Console.WriteLine($"✓ Cache misses: {finalStats.JsonCacheMisses}");
            
            if (finalStats.JsonCacheSize > 0)
            {
                Console.WriteLine("✓ Cache is working correctly");
            }
            else
            {
                Console.WriteLine("⚠ Cache appears to be empty (may be disabled)");
            }

            Console.WriteLine();
            await Task.Delay(10); // Brief pause
        }

        /// <summary>
        /// Tests pool statistics and resource management.
        /// </summary>
        private static void TestPoolStatistics()
        {
            Console.WriteLine("3. Testing Pool Statistics");
            Console.WriteLine("--------------------------");

            // Get initial pool statistics
            var initialStats = SerializationPoolManager.GetStatistics();
            Console.WriteLine($"✓ Initial StringBuilder pool: {initialStats.StringBuilderPoolCount}/{initialStats.StringBuilderPoolLimit}");
            Console.WriteLine($"✓ Initial MemoryStream pool: {initialStats.MemoryStreamPoolCount}/{initialStats.MemoryStreamPoolLimit}");

            // Test pool usage
            var sb = SerializationPoolManager.RentStringBuilder();
            var ms = SerializationPoolManager.RentMemoryStream();

            sb.Append("Test string for pool verification");
            ms.WriteByte(42);

            Console.WriteLine($"✓ Rented StringBuilder capacity: {sb.Capacity}");
            Console.WriteLine($"✓ Rented MemoryStream capacity: {ms.Capacity}");

            // Return to pool
            SerializationPoolManager.ReturnStringBuilder(sb);
            SerializationPoolManager.ReturnMemoryStream(ms);

            // Check final statistics
            var finalStats = SerializationPoolManager.GetStatistics();
            Console.WriteLine($"✓ Final StringBuilder pool: {finalStats.StringBuilderPoolCount}/{finalStats.StringBuilderPoolLimit}");
            Console.WriteLine($"✓ Final MemoryStream pool: {finalStats.MemoryStreamPoolCount}/{finalStats.MemoryStreamPoolLimit}");

            Console.WriteLine("✓ Pool management working correctly");
            Console.WriteLine();
        }
    }
}
