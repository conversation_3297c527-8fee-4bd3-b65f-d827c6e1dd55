# Message Structure with UDP Security

```mermaid
graph TD
    subgraph "Original Message"
        A[ServiceAdvertisement Object]
        B[Serialize to CBOR]
        C[Raw CBOR Data<br/>~200 bytes]
    end
    
    subgraph "Security Processing"
        D[Add Timestamp<br/>8 bytes]
        E[Generate HMAC-SHA256<br/>32 bytes]
        F[Secured Message<br/>~240 bytes]
    end
    
    subgraph "Network Transmission"
        G[UDP Packet]
        H[Multicast to 23*************:5353]
    end
    
    subgraph "Reception & Verification"
        I[Receive UDP Packet]
        J[Verify Timestamp<br/>Check replay window]
        K[Verify HMAC<br/>Authenticate message]
        L[Extract Original Data]
        M[Deserialize CBOR]
        N[Process ServiceAdvertisement]
    end
    
    A --> B --> C
    C --> D --> E --> F
    F --> G --> H
    H --> I --> J --> K --> L --> M --> N
    
    style F fill:#ffeb3b
    style J fill:#4caf50
    style K fill:#4caf50
```

## Description

This flowchart shows the complete message transformation process from creation to consumption with UDP security:

### Message Evolution:
1. **Original Message**: ServiceAdvertisement object (~200 bytes when serialized)
2. **Security Processing**: Addition of timestamp (8 bytes) and HMAC-SHA256 (32 bytes)
3. **Network Transmission**: Secured message sent via UDP multicast
4. **Reception & Verification**: Multi-step verification process ensuring message integrity

### Security Components:
- **Timestamp**: Provides replay attack protection with configurable time window
- **HMAC-SHA256**: Ensures message authentication and integrity
- **Total Overhead**: ~40 bytes (20% increase in message size)

### Verification Process:
1. **Timestamp Check**: Ensures message is within acceptable time window (default: 5 minutes)
2. **HMAC Verification**: Cryptographically verifies message hasn't been tampered with
3. **Data Extraction**: Recovers original CBOR data for processing

The highlighted elements (yellow and green) represent the security-specific processing steps that distinguish secured from unsecured communication.
