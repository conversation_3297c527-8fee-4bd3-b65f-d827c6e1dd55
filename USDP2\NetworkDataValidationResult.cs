using System;

namespace USDP2
{
    /// <summary>
    /// Represents the result of network data validation.
    /// </summary>
    public class NetworkDataValidationResult
    {
        /// <summary>
        /// Gets a value indicating whether the validation was successful.
        /// </summary>
        public bool IsValid { get; private set; }

        /// <summary>
        /// Gets the validation error type if validation failed.
        /// </summary>
        public NetworkDataValidationError? ErrorType { get; private set; }

        /// <summary>
        /// Gets the error message if validation failed.
        /// </summary>
        public string? ErrorMessage { get; private set; }

        /// <summary>
        /// Gets the remote address that sent the data.
        /// </summary>
        public string RemoteAddress { get; private set; }

        /// <summary>
        /// Gets the remote port that sent the data.
        /// </summary>
        public int RemotePort { get; private set; }

        /// <summary>
        /// Gets the timestamp when validation was performed.
        /// </summary>
        public DateTimeOffset ValidationTimestamp { get; private set; }

        /// <summary>
        /// Initializes a new instance of the <see cref="NetworkDataValidationResult"/> class.
        /// </summary>
        private NetworkDataValidationResult(
            bool isValid,
            NetworkDataValidationError? errorType,
            string? errorMessage,
            string remoteAddress,
            int remotePort)
        {
            IsValid = isValid;
            ErrorType = errorType;
            ErrorMessage = errorMessage;
            RemoteAddress = remoteAddress;
            RemotePort = remotePort;
            ValidationTimestamp = DateTimeOffset.UtcNow;
        }

        /// <summary>
        /// Creates a successful validation result.
        /// </summary>
        /// <param name="remoteAddress">The remote address that sent the data.</param>
        /// <param name="remotePort">The remote port that sent the data.</param>
        /// <returns>A successful validation result.</returns>
        public static NetworkDataValidationResult Success(string remoteAddress, int remotePort)
        {
            return new NetworkDataValidationResult(true, null, null, remoteAddress, remotePort);
        }

        /// <summary>
        /// Creates a failed validation result.
        /// </summary>
        /// <param name="errorType">The type of validation error.</param>
        /// <param name="errorMessage">The error message.</param>
        /// <param name="remoteAddress">The remote address that sent the data.</param>
        /// <param name="remotePort">The remote port that sent the data.</param>
        /// <returns>A failed validation result.</returns>
        public static NetworkDataValidationResult Failure(
            NetworkDataValidationError errorType,
            string errorMessage,
            string remoteAddress,
            int remotePort)
        {
            return new NetworkDataValidationResult(false, errorType, errorMessage, remoteAddress, remotePort);
        }
    }

    /// <summary>
    /// Represents the result of service advertisement validation.
    /// </summary>
    public class ServiceAdvertisementValidationResult
    {
        /// <summary>
        /// Gets a value indicating whether the validation was successful.
        /// </summary>
        public bool IsValid { get; private set; }

        /// <summary>
        /// Gets the sanitized service advertisement if validation was successful.
        /// </summary>
        public ServiceAdvertisement? SanitizedAdvertisement { get; private set; }

        /// <summary>
        /// Gets the error message if validation failed.
        /// </summary>
        public string? ErrorMessage { get; private set; }

        /// <summary>
        /// Gets the remote address that sent the data.
        /// </summary>
        public string RemoteAddress { get; private set; }

        /// <summary>
        /// Gets the remote port that sent the data.
        /// </summary>
        public int RemotePort { get; private set; }

        /// <summary>
        /// Gets the timestamp when validation was performed.
        /// </summary>
        public DateTimeOffset ValidationTimestamp { get; private set; }

        /// <summary>
        /// Initializes a new instance of the <see cref="ServiceAdvertisementValidationResult"/> class.
        /// </summary>
        private ServiceAdvertisementValidationResult(
            bool isValid,
            ServiceAdvertisement? sanitizedAdvertisement,
            string? errorMessage,
            string remoteAddress,
            int remotePort)
        {
            IsValid = isValid;
            SanitizedAdvertisement = sanitizedAdvertisement;
            ErrorMessage = errorMessage;
            RemoteAddress = remoteAddress;
            RemotePort = remotePort;
            ValidationTimestamp = DateTimeOffset.UtcNow;
        }

        /// <summary>
        /// Creates a successful validation result with sanitized data.
        /// </summary>
        /// <param name="sanitizedAdvertisement">The sanitized service advertisement.</param>
        /// <param name="remoteAddress">The remote address that sent the data.</param>
        /// <param name="remotePort">The remote port that sent the data.</param>
        /// <returns>A successful validation result.</returns>
        public static ServiceAdvertisementValidationResult Success(
            ServiceAdvertisement sanitizedAdvertisement,
            string remoteAddress,
            int remotePort)
        {
            return new ServiceAdvertisementValidationResult(
                true, sanitizedAdvertisement, null, remoteAddress, remotePort);
        }

        /// <summary>
        /// Creates a failed validation result.
        /// </summary>
        /// <param name="errorMessage">The error message.</param>
        /// <param name="remoteAddress">The remote address that sent the data.</param>
        /// <param name="remotePort">The remote port that sent the data.</param>
        /// <returns>A failed validation result.</returns>
        public static ServiceAdvertisementValidationResult Failure(
            string errorMessage,
            string remoteAddress,
            int remotePort)
        {
            return new ServiceAdvertisementValidationResult(
                false, null, errorMessage, remoteAddress, remotePort);
        }
    }

    /// <summary>
    /// Enumeration of network data validation error types.
    /// </summary>
    public enum NetworkDataValidationError
    {
        /// <summary>
        /// The incoming data is null.
        /// </summary>
        NullData,

        /// <summary>
        /// The incoming data is empty.
        /// </summary>
        EmptyData,

        /// <summary>
        /// The data payload exceeds maximum allowed size.
        /// </summary>
        OversizedPayload,

        /// <summary>
        /// The data contains suspicious content patterns.
        /// </summary>
        SuspiciousContent,

        /// <summary>
        /// The data contains potentially malicious content.
        /// </summary>
        MaliciousContent,

        /// <summary>
        /// The data has invalid text encoding.
        /// </summary>
        InvalidEncoding,

        /// <summary>
        /// The data format is invalid or corrupted.
        /// </summary>
        InvalidFormat,

        /// <summary>
        /// The data contains invalid field values.
        /// </summary>
        InvalidFieldValue,

        /// <summary>
        /// The data exceeds nesting depth limits.
        /// </summary>
        ExcessiveNesting,

        /// <summary>
        /// The data contains too many elements.
        /// </summary>
        TooManyElements,

        /// <summary>
        /// The data fails security validation.
        /// </summary>
        SecurityValidationFailure
    }

    /// <summary>
    /// Represents validation statistics for monitoring and alerting.
    /// </summary>
    public class NetworkDataValidationStats
    {
        /// <summary>
        /// Gets or sets the total number of validation attempts.
        /// </summary>
        public long TotalValidations { get; set; }

        /// <summary>
        /// Gets or sets the number of successful validations.
        /// </summary>
        public long SuccessfulValidations { get; set; }

        /// <summary>
        /// Gets or sets the number of failed validations.
        /// </summary>
        public long FailedValidations { get; set; }

        /// <summary>
        /// Gets or sets the number of oversized payload rejections.
        /// </summary>
        public long OversizedPayloadRejections { get; set; }

        /// <summary>
        /// Gets or sets the number of malicious content detections.
        /// </summary>
        public long MaliciousContentDetections { get; set; }

        /// <summary>
        /// Gets or sets the number of suspicious content detections.
        /// </summary>
        public long SuspiciousContentDetections { get; set; }

        /// <summary>
        /// Gets or sets the timestamp of the last validation.
        /// </summary>
        public DateTimeOffset LastValidation { get; set; }

        /// <summary>
        /// Gets or sets the timestamp when stats were last reset.
        /// </summary>
        public DateTimeOffset StatsResetTime { get; set; }

        /// <summary>
        /// Gets the validation success rate as a percentage.
        /// </summary>
        public double SuccessRate => TotalValidations > 0 
            ? (double)SuccessfulValidations / TotalValidations * 100 
            : 0;

        /// <summary>
        /// Gets the validation failure rate as a percentage.
        /// </summary>
        public double FailureRate => TotalValidations > 0 
            ? (double)FailedValidations / TotalValidations * 100 
            : 0;

        /// <summary>
        /// Initializes a new instance of the <see cref="NetworkDataValidationStats"/> class.
        /// </summary>
        public NetworkDataValidationStats()
        {
            StatsResetTime = DateTimeOffset.UtcNow;
        }

        /// <summary>
        /// Records a successful validation.
        /// </summary>
        public void RecordSuccess()
        {
            TotalValidations++;
            SuccessfulValidations++;
            LastValidation = DateTimeOffset.UtcNow;
        }

        /// <summary>
        /// Records a failed validation.
        /// </summary>
        /// <param name="errorType">The type of validation error.</param>
        public void RecordFailure(NetworkDataValidationError errorType)
        {
            TotalValidations++;
            FailedValidations++;
            LastValidation = DateTimeOffset.UtcNow;

            switch (errorType)
            {
                case NetworkDataValidationError.OversizedPayload:
                    OversizedPayloadRejections++;
                    break;
                case NetworkDataValidationError.MaliciousContent:
                    MaliciousContentDetections++;
                    break;
                case NetworkDataValidationError.SuspiciousContent:
                    SuspiciousContentDetections++;
                    break;
            }
        }

        /// <summary>
        /// Resets all statistics.
        /// </summary>
        public void Reset()
        {
            TotalValidations = 0;
            SuccessfulValidations = 0;
            FailedValidations = 0;
            OversizedPayloadRejections = 0;
            MaliciousContentDetections = 0;
            SuspiciousContentDetections = 0;
            StatsResetTime = DateTimeOffset.UtcNow;
        }
    }
}
