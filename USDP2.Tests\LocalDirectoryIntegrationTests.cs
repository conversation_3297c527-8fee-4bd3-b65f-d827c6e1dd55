using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.VisualStudio.TestTools.UnitTesting;

namespace USDP2.Tests
{
    [TestClass]
    public class LocalDirectoryIntegrationTests
    {
        // Use centralized configuration for test values
        private static int TestPort => UsdpConfiguration.Instance.DefaultMulticastPort + 2000; // Offset to avoid conflicts
        private static string TestMulticastAddress => UsdpConfiguration.Instance.DefaultMulticastAddress;

        [TestMethod]
        public async Task LocalDirectory_AnnounceService_SendsMulticastMessage()
        {
            // Arrange
            var sender = new UdpNetworkSender();
            var receiver = new UdpNetworkReceiver(TestPort, true, TestMulticastAddress);
            var localDirectory = new LocalDirectory(sender, receiver, TestMulticastAddress, TestPort);

            // Use centralized configuration for service creation
            var serviceId = new ServiceIdentifier(UsdpConfiguration.Instance.DefaultServiceType, "service1");
            var endpoint = new TransportEndpoint
            {
                Protocol = UsdpConfiguration.Instance.DefaultProtocol,
                Address = UsdpConfiguration.Instance.DefaultServiceAddress,
                Port = UsdpConfiguration.Instance.DefaultServicePort,
                Security = UsdpConfiguration.Instance.DefaultSecurity
            };
            var advertisement = new ServiceAdvertisement(serviceId, endpoint);

            // Act & Assert - Should not throw
            try
            {
                await localDirectory.AnnounceServiceAsync(advertisement);
                Assert.IsTrue(true); // If we reach here, announcement succeeded
            }
            catch (Exception ex)
            {
                // In test environment, multicast might not work properly
                Assert.IsTrue(ex is InvalidOperationException || ex is System.Net.Sockets.SocketException,
                    $"Unexpected exception type: {ex.GetType()}");
            }
            finally
            {
                await localDirectory.DisposeAsync();
            }
        }

        [TestMethod]
        public async Task LocalDirectory_StartAsync_StartsReceiving()
        {
            // Arrange
            var sender = new UdpNetworkSender();
            var receiver = new UdpNetworkReceiver(TestPort + 1, false, null);
            var localDirectory = new LocalDirectory(sender, receiver, "127.0.0.1", TestPort + 1);

            // Act
            var startTask = Task.Run(async () =>
            {
                try
                {
                    await localDirectory.StartAsync(CancellationToken.None);
                }
                catch (Exception)
                {
                    // Expected in test environment
                }
            });

            // Give it a moment to start - use centralized configuration
            await Task.Delay(UsdpConfiguration.Instance.StartupDelay);

            // Assert - Should not throw during startup
            Assert.IsTrue(true);

            // Cleanup
            await localDirectory.DisposeAsync();
        }

        [TestMethod]
        public async Task LocalDirectory_GetCachedAdvertisements_ReturnsEmptyInitially()
        {
            // Arrange
            var sender = new UdpNetworkSender();
            var receiver = new UdpNetworkReceiver(TestPort + 2, false, null);
            var localDirectory = new LocalDirectory(sender, receiver, "127.0.0.1", TestPort + 2);

            // Act
            var advertisements = localDirectory.GetCachedAdvertisements();

            // Assert
            Assert.IsNotNull(advertisements);
            Assert.AreEqual(0, advertisements.Count());

            // Cleanup
            await localDirectory.DisposeAsync();
        }

        [TestMethod]
        public async Task LocalDirectory_AnnounceService_AddsToCache()
        {
            // Arrange
            var sender = new UdpNetworkSender();
            var receiver = new UdpNetworkReceiver(TestPort + 3, false, null);
            var localDirectory = new LocalDirectory(sender, receiver, "127.0.0.1", TestPort + 3);

            // Use centralized configuration for service creation
            var serviceId = new ServiceIdentifier(UsdpConfiguration.Instance.DefaultServiceType, "service2");
            var endpoint = new TransportEndpoint
            {
                Protocol = UsdpConfiguration.Instance.DefaultProtocol,
                Address = UsdpConfiguration.Instance.DefaultServiceAddress,
                Port = UsdpConfiguration.Instance.DefaultServicePort + 1, // Slight offset for uniqueness
                Security = UsdpConfiguration.Instance.DefaultSecurity
            };
            var advertisement = new ServiceAdvertisement(serviceId, endpoint);

            // Act
            try
            {
                await localDirectory.AnnounceServiceAsync(advertisement);

                // Assert
                var advertisements = localDirectory.GetCachedAdvertisements();
                Assert.AreEqual(1, advertisements.Count());
                Assert.AreEqual(serviceId.FullName, advertisements.First().ServiceId.FullName);
            }
            catch (Exception)
            {
                // In test environment, network operations might fail
                // But the service should still be added to cache
                var advertisements = localDirectory.GetCachedAdvertisements();
                Assert.AreEqual(1, advertisements.Count());
            }
            finally
            {
                await localDirectory.DisposeAsync();
            }
        }

        [TestMethod]
        public async Task LocalDirectory_DisposeAsync_ProperlyDisposesResources()
        {
            // Arrange
            var sender = new UdpNetworkSender();
            var receiver = new UdpNetworkReceiver(TestPort + 4, false, null);
            var localDirectory = new LocalDirectory(sender, receiver, "127.0.0.1", TestPort + 4);

            // Act & Assert - Should not throw
            await localDirectory.DisposeAsync();
            Assert.IsTrue(true);
        }

        [TestMethod]
        public async Task LocalDirectory_Dispose_ProperlyDisposesResources()
        {
            // Arrange
            var sender = new UdpNetworkSender();
            var receiver = new UdpNetworkReceiver(TestPort + 5, false, null);
            var localDirectory = new LocalDirectory(sender, receiver, "127.0.0.1", TestPort + 5);

            // Act & Assert - Should not throw
            localDirectory.Dispose();
            Assert.IsTrue(true);
        }

        [TestMethod]
        public void LocalDirectory_Constructor_ValidParameters_Success()
        {
            // Arrange & Act
            var sender = new UdpNetworkSender();
            var receiver = new UdpNetworkReceiver(TestPort + 6, false, null);
            var localDirectory = new LocalDirectory(sender, receiver, "127.0.0.1", TestPort + 6);

            // Assert
            Assert.IsNotNull(localDirectory);

            // Cleanup
            localDirectory.Dispose();
        }

        [TestMethod]
        public void LocalDirectory_Constructor_NullSender_ThrowsArgumentNullException()
        {
            // Arrange
            var receiver = new UdpNetworkReceiver(TestPort + 7, false, null);

            // Act & Assert - Constructor now validates null parameters
            Assert.ThrowsException<ArgumentNullException>(
                () => new LocalDirectory(null!, receiver, "127.0.0.1", TestPort + 7));
        }

        [TestMethod]
        public void LocalDirectory_Constructor_NullReceiver_ThrowsArgumentNullException()
        {
            // Arrange
            var sender = new UdpNetworkSender();

            // Act & Assert - Constructor now validates null parameters
            Assert.ThrowsException<ArgumentNullException>(
                () => new LocalDirectory(sender, null!, "127.0.0.1", TestPort + 8));
        }

        [TestMethod]
        public void LocalDirectory_Constructor_NullMulticastAddress_ThrowsArgumentException()
        {
            // Arrange
            var sender = new UdpNetworkSender();
            var receiver = new UdpNetworkReceiver(TestPort + 9, false, null);

            // Act & Assert
            Assert.ThrowsException<ArgumentException>(
                () => new LocalDirectory(sender, receiver, null!, TestPort + 9));
        }

        [TestMethod]
        public void LocalDirectory_Constructor_EmptyMulticastAddress_ThrowsArgumentException()
        {
            // Arrange
            var sender = new UdpNetworkSender();
            var receiver = new UdpNetworkReceiver(TestPort + 10, false, null);

            // Act & Assert
            Assert.ThrowsException<ArgumentException>(
                () => new LocalDirectory(sender, receiver, "", TestPort + 10));
        }

        [TestMethod]
        public void LocalDirectory_Constructor_InvalidPort_ThrowsArgumentOutOfRangeException()
        {
            // Arrange
            var sender = new UdpNetworkSender();
            var receiver = new UdpNetworkReceiver(TestPort + 11, false, null);

            // Act & Assert
            Assert.ThrowsException<ArgumentOutOfRangeException>(
                () => new LocalDirectory(sender, receiver, "127.0.0.1", 0));
        }

        [TestMethod]
        public async Task LocalDirectory_AnnounceService_NullAdvertisement_ThrowsArgumentNullException()
        {
            // Arrange
            var sender = new UdpNetworkSender();
            var receiver = new UdpNetworkReceiver(TestPort + 9, false, null);
            var localDirectory = new LocalDirectory(sender, receiver, "127.0.0.1", TestPort + 9);

            // Act & Assert
            await Assert.ThrowsExceptionAsync<ArgumentNullException>(
                () => localDirectory.AnnounceServiceAsync(null!));

            // Cleanup
            await localDirectory.DisposeAsync();
        }
    }
}
