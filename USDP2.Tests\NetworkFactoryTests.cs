using System;
using Microsoft.VisualStudio.TestTools.UnitTesting;

namespace USDP2.Tests
{
    [TestClass]
    public class NetworkFactoryTests
    {
        [TestMethod]
        public void NetworkSenderFactory_CreateSender_LocalScope_ReturnsUdpSender()
        {
            // Act
            var sender = NetworkSenderFactory.CreateSender(NetworkScope.Local);

            try
            {
                // Assert
                Assert.IsInstanceOfType(sender, typeof(UdpNetworkSender));
                Assert.IsInstanceOfType(sender, typeof(IDisposable));
            }
            finally
            {
                // Cleanup
                if (sender is IDisposable disposable)
                {
                    disposable.Dispose();
                }
            }
        }

        [TestMethod]
        public void NetworkSenderFactory_CreateSender_GlobalScope_ReturnsHttpSender()
        {
            // Act
            var sender = NetworkSenderFactory.CreateSender(NetworkScope.Global);

            try
            {
                // Assert
                Assert.IsInstanceOfType(sender, typeof(HttpNetworkSender));
                Assert.IsInstanceOfType(sender, typeof(IDisposable));
            }
            finally
            {
                // Cleanup
                if (sender is IDisposable disposable)
                {
                    disposable.Dispose();
                }
            }
        }

        [TestMethod]
        public void NetworkSenderFactory_CreateSender_InvalidScope_ThrowsArgumentOutOfRangeException()
        {
            // Act & Assert
            Assert.ThrowsException<ArgumentOutOfRangeException>(
                () => NetworkSenderFactory.CreateSender((NetworkScope)999));
        }

        [TestMethod]
        public void NetworkSenderFactory_CreateSender_WithConfiguration_PassesConfigurationCorrectly()
        {
            // Arrange
            var config = new UsdpConfiguration(forTesting: true)
            {
                EnableUdpSecurity = false
            };

            // Act
            var udpSender = NetworkSenderFactory.CreateSender(NetworkScope.Local, config);
            var httpSender = NetworkSenderFactory.CreateSender(NetworkScope.Global, config);

            try
            {
                // Assert
                Assert.IsInstanceOfType(udpSender, typeof(UdpNetworkSender));
                Assert.IsInstanceOfType(httpSender, typeof(HttpNetworkSender));
                Assert.IsInstanceOfType(udpSender, typeof(IDisposable));
                Assert.IsInstanceOfType(httpSender, typeof(IDisposable));
            }
            finally
            {
                // Cleanup
                if (udpSender is IDisposable udpDisposable)
                {
                    udpDisposable.Dispose();
                }
                if (httpSender is IDisposable httpDisposable)
                {
                    httpDisposable.Dispose();
                }
            }
        }

        [TestMethod]
        public void NetworkReceiverFactory_CreateReceiver_LocalScope_ReturnsUdpReceiver()
        {
            // Act
            var receiver = NetworkReceiverFactory.CreateReceiver(NetworkScope.Local, 12345);

            // Assert
            Assert.IsInstanceOfType(receiver, typeof(UdpNetworkReceiver));
        }

        [TestMethod]
        public void NetworkReceiverFactory_CreateReceiver_LocalScopeWithMulticast_ReturnsUdpReceiver()
        {
            // Act
            var receiver = NetworkReceiverFactory.CreateReceiver(NetworkScope.Local, 12345, "239.255.255.250");

            // Assert
            Assert.IsInstanceOfType(receiver, typeof(UdpNetworkReceiver));
        }

        [TestMethod]
        public void NetworkReceiverFactory_CreateReceiver_GlobalScope_ReturnsHttpReceiver()
        {
            // Act
            var receiver = NetworkReceiverFactory.CreateReceiver(NetworkScope.Global, 8080);

            // Assert
            Assert.IsInstanceOfType(receiver, typeof(HttpNetworkReceiver));
        }

        [TestMethod]
        public void NetworkReceiverFactory_CreateReceiver_InvalidScope_ThrowsArgumentOutOfRangeException()
        {
            // Act & Assert
            Assert.ThrowsException<ArgumentOutOfRangeException>(
                () => NetworkReceiverFactory.CreateReceiver((NetworkScope)999, 12345));
        }

        [TestMethod]
        public void NetworkReceiverFactory_CreateReceiver_ValidParameters_LogsCreation()
        {
            // This test verifies that the factory logs the creation event
            // In a real scenario, you might want to mock the Diagnostics.Log method

            // Act
            var receiver = NetworkReceiverFactory.CreateReceiver(NetworkScope.Local, 12345, "239.255.255.250");

            // Assert
            Assert.IsNotNull(receiver);
            // Note: In a more sophisticated test setup, you would verify that Diagnostics.Log was called
        }

        [TestMethod]
        public void NetworkReceiverFactory_CreateReceiver_ExceptionDuringCreation_LogsError()
        {
            // This test would require mocking the constructor to throw an exception
            // For now, we'll test with invalid parameters that might cause issues

            // Act & Assert
            try
            {
                // This might throw depending on the implementation
                var receiver = NetworkReceiverFactory.CreateReceiver(NetworkScope.Local, -1);
                Assert.IsNotNull(receiver); // If no exception, that's also valid
            }
            catch (Exception)
            {
                // Exception is expected for invalid port
                Assert.IsTrue(true);
            }
        }
    }

    [TestClass]
    public class NetworkScopeTests
    {
        [TestMethod]
        public void NetworkScope_Local_HasCorrectValue()
        {
            // Assert
            Assert.AreEqual(0, (int)NetworkScope.Local);
        }

        [TestMethod]
        public void NetworkScope_Global_HasCorrectValue()
        {
            // Assert
            Assert.AreEqual(1, (int)NetworkScope.Global);
        }
    }

    [TestClass]
    public class ReceiverStartModeTests
    {
        [TestMethod]
        public void ReceiverStartMode_Await_HasCorrectValue()
        {
            // Assert
            Assert.AreEqual(0, (int)ReceiverStartMode.Await);
        }

        [TestMethod]
        public void ReceiverStartMode_FireAndForget_HasCorrectValue()
        {
            // Assert
            Assert.AreEqual(1, (int)ReceiverStartMode.FireAndForget);
        }

        [TestMethod]
        public void ReceiverStartMode_ThreadPool_HasCorrectValue()
        {
            // Assert
            Assert.AreEqual(2, (int)ReceiverStartMode.ThreadPool);
        }
    }
}
