# Service Query Flow with Mixed Security Settings

```mermaid
sequenceDiagram
    participant B as Instance B<br/>(Security Enabled)
    participant Net as Multicast Network<br/>(***************:5353)
    participant A as Instance A<br/>(Security Enabled)
    participant C as Instance C<br/>(Security Disabled)
    
    Note over B,C: Mixed security configuration scenario
    
    B->>B: Create ServiceQuery<br/>{"metadataFilter": {"type": "lighting"}}
    B->>B: Serialize to CBOR
    B->>B: UdpSecurityOverride.SecureData()<br/>Add security wrapper
    B->>Net: Send secured query
    
    Net->>A: Receive secured query
    Net->>C: Receive secured query
    
    A->>A: UdpSecurityOverride.VerifyAndExtractData()<br/>✅ Success - same key
    A->>A: Deserialize to ServiceQuery
    A->>A: Check BloomFilter for matches
    A->>A: Find matching services
    A->>A: Serialize responses to CBOR
    A->>A: UdpSecurityOverride.SecureData()
    A->>Net: Send secured responses
    
    C->>C: UdpSecurityOverride.VerifyAndExtractData()<br/>❌ Fails - no security enabled
    C->>C: Treat as unsecured message<br/>Process original secured data as CBOR
    C->>C: ❌ CBOR deserialization fails
    C->>C: Log unknown message, ignore
    
    Net->>B: Receive secured responses from A
    Net->>C: Receive secured responses from A
    
    B->>B: VerifyAndExtractData() ✅ Success
    B->>B: Process service responses
    
    C->>C: VerifyAndExtractData() fails<br/>Treat as unsecured, ignore
    
    Note over B,C: Only B receives A's responses due to security mismatch
```

## Description

This diagram illustrates the communication flow in a mixed security environment where:

- **Instance A & B**: Have UDP security enabled with shared authentication keys
- **Instance C**: Has UDP security disabled

### Key Behaviors:

1. **Secured Communication**: Instances A and B can communicate securely
2. **Security Isolation**: Instance C cannot process secured messages from A and B
3. **Graceful Degradation**: Instance C logs unknown messages but doesn't crash
4. **Selective Participation**: Only instances with matching security configuration can participate in secured communication

This scenario demonstrates how UDP security creates natural security boundaries within the network.
