using System;
using System.Collections;
using System.Linq;
using System.Security.Cryptography;
using System.Text;

namespace USDP2
{
    /// <summary>
    /// A high-performance Bloom Filter implementation for string keys used in USDP2 service discovery optimization.
    ///
    /// <para>
    /// A Bloom filter is a space-efficient probabilistic data structure that tests whether an element is a member of a set.
    /// It may produce false positives (saying an item is present when it's not) but never false negatives
    /// (if it says an item is not present, it's definitely not present).
    /// </para>
    ///
    /// <para><strong>Key Features:</strong></para>
    /// <list type="bullet">
    /// <item><description>Fast membership testing: O(k) where k is the number of hash functions</description></item>
    /// <item><description>Memory efficient: Uses bit arrays instead of storing actual data</description></item>
    /// <item><description>No false negatives: Guaranteed accuracy for negative results</description></item>
    /// <item><description>Configurable false positive rate: Trade-off between memory usage and accuracy</description></item>
    /// <item><description>Prime-sized filters: Automatically adjusts size to nearest prime for better hash distribution</description></item>
    /// </list>
    ///
    /// <para><strong>Implementation Details:</strong></para>
    /// <list type="bullet">
    /// <item><description>Uses Kirsch-Mitzenmacher technique to generate k hash functions from 2 base hashes</description></item>
    /// <item><description>Employs FNV-1a hash algorithm for fast, well-distributed hashing</description></item>
    /// <item><description>Supports up to 100 million bits (~12.5 MB) to prevent excessive memory usage</description></item>
    /// <item><description>Thread-safe for concurrent read operations (Add operations should be synchronized externally)</description></item>
    /// </list>
    ///
    /// <para><strong>Use Cases in USDP2:</strong></para>
    /// <list type="bullet">
    /// <item><description>Service discovery optimization: Quickly eliminate non-matching services</description></item>
    /// <item><description>Metadata filtering: Fast checks for service metadata presence</description></item>
    /// <item><description>Network efficiency: Reduce unnecessary detailed queries in large networks</description></item>
    /// </list>
    ///
    /// <para><strong>Performance Characteristics:</strong></para>
    /// <list type="bullet">
    /// <item><description>False Positive Rate: Approximately (1 - e^(-kn/m))^k where k=hash functions, n=items, m=filter size</description></item>
    /// <item><description>Optimal k: (m/n) * ln(2) for minimum false positive rate</description></item>
    /// <item><description>Memory Usage: m bits = m/8 bytes (independent of number of items stored)</description></item>
    /// </list>
    ///
    /// <para><strong>Error Handling:</strong></para>
    /// <para>
    /// If hash generation fails, an empty array is returned, causing the filter to treat the item as "not present".
    /// This fail-safe behavior ensures that hash failures don't cause false positives, maintaining the filter's
    /// core guarantee of no false negatives.
    /// </para>
    ///
    /// <example>
    /// <code>
    /// // Create a filter optimized for 10,000 items with ~1% false positive rate
    /// var filter = new BloomFilter(size: 100000, hashFunctions: 7);
    ///
    /// // Add service identifiers
    /// filter.Add("home/lighting");
    /// filter.Add("office/hvac");
    ///
    /// // Test membership
    /// bool mightBePresent = filter.MightContain("home/lighting"); // true
    /// bool definitelyNotPresent = filter.MightContain("factory/sensors"); // false (or true if false positive)
    /// </code>
    /// </example>
    /// </summary>
    public class BloomFilter
    {
        /// <summary>
        /// The bit array that stores the bloom filter data.
        /// </summary>
        private readonly BitArray _bits;
        /// <summary>
        /// The number of hash functions used for each item.
        /// </summary>
        private readonly int _hashFunctions;

        /// <summary>
        /// The size of the bit array.
        /// </summary>
        private readonly int _size;
        /// <summary>
        /// The maximum allowed size for the bit array to prevent excessive memory usage.
        /// 100 million bits is approximately 12.5 MB.
        /// </summary>
        private const int MaxSize = 100_000_000;

        /// <summary>
        /// Initializes a new instance of the BloomFilter class with specified parameters.
        /// </summary>
        /// <param name="size">
        /// The desired size of the bit array (number of bits). If the specified size is not prime,
        /// the next larger prime number will be used to improve hash distribution and reduce clustering.
        ///
        /// <para><strong>Size Guidelines:</strong></para>
        /// <list type="bullet">
        /// <item><description>100,000 bits (~12.5 KB): Good for up to 1,000 items</description></item>
        /// <item><description>1,000,000 bits (~125 KB): Good for up to 10,000 items</description></item>
        /// <item><description>10,000,000 bits (~1.25 MB): Good for up to 100,000 items</description></item>
        /// </list>
        ///
        /// <para><strong>Range:</strong> 1 to 100,000,000 (maximum ~12.5 MB)</para>
        /// </param>
        /// <param name="hashFunctions">
        /// The number of independent hash functions to use for each item. More hash functions reduce
        /// false positive rates but increase computation time.
        ///
        /// <para><strong>Optimal Selection:</strong></para>
        /// <para>For minimum false positive rate: k = (m/n) * ln(2)</para>
        /// <para>Where m = filter size, n = expected number of items</para>
        ///
        /// <para><strong>Common Values:</strong></para>
        /// <list type="bullet">
        /// <item><description>3-5 functions: Fast, ~5-10% false positive rate</description></item>
        /// <item><description>6-8 functions: Balanced, ~1-3% false positive rate</description></item>
        /// <item><description>9-12 functions: Accurate, ~0.1-1% false positive rate</description></item>
        /// </list>
        ///
        /// <para><strong>Range:</strong> 1 to 20 (practical upper limit)</para>
        /// </param>
        /// <exception cref="ArgumentOutOfRangeException">
        /// Thrown when <paramref name="size"/> is less than or equal to 0, greater than 100,000,000,
        /// or when <paramref name="hashFunctions"/> is less than or equal to 0.
        /// </exception>
        /// <remarks>
        /// The constructor automatically optimizes the filter size by using the next prime number if the
        /// specified size is not prime. This improves hash distribution and reduces the likelihood of
        /// hash collisions that could increase false positive rates.
        /// </remarks>
        public BloomFilter(int size, int hashFunctions)
        {
            if (size <= 0) throw new ArgumentOutOfRangeException(nameof(size), "Size must be positive.");
            if (size > MaxSize) throw new ArgumentOutOfRangeException(nameof(size), $"Size must not exceed {MaxSize} to prevent excessive memory usage.");
            if (hashFunctions <= 0) throw new ArgumentOutOfRangeException(nameof(hashFunctions), "Number of hash functions must be positive.");

            // Use a prime number for the size to improve hash distribution
            _size = IsPrime(size) ? size : GetNextPrime(size);
            _hashFunctions = hashFunctions;
            _bits = new BitArray(_size);
        }
        /// <summary>
        /// Adds an item to the Bloom filter by setting the corresponding bits.
        /// </summary>
        /// <param name="item">
        /// The string item to add to the filter. The item is hashed using multiple hash functions,
        /// and the corresponding bit positions are set to true.
        /// </param>
        /// <remarks>
        /// <para><strong>Performance:</strong> O(k) where k is the number of hash functions.</para>
        /// <para><strong>Thread Safety:</strong> This method is not thread-safe. External synchronization
        /// is required if multiple threads will be adding items concurrently.</para>
        /// <para><strong>Idempotent:</strong> Adding the same item multiple times has no additional effect.</para>
        /// <para><strong>Error Handling:</strong> If hash generation fails, the item is silently ignored
        /// to maintain the filter's integrity (no false negatives guarantee).</para>
        /// </remarks>
        /// <example>
        /// <code>
        /// var filter = new BloomFilter(10000, 5);
        /// filter.Add("service/lighting");
        /// filter.Add("service/hvac");
        /// // Items are now in the filter and will return true for MightContain()
        /// </code>
        /// </example>
        public void Add(string item)
        {
            foreach (var position in GetHashes(item))
                _bits[position] = true;
        }

        /// <summary>
        /// Tests whether the Bloom filter might contain the specified item.
        /// </summary>
        /// <param name="item">
        /// The string item to test for membership in the filter.
        /// </param>
        /// <returns>
        /// <para><c>true</c> if the item might be in the set (subject to false positives).</para>
        /// <para><c>false</c> if the item is definitely not in the set (guaranteed accurate).</para>
        /// </returns>
        /// <remarks>
        /// <para><strong>Accuracy Guarantees:</strong></para>
        /// <list type="bullet">
        /// <item><description><strong>No False Negatives:</strong> If this method returns false, the item was definitely not added to the filter.</description></item>
        /// <item><description><strong>Possible False Positives:</strong> If this method returns true, the item might or might not have been added to the filter.</description></item>
        /// </list>
        ///
        /// <para><strong>Performance:</strong> O(k) where k is the number of hash functions.</para>
        /// <para><strong>Thread Safety:</strong> This method is thread-safe for concurrent reads.</para>
        ///
        /// <para><strong>False Positive Rate:</strong></para>
        /// <para>The probability of false positives increases as more items are added to the filter.
        /// The rate can be estimated using: (1 - e^(-kn/m))^k where k=hash functions, n=items added, m=filter size.</para>
        /// </remarks>
        /// <example>
        /// <code>
        /// var filter = new BloomFilter(10000, 5);
        /// filter.Add("service/lighting");
        ///
        /// bool result1 = filter.MightContain("service/lighting"); // Always true (item was added)
        /// bool result2 = filter.MightContain("service/unknown");   // Usually false, but could be true (false positive)
        /// </code>
        /// </example>
        public bool MightContain(string item) => GetHashes(item).All(position => _bits[position]);



        /// <summary>
        /// Generates multiple independent hash values for the specified item using the Kirsch-Mitzenmacher technique.
        /// </summary>
        /// <param name="item">The string item to hash.</param>
        /// <returns>
        /// An array of integer hash values representing bit positions in the filter array.
        /// Returns an empty array if hash generation fails to maintain filter integrity.
        /// </returns>
        /// <remarks>
        /// <para><strong>Algorithm:</strong> Kirsch-Mitzenmacher Technique</para>
        /// <para>This method uses the Kirsch-Mitzenmacher technique to generate k independent hash functions
        /// from just 2 base hash functions. This is both mathematically sound and computationally efficient.</para>
        ///
        /// <para><strong>Formula:</strong> h_i(x) = (h1(x) + i * h2(x)) % m</para>
        /// <para>Where h1 and h2 are independent FNV-1a hash functions with different seeds.</para>
        ///
        /// <para><strong>Hash Function Properties:</strong></para>
        /// <list type="bullet">
        /// <item><description><strong>FNV-1a Algorithm:</strong> Fast, simple, and provides good distribution</description></item>
        /// <item><description><strong>Independent Seeds:</strong> Ensures h1 and h2 are truly independent</description></item>
        /// <item><description><strong>Overflow Safe:</strong> Uses unchecked arithmetic to handle overflow gracefully</description></item>
        /// <item><description><strong>Prime Modulo:</strong> Filter size is adjusted to prime numbers for better distribution</description></item>
        /// </list>
        ///
        /// <para><strong>Error Handling:</strong></para>
        /// <para>If any exception occurs during hash generation, an empty array is returned. This ensures that
        /// hash failures don't compromise the filter's core guarantee of no false negatives.</para>
        /// </remarks>
        private int[] GetHashes(string item)
        {
            try
            {
                var hashes = new int[_hashFunctions];


                /*******************************************
                 * KIRSCH-MITZENMACHER TECHNIQUE
                 * Uses two independent hash functions to generate k hash functions.
                 * This is mathematically proven to provide good independence.
                 * Formula: h_i(x) = (h1(x) + i * h2(x)) % m
                 *******************************************/

                var data = Encoding.UTF8.GetBytes(item);

                // Get two independent hash values using FNV-1a hash algorithm with different seeds
                uint hash1 = FNV1a(data);
                uint hash2 = FNV1a(data, GenerateSecondSeed(_size));

                // Generate k hash functions using proper overflow handling
                for (int i = 0; i < _hashFunctions; i++)
                {
                    // Use long arithmetic to prevent overflow, then reduce to size
                    long combinedHash = (long)hash1 + ((long)i * (long)hash2);

                    // Take modulo to get final hash position
                    int hashPosition = (int)(combinedHash % _size);

                    // Ensure positive result
                    if (hashPosition < 0)
                        hashPosition += _size;

                    hashes[i] = hashPosition;
                }

                return hashes;
            }
            catch (System.Exception ex)
            {
                Log("GetHashesException", new { Item = item, Error = ex.Message });
                return Array.Empty<int>(); // Or throw, depending on your error handling strategy
            }
        }
        /// <summary>
        /// Generates a unique seed for the second hash function based on the Bloom filter size.
        /// </summary>
        /// <param name="size">The size of the Bloom filter bit array.</param>
        /// <returns>
        /// A seed value that provides good distribution for the second hash function and ensures
        /// independence from the first hash function.
        /// </returns>
        /// <remarks>
        /// <para><strong>Purpose:</strong></para>
        /// <para>The Kirsch-Mitzenmacher technique requires two independent hash functions. This method
        /// generates a unique seed for the second FNV-1a hash function that is mathematically independent
        /// from the first hash function's default seed.</para>
        ///
        /// <para><strong>Algorithm:</strong></para>
        /// <list type="number">
        /// <item><description>Start with FNV offset basis (2166136261)</description></item>
        /// <item><description>XOR with the filter size to make it size-dependent</description></item>
        /// <item><description>Multiply by FNV prime (16777619) for distribution</description></item>
        /// <item><description>XOR with large prime (4294967291) for additional mixing</description></item>
        /// <item><description>Ensure result is never 0 or equal to default seed</description></item>
        /// </list>
        ///
        /// <para><strong>Mathematical Properties:</strong></para>
        /// <list type="bullet">
        /// <item><description><strong>Size Dependency:</strong> Different filter sizes produce different seeds</description></item>
        /// <item><description><strong>Prime Mixing:</strong> Uses large primes to ensure good bit distribution</description></item>
        /// <item><description><strong>Collision Avoidance:</strong> Prevents seed from being 0 or default FNV seed</description></item>
        /// <item><description><strong>Overflow Safe:</strong> Uses unchecked arithmetic to handle overflow gracefully</description></item>
        /// </list>
        /// </remarks>
        private static uint GenerateSecondSeed(int size)
        {
            // Start with a prime number (FNV offset basis)
            const uint baseSeed = 2166136261;

            // Use several prime numbers to create a unique seed based on the size
            // This helps ensure good distribution and reduces the chance of collisions
            const uint prime1 = 16777619; // FNV prime
            const uint prime2 = 4294967291; // Largest prime under 2^32

            // Combine the size with the primes in a way that distributes well
            // Use unchecked arithmetic to allow overflow (this is safe for hash functions)
            uint seed;
            unchecked
            {
                seed = baseSeed;
                seed ^= (uint)size;
                seed *= prime1;
                seed ^= prime2;
            }

            // Ensure the seed is never 0 or equal to the default FNV offset basis
            if (seed == 0 || seed == baseSeed)
                seed = 1640531527; // Fallback to 2^32 / golden ratio

            return seed;
        }

        /// <summary>
        /// Checks if a number is prime.
        /// </summary>
        /// <param name="number">The number to check.</param>
        /// <returns>True if the number is prime, false otherwise.</returns>
        private static bool IsPrime(int number)
        {
            if (number <= 1) return false;
            if (number <= 3) return true;
            if (number % 2 == 0 || number % 3 == 0) return false;

            // Check using 6k +/- 1 optimization
            int i = 5;
            while (i * i <= number)
            {
                if (number % i == 0 || number % (i + 2) == 0)
                    return false;
                i += 6;
            }
            return true;
        }

        /// <summary>
        /// Finds the next prime number greater than or equal to the given number.
        /// </summary>
        /// <param name="number">The starting number.</param>
        /// <returns>The next prime number.</returns>
        private static int GetNextPrime(int number)
        {
            // Handle small numbers
            if (number <= 1) return 2;

            // Start with the given number
            int prime = number;

            // If even, add 1 to make it odd
            if (prime % 2 == 0)
                prime++;

            // Keep checking until we find a prime
            while (!IsPrime(prime))
            {
                prime += 2; // Skip even numbers

                // Safety check to prevent excessive computation
                if (prime > MaxSize)
                {
                    // If we exceed MaxSize, return a known large prime below MaxSize
                    return 99999989; // A prime just below 100 million
                }
            }

            return prime;
        }

        /// <summary>
        /// Implements the FNV-1a (Fowler-Noll-Vo) hash algorithm for fast, high-quality string hashing.
        /// </summary>
        /// <param name="data">The byte array data to hash (typically UTF-8 encoded string).</param>
        /// <param name="seed">
        /// The initial hash value (seed). Default is the FNV offset basis (2166136261).
        /// Different seeds produce independent hash functions for the same input data.
        /// </param>
        /// <returns>A 32-bit unsigned integer hash value with good distribution properties.</returns>
        /// <remarks>
        /// <para><strong>Algorithm: FNV-1a (Fowler-Noll-Vo variant 1a)</strong></para>
        /// <para>FNV-1a is a non-cryptographic hash function known for its simplicity and excellent distribution
        /// properties. It's particularly well-suited for hash tables and Bloom filters.</para>
        ///
        /// <para><strong>Algorithm Steps:</strong></para>
        /// <list type="number">
        /// <item><description>Initialize hash with the seed value</description></item>
        /// <item><description>For each byte: XOR hash with byte, then multiply by FNV prime</description></item>
        /// <item><description>Return final hash value</description></item>
        /// </list>
        ///
        /// <para><strong>Properties:</strong></para>
        /// <list type="bullet">
        /// <item><description><strong>Fast:</strong> Simple operations (XOR, multiply) make it very fast</description></item>
        /// <item><description><strong>Good Distribution:</strong> Produces well-distributed hash values</description></item>
        /// <item><description><strong>Low Collision Rate:</strong> Minimizes hash collisions for typical data</description></item>
        /// <item><description><strong>Avalanche Effect:</strong> Small input changes cause large output changes</description></item>
        /// <item><description><strong>Deterministic:</strong> Same input always produces same output</description></item>
        /// </list>
        ///
        /// <para><strong>Constants:</strong></para>
        /// <list type="bullet">
        /// <item><description><strong>FNV Prime:</strong> 16777619 (chosen for optimal distribution)</description></item>
        /// <item><description><strong>FNV Offset Basis:</strong> 2166136261 (default seed value)</description></item>
        /// </list>
        ///
        /// <para><strong>Overflow Handling:</strong></para>
        /// <para>Uses unchecked arithmetic to allow integer overflow, which is safe and expected in hash functions.
        /// The overflow behavior is part of the algorithm's design and contributes to good distribution.</para>
        /// </remarks>
        private static uint FNV1a(byte[] data, uint seed = 2166136261) // FNV offset basis
        {
            const uint FNV_PRIME = 16777619;
            uint hash = seed;

            unchecked // Allow overflow in hash calculations
            {
                foreach (byte b in data)
                {
                    hash ^= b;
                    hash *= FNV_PRIME;
                }
            }

            return hash;
        }
        /// <summary>
        /// Logs Bloom filter-specific events using the USDP2 diagnostics system.
        /// </summary>
        /// <param name="eventType">
        /// The type of event being logged (e.g., "GetHashesException", "FilterCreated").
        /// Will be prefixed with "BloomFilter." for categorization.
        /// </param>
        /// <param name="data">
        /// The event data to log. Typically an anonymous object containing relevant information
        /// such as error messages, performance metrics, or operational details.
        /// </param>
        /// <remarks>
        /// <para>This method integrates with the USDP2 diagnostics system to provide structured logging
        /// for Bloom filter operations. All events are automatically prefixed with "BloomFilter." to
        /// distinguish them from other system events.</para>
        ///
        /// <para><strong>Common Event Types:</strong></para>
        /// <list type="bullet">
        /// <item><description><strong>GetHashesException:</strong> Hash generation failures</description></item>
        /// <item><description><strong>FilterInitialized:</strong> Filter creation events</description></item>
        /// <item><description><strong>PerformanceMetrics:</strong> Timing and efficiency data</description></item>
        /// </list>
        /// </remarks>
        private static void Log(string eventType, object data)
        {
            Diagnostics.Log($"BloomFilter.{eventType}", data);
        }

    }
};

