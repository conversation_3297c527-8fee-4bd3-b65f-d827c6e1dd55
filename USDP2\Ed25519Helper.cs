using System;
using System.Security.Cryptography;

namespace USDP2
{
    /// <summary>
    /// Helper for Ed25519 signing and verification.
    /// </summary>
    public static class Ed25519Helper
    {
        /// <summary>
        /// Signs the given data using the provided Ed25519 private key.
        /// </summary>
        /// <param name="data">The data to be signed.</param>
        /// <param name="privateKey">The Ed25519 private key used for signing.</param>
        /// <returns>An array of bytes containing the Ed25519 signature.</returns>
        public static byte[] Sign(byte[] data, NSec.Cryptography.Key privateKey)
        {
            ArgumentNullException.ThrowIfNull(privateKey, nameof(privateKey));

            try
            {
                return NSec.Cryptography.SignatureAlgorithm.Ed25519.Sign(privateKey, data);
            }
            catch (Exception ex)
            {
                Diagnostics.Log("Ed25519SignError", new { Message = "Error during signing", Exception = ex.Message });
                throw;
            }
        }

        /// <summary>
        /// Verifies the Ed25519 signature for the given data using the provided public key.
        /// </summary>
        /// <param name="data">The data that was signed.</param>
        /// <param name="signature">The signature to verify.</param>
        /// <param name="publicKey">The Ed25519 public key used for verification.</param>
        /// <returns>True if the signature is valid; otherwise, false.</returns>
        public static bool Verify(byte[] data, byte[] signature, NSec.Cryptography.PublicKey publicKey)
        {
            ArgumentNullException.ThrowIfNull(publicKey, nameof(publicKey));

            try
            {
                return NSec.Cryptography.SignatureAlgorithm.Ed25519.Verify(publicKey, data, signature);
            }
            catch (Exception ex)
            {
                Diagnostics.Log("Ed25519VerifyError", new { Message = "Error during verification", Exception = ex.Message });
                return false; // Return false for verification failure
            }
        }

        /// <summary>
        /// Creates a new Ed25519 private key for digital signatures.
        /// </summary>
        /// <returns>A new <see cref="NSec.Cryptography.Key"/> containing an Ed25519 private key.</returns>
        public static NSec.Cryptography.Key CreatePrivateKey()
        {
            return new NSec.Cryptography.Key(NSec.Cryptography.SignatureAlgorithm.Ed25519);
        }

        /// <summary>
        /// Gets the public key associated with the given Ed25519 private key.
        /// </summary>
        /// <param name="privateKey">The Ed25519 private key from which to derive the public key.</param>
        /// <returns>A <see cref="NSec.Cryptography.PublicKey"/> derived from the provided private key, which can be used for signature verification.</returns>
        public static NSec.Cryptography.PublicKey GetPublicKey(NSec.Cryptography.Key privateKey) => privateKey.PublicKey;
    }
}