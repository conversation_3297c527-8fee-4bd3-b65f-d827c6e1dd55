using System;
using System.Collections.Generic;
using System.Net;
using System.Threading;
using System.Threading.Tasks;
using System.Diagnostics;
using static USDP2.Diagnostics; // Use static import for Diagnostics methods

namespace USDP2
{
    /// <summary>
    /// Simple mDNS/DNS-SD proxy for bridging local directory advertisements with mDNS.
    ///
    /// This proxy now includes circuit breaker protection for improved resilience during
    /// network instability, preventing cascading failures and providing graceful degradation.
    /// </summary>
    public class MdnsProxy
    {
        /// <summary>
        /// The cache.
        /// </summary>
        private readonly ServiceAdvertisementCache _cache;
        /// <summary>
        /// The interface name.
        /// </summary>
        private readonly string _interfaceName;
        /// <summary>
        /// Circuit breaker for mDNS publish operations.
        /// </summary>
        private readonly CircuitBreaker _publishCircuitBreaker;
        /// <summary>
        /// Circuit breaker for mDNS import operations.
        /// </summary>
        private readonly CircuitBreaker _importCircuitBreaker;

        /// <summary>
        /// Initializes a new instance of the <see cref="MdnsProxy"/> class.
        /// </summary>
        /// <param name="interfaceName">The interface name.</param>
        public MdnsProxy(string interfaceName)
        {
            _interfaceName = interfaceName;
            _cache = new ServiceAdvertisementCache();

            // Initialize circuit breakers with mDNS-specific configuration
            var mdnsOptions = new CircuitBreakerOptions
            {
                FailureThreshold = 7,  // mDNS is local network, be more lenient
                OpenTimeout = TimeSpan.FromSeconds(45),
                OperationTimeout = TimeSpan.FromSeconds(10),
                SuccessThreshold = 3,
                ShouldHandleException = ex => ex switch
                {
                    TaskCanceledException when ex.InnerException is TimeoutException => true, // Count timeout cancellations as failures
                    OperationCanceledException => false, // Don't count regular cancellations as failures
                    TimeoutException => true,            // Count timeouts as failures
                    _ => true // Count other exceptions as failures
                }
            };

            _publishCircuitBreaker = CircuitBreakerManager.Instance.GetOrCreateCircuitBreaker("MdnsProxy.Publish", mdnsOptions);
            _importCircuitBreaker = CircuitBreakerManager.Instance.GetOrCreateCircuitBreaker("MdnsProxy.Import", mdnsOptions);
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="MdnsProxy"/> class.
        /// </summary>
        /// <param name="cache">The cache.</param>
        public MdnsProxy(ServiceAdvertisementCache cache) : this("")
        {
            _cache = cache;
        }

        // Simulate publishing USDP advertisements as mDNS records
        /// <summary>
        /// Publish converts to mdns asynchronously with circuit breaker protection.
        ///
        /// This method now includes circuit breaker protection to handle network failures
        /// gracefully and prevent cascading failures during mDNS publishing operations.
        /// </summary>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>A <see cref="Task"/></returns>
        /// <exception cref="CircuitBreakerOpenException">Thrown when the circuit breaker is open due to repeated failures.</exception>
        public async Task PublishToMdnsAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                await _publishCircuitBreaker.ExecuteAsync(async ct =>
                {
                    var start = DateTimeOffset.UtcNow;
                    int published = 0;

                    Log("MdnsPublishStart", new
                    {
                        CircuitState = _publishCircuitBreaker.State,
                        AvailableAdvertisements = _cache.GetActiveAdvertisements().Count()
                    });

                    foreach (var adv in _cache.GetActiveAdvertisements())
                    {
                        // Check for cancellation between each advertisement
                        ct.ThrowIfCancellationRequested();

                        Log("MdnsPublish", new
                        {
                            ServiceId = adv.ServiceId.ToString(),
                            Endpoint = adv.Endpoint.ToString(),
                            Metadata = adv.Metadata
                        });
                        published++;

                        // Simulate async work with potential for timeout/failure
                        await Task.Delay(UsdpConfiguration.Instance.MdnsOperationDelay, ct);
                    }

                    SetServiceCount(published);
                    var duration = DateTimeOffset.UtcNow - start;

                    Log("MdnsPublishSummary", new
                    {
                        Count = published,
                        DurationMs = duration.TotalMilliseconds,
                        CircuitState = _publishCircuitBreaker.State,
                        Success = true
                    });

                }, cancellationToken);
            }
            catch (CircuitBreakerOpenException ex)
            {
                Log("MdnsPublishBlocked", new
                {
                    Reason = "Circuit breaker is open",
                    State = ex.State,
                    TimeUntilRetry = ex.TimeUntilRetry.TotalSeconds,
                    Statistics = _publishCircuitBreaker.GetStatistics()
                });
                throw;
            }
            catch (Exception ex)
            {
                Log("MdnsPublishError", new
                {
                    Error = ex.Message,
                    ExceptionType = ex.GetType().Name,
                    CircuitState = _publishCircuitBreaker.State
                });
                throw;
            }
        }

        // Simulate discovering mDNS services and adding them to the USDP cache
        /// <summary>
        /// Imports from mdns asynchronously with circuit breaker protection.
        ///
        /// This method now includes circuit breaker protection to handle network failures
        /// gracefully and prevent cascading failures during mDNS import operations.
        /// </summary>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>A <see cref="Task"/></returns>
        /// <exception cref="CircuitBreakerOpenException">Thrown when the circuit breaker is open due to repeated failures.</exception>
        public async Task ImportFromMdnsAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                await _importCircuitBreaker.ExecuteAsync(async ct =>
                {
                    var start = Stopwatch.GetTimestamp();

                    Log("MdnsImportStart", new
                    {
                        CircuitState = _importCircuitBreaker.State
                    });

                    // Simulate a query with potential for timeout/failure
                    await Task.Delay(UsdpConfiguration.Instance.MdnsOperationDelay, ct);

                    var latencyMs = (Stopwatch.GetTimestamp() - start) * 1000 / Stopwatch.Frequency;
                    RecordQueryLatency(latencyMs);

                    Log("MdnsImport", new
                    {
                        LatencyMs = latencyMs,
                        CircuitState = _importCircuitBreaker.State,
                        Success = true
                    });

                }, cancellationToken);
            }
            catch (CircuitBreakerOpenException ex)
            {
                Log("MdnsImportBlocked", new
                {
                    Reason = "Circuit breaker is open",
                    State = ex.State,
                    TimeUntilRetry = ex.TimeUntilRetry.TotalSeconds,
                    Statistics = _importCircuitBreaker.GetStatistics()
                });
                throw;
            }
            catch (Exception ex)
            {
                Log("MdnsImportError", new
                {
                    Error = ex.Message,
                    ExceptionType = ex.GetType().Name,
                    CircuitState = _importCircuitBreaker.State
                });
                throw;
            }
        }

        // Mark as static if not using instance data
        /// <summary>
        /// Somes utility method.
        /// </summary>
        public static void SomeUtilityMethod()
        {
            // ...existing code...
        }
    }
}