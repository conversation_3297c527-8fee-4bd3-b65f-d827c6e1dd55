# USDP2 Serialization Guide

## Overview

This document describes the standardized serialization approach used in the USDP2 project. All serialization and deserialization operations should use the `USDPSerializer` facade to ensure consistency across the codebase.

## Serialization Architecture

The serialization system consists of:

1. **IUSDPSerializer Interface**: Defines the contract for serialization implementations
2. **USDPSerializer Static Facade**: Provides a single entry point for all serialization operations
3. **DefaultUSDPSerializer**: The default implementation using System.Text.Json and PeterO.Cbor

## How to Use

### For Model Classes

All model classes should follow this pattern for serialization:

```csharp
// JSON serialization
public string ToJson() => USDPSerializer.ToJson(this);
public static MyClass? FromJson(string json) => USDPSerializer.FromJson<MyClass>(json);

// CBOR serialization
public byte[] ToCbor() => USDPSerializer.ToCbor(this);
public static MyClass? FromCbor(byte[] cbor) => USDPSerializer.FromCbor<MyClass>(cbor);
```

### For Application Code

When serializing or deserializing objects:

```csharp
// Serialize
string json = myObject.ToJson();
byte[] cbor = myObject.ToCbor();

// Deserialize
var fromJson = MyClass.FromJson(jsonString);
var fromCbor = MyClass.FromCbor(cborBytes);
```

## Custom Serialization

To implement a custom serializer:

1. Create a class that implements `IUSDPSerializer`
2. Register it at application startup:

```csharp
USDPSerializer.SetSerializer(new MyCustomSerializer());
```

## Error Handling

The serialization system includes comprehensive error handling:

- All serialization methods catch exceptions and log errors
- Failed serialization returns empty string or empty byte array
- Failed deserialization returns null
- Input validation prevents null reference exceptions

## Testing

The `USDP2.Tests` project includes serialization tests that verify:

- JSON serialization round-trip
- CBOR serialization round-trip
- Handling of null values
- Handling of invalid input
- Signing and verification

## Best Practices

1. Always use the `USDPSerializer` facade instead of direct serialization calls
2. Include proper error handling when processing serialized data
3. Validate deserialized objects before using them
4. Use the test suite to verify serialization behavior for new models
5. Consider performance implications for large objects or high-frequency operations