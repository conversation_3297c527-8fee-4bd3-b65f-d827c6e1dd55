using System.Net.Sockets;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace USDP2
{
    /// <summary>
    /// The tcp network sender.
    /// </summary>
    public class TcpNetworkSender : INetworkSender
    {
        /// <summary>
        /// Sends data asynchronously via TCP to the specified address and port.
        /// </summary>
        /// <param name="data">The data to send.</param>
        /// <param name="address">The target IP address or hostname.</param>
        /// <param name="port">The target port number.</param>
        /// <param name="cancellationToken">The cancellation token to cancel the operation.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous send operation.</returns>
        public async Task SendAsync(byte[] data, string address, int port, CancellationToken cancellationToken = default)
        {
            using var client = new TcpClient();
            await client.ConnectAsync(address, port, cancellationToken);
            using var stream = client.GetStream();
            await stream.WriteAsync(data, cancellationToken);
        }
    }
}