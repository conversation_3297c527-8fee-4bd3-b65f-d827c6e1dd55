using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;

namespace USDP2
{
    /// <summary>
    /// Sends data over HTTP/HTTPS with advanced TLS configuration, fallback mechanisms, and circuit breaker protection.
    ///
    /// This class provides secure HTTP communication with support for:
    /// - OS-managed TLS configuration (default)
    /// - Graceful TLS fallback for compatibility
    /// - Manual TLS override capabilities (when enabled)
    /// - Circuit breaker protection against failing endpoints
    /// - Per-endpoint circuit breaker isolation
    /// - Comprehensive security logging and monitoring
    /// </summary>
    public class HttpNetworkSender : INetworkSender, IDisposable
    {
        private readonly TlsConfigurationManager _tlsManager;
        private readonly TlsOverrideProvider? _tlsOverrideProvider;
        private readonly UsdpConfiguration _config;
        private readonly ConcurrentDictionary<string, CircuitBreaker> _circuitBreakers = new();
        private HttpClient? _httpClient;
        private bool _disposed;

        /// <summary>
        /// Initializes a new instance of the HttpNetworkSender class.
        /// </summary>
        /// <param name="config">The USDP configuration. If null, uses the global instance.</param>
        /// <param name="tlsOverrideConfig">Optional manual TLS override configuration.</param>
        public HttpNetworkSender(UsdpConfiguration? config = null, TlsOverrideConfiguration? tlsOverrideConfig = null)
        {
            _config = config ?? UsdpConfiguration.Instance;
            _tlsManager = new TlsConfigurationManager(_config);

            // Initialize TLS override provider only if manual override is enabled
            if (_config.EnableManualTlsOverride && tlsOverrideConfig != null)
            {
                try
                {
                    _tlsOverrideProvider = new TlsOverrideProvider(tlsOverrideConfig);

                    Diagnostics.Log("HttpNetworkSender", new
                    {
                        Message = "Initialized with manual TLS override configuration",
                        ManualOverrideEnabled = true
                    });
                }
                catch (Exception ex)
                {
                    // Gracefully handle case where TlsOverrideProvider is not available
                    // (e.g., if the class was removed for security/simplicity)
                    Diagnostics.Log("HttpNetworkSender", new
                    {
                        Message = "Manual TLS override requested but TlsOverrideProvider not available, falling back to OS-managed TLS",
                        ManualOverrideEnabled = false,
                        Error = ex.Message,
                        FallbackToOSManaged = true
                    });

                    // _tlsOverrideProvider remains null, system will use OS-managed TLS
                }
            }
            else
            {
                Diagnostics.Log("HttpNetworkSender", new
                {
                    Message = "Initialized with OS-managed TLS configuration",
                    UseOSManagedTls = _config.UseOSManagedTls,
                    EnableTlsFallback = _config.EnableTlsFallback
                });
            }
        }

        /// <summary>
        /// Sends data asynchronously to the specified address and port using HTTP/HTTPS with circuit breaker protection.
        ///
        /// This method automatically selects the appropriate TLS configuration based on
        /// the current settings and implements fallback mechanisms for enhanced compatibility.
        /// Circuit breaker protection prevents cascading failures by failing fast when endpoints are down.
        /// </summary>
        /// <param name="data">The data to send.</param>
        /// <param name="address">The target address.</param>
        /// <param name="port">The target port.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        /// <exception cref="CircuitBreakerOpenException">Thrown when the circuit breaker is open for the target endpoint.</exception>
        /// <exception cref="HttpRequestException">Thrown when the HTTP request fails.</exception>
        public async Task SendAsync(byte[] data, string address, int port, CancellationToken cancellationToken = default)
        {
            ObjectDisposedException.ThrowIf(_disposed, this);

            var protocol = _config.UseHttps ? "https" : "http";
            var path = _config.UseHttps ? _config.HttpsEndpointPath : _config.HttpEndpointPath;
            var url = $"{protocol}://{address}:{port}{path}";

            // Get or create circuit breaker for this endpoint
            var endpointKey = $"{address}:{port}";
            var circuitBreaker = GetOrCreateCircuitBreaker(endpointKey);

            // Execute the HTTP request through the circuit breaker
            await circuitBreaker.ExecuteAsync(async ct =>
            {
                using var content = new ByteArrayContent(data);
                var request = new HttpRequestMessage(HttpMethod.Post, url)
                {
                    Content = content
                };

                UsdpLogger.Log("HttpNetworkSender.RequestStarted", new
                {
                    Url = url,
                    DataSize = data.Length,
                    Protocol = protocol.ToUpperInvariant(),
                    CircuitBreakerState = circuitBreaker.State
                });

                HttpClient httpClient = GetConfiguredHttpClient();

                HttpResponseMessage response;

                // Use fallback mechanism if enabled and using TlsConfigurationManager
                if (_config.EnableTlsFallback && _tlsOverrideProvider == null)
                {
                    response = await _tlsManager.SendWithFallbackAsync(request, ct).ConfigureAwait(false);
                }
                else
                {
                    // Direct send without fallback (either fallback disabled or using override)
                    response = await httpClient.SendAsync(request, ct).ConfigureAwait(false);
                }

                response.EnsureSuccessStatusCode();

                UsdpLogger.Log("HttpNetworkSender.Success", new
                {
                    Url = url,
                    StatusCode = (int)response.StatusCode,
                    DataSize = data.Length,
                    Protocol = protocol.ToUpperInvariant()
                });

            }, cancellationToken).ConfigureAwait(false);
        }

        /// <summary>
        /// Gets the appropriately configured HttpClient based on current settings.
        /// </summary>
        /// <returns>A configured HttpClient instance.</returns>
        private HttpClient GetConfiguredHttpClient()
        {
            if (_httpClient != null)
            {
                return _httpClient;
            }

            // Use manual override if enabled and available
            if (_config.EnableManualTlsOverride && _tlsOverrideProvider != null)
            {
                _httpClient = _tlsOverrideProvider.CreateOverrideHttpClient();

                Diagnostics.Log("HttpNetworkSender", new
                {
                    Message = "Using manual TLS override configuration",
                    OverrideActive = true
                });
            }
            else
            {
                // Use OS-managed TLS configuration
                _httpClient = _tlsManager.CreateHttpClient();

                Diagnostics.Log("HttpNetworkSender", new
                {
                    Message = "Using OS-managed TLS configuration",
                    OSManaged = true
                });
            }

            return _httpClient;
        }

        /// <summary>
        /// Gets or creates a circuit breaker for the specified endpoint.
        /// </summary>
        /// <param name="endpointKey">The endpoint key (address:port).</param>
        /// <returns>A circuit breaker instance for the endpoint.</returns>
        private CircuitBreaker GetOrCreateCircuitBreaker(string endpointKey)
        {
            return _circuitBreakers.GetOrAdd(endpointKey, key =>
            {
                var options = new CircuitBreakerOptions
                {
                    FailureThreshold = _config.CircuitBreakerFailureThreshold,
                    OpenTimeout = _config.CircuitBreakerOpenTimeout,
                    OperationTimeout = _config.NetworkTimeout,
                    SuccessThreshold = _config.CircuitBreakerSuccessThreshold,
                    ShouldHandleException = ex => ex switch
                    {
                        TaskCanceledException when ex.InnerException is TimeoutException => true, // Count timeout cancellations as failures
                        OperationCanceledException => false, // Don't count cancellations as failures
                        TimeoutException => true,            // Count timeouts as failures
                        HttpRequestException => true,        // Count HTTP errors as failures
                        _ => true // Count other exceptions as failures by default
                    }
                };

                var circuitBreaker = new CircuitBreaker($"HttpNetworkSender.{key}", options);

                UsdpLogger.Log("HttpNetworkSender.CircuitBreakerCreated", new
                {
                    EndpointKey = key,
                    FailureThreshold = options.FailureThreshold,
                    OpenTimeout = options.OpenTimeout.TotalSeconds,
                    OperationTimeout = options.OperationTimeout.TotalSeconds
                });

                return circuitBreaker;
            });
        }

        /// <summary>
        /// Gets statistics for all circuit breakers managed by this sender.
        /// </summary>
        /// <returns>A dictionary of endpoint keys and their circuit breaker statistics.</returns>
        public Dictionary<string, CircuitBreakerStatistics> GetCircuitBreakerStatistics()
        {
            var statistics = new Dictionary<string, CircuitBreakerStatistics>();

            foreach (var kvp in _circuitBreakers)
            {
                try
                {
                    statistics[kvp.Key] = kvp.Value.GetStatistics();
                }
                catch (Exception ex)
                {
                    UsdpLogger.Log("HttpNetworkSender.StatisticsError", new
                    {
                        EndpointKey = kvp.Key,
                        Error = ex.Message
                    });
                }
            }

            return statistics;
        }

        /// <summary>
        /// Gets the circuit breaker for a specific endpoint, if it exists.
        /// </summary>
        /// <param name="address">The target address.</param>
        /// <param name="port">The target port.</param>
        /// <returns>The circuit breaker for the endpoint, or null if it doesn't exist.</returns>
        public CircuitBreaker? GetCircuitBreaker(string address, int port)
        {
            var endpointKey = $"{address}:{port}";
            return _circuitBreakers.TryGetValue(endpointKey, out var circuitBreaker) ? circuitBreaker : null;
        }

        /// <summary>
        /// Disposes of managed resources.
        /// </summary>
        public void Dispose()
        {
            if (!_disposed)
            {
                _httpClient?.Dispose();
                _tlsManager?.Dispose();

                UsdpLogger.Log("HttpNetworkSender.Disposed", new
                {
                    CircuitBreakersCount = _circuitBreakers.Count
                });

                _disposed = true;
                GC.SuppressFinalize(this);
            }
        }
    }
}