using System;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;

namespace USDP2
{
    /// <summary>
    /// Sends data over HTTP/HTTPS with advanced TLS configuration and fallback mechanisms.
    ///
    /// This class provides secure HTTP communication with support for:
    /// - OS-managed TLS configuration (default)
    /// - Graceful TLS fallback for compatibility
    /// - Manual TLS override capabilities (when enabled)
    /// - Comprehensive security logging and monitoring
    /// </summary>
    public class HttpNetworkSender : INetworkSender, IDisposable
    {
        private readonly TlsConfigurationManager _tlsManager;
        private readonly TlsOverrideProvider? _tlsOverrideProvider;
        private readonly UsdpConfiguration _config;
        private HttpClient? _httpClient;
        private bool _disposed;

        /// <summary>
        /// Initializes a new instance of the HttpNetworkSender class.
        /// </summary>
        /// <param name="config">The USDP configuration. If null, uses the global instance.</param>
        /// <param name="tlsOverrideConfig">Optional manual TLS override configuration.</param>
        public HttpNetworkSender(UsdpConfiguration? config = null, TlsOverrideConfiguration? tlsOverrideConfig = null)
        {
            _config = config ?? UsdpConfiguration.Instance;
            _tlsManager = new TlsConfigurationManager(_config);

            // Initialize TLS override provider only if manual override is enabled
            if (_config.EnableManualTlsOverride && tlsOverrideConfig != null)
            {
                try
                {
                    _tlsOverrideProvider = new TlsOverrideProvider(tlsOverrideConfig);

                    Diagnostics.Log("HttpNetworkSender", new
                    {
                        Message = "Initialized with manual TLS override configuration",
                        ManualOverrideEnabled = true
                    });
                }
                catch (Exception ex)
                {
                    // Gracefully handle case where TlsOverrideProvider is not available
                    // (e.g., if the class was removed for security/simplicity)
                    Diagnostics.Log("HttpNetworkSender", new
                    {
                        Message = "Manual TLS override requested but TlsOverrideProvider not available, falling back to OS-managed TLS",
                        ManualOverrideEnabled = false,
                        Error = ex.Message,
                        FallbackToOSManaged = true
                    });

                    // _tlsOverrideProvider remains null, system will use OS-managed TLS
                }
            }
            else
            {
                Diagnostics.Log("HttpNetworkSender", new
                {
                    Message = "Initialized with OS-managed TLS configuration",
                    UseOSManagedTls = _config.UseOSManagedTls,
                    EnableTlsFallback = _config.EnableTlsFallback
                });
            }
        }

        /// <summary>
        /// Sends data asynchronously to the specified address and port using HTTP/HTTPS.
        ///
        /// This method automatically selects the appropriate TLS configuration based on
        /// the current settings and implements fallback mechanisms for enhanced compatibility.
        /// </summary>
        /// <param name="data">The data to send.</param>
        /// <param name="address">The target address.</param>
        /// <param name="port">The target port.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public async Task SendAsync(byte[] data, string address, int port, CancellationToken cancellationToken = default)
        {
            ObjectDisposedException.ThrowIf(_disposed, this);

            var protocol = _config.UseHttps ? "https" : "http";
            var path = _config.UseHttps ? _config.HttpsEndpointPath : _config.HttpEndpointPath;
            var url = $"{protocol}://{address}:{port}{path}";

            var content = new ByteArrayContent(data);
            var request = new HttpRequestMessage(HttpMethod.Post, url)
            {
                Content = content
            };

            try
            {
                HttpClient httpClient = GetConfiguredHttpClient();

                // Use fallback mechanism if enabled and using TlsConfigurationManager
                if (_config.EnableTlsFallback && _tlsOverrideProvider == null)
                {
                    var response = await _tlsManager.SendWithFallbackAsync(request, cancellationToken);
                    response.EnsureSuccessStatusCode();
                }
                else
                {
                    // Direct send without fallback (either fallback disabled or using override)
                    var response = await httpClient.SendAsync(request, cancellationToken);
                    response.EnsureSuccessStatusCode();
                }

                Diagnostics.Log("HttpNetworkSender", new
                {
                    Message = "Successfully sent data",
                    Url = url,
                    DataSize = data.Length,
                    Protocol = protocol.ToUpperInvariant()
                });
            }
            catch (Exception ex)
            {
                Diagnostics.Log("HttpNetworkSenderError", new
                {
                    Message = "Failed to send data",
                    Url = url,
                    Error = ex.Message,
                    ExceptionType = ex.GetType().Name
                });
                throw;
            }
        }

        /// <summary>
        /// Gets the appropriately configured HttpClient based on current settings.
        /// </summary>
        /// <returns>A configured HttpClient instance.</returns>
        private HttpClient GetConfiguredHttpClient()
        {
            if (_httpClient != null)
            {
                return _httpClient;
            }

            // Use manual override if enabled and available
            if (_config.EnableManualTlsOverride && _tlsOverrideProvider != null)
            {
                _httpClient = _tlsOverrideProvider.CreateOverrideHttpClient();

                Diagnostics.Log("HttpNetworkSender", new
                {
                    Message = "Using manual TLS override configuration",
                    OverrideActive = true
                });
            }
            else
            {
                // Use OS-managed TLS configuration
                _httpClient = _tlsManager.CreateHttpClient();

                Diagnostics.Log("HttpNetworkSender", new
                {
                    Message = "Using OS-managed TLS configuration",
                    OSManaged = true
                });
            }

            return _httpClient;
        }

        /// <summary>
        /// Disposes of managed resources.
        /// </summary>
        public void Dispose()
        {
            if (!_disposed)
            {
                _httpClient?.Dispose();
                _tlsManager?.Dispose();
                _disposed = true;
                GC.SuppressFinalize(this);
            }
        }
    }
}