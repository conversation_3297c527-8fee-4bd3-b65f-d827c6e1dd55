using System.Security.Cryptography;
using System.Threading.Tasks;

namespace USDP2
{
    /// <summary>
    /// Simple PSK (Pre-Shared Key) authentication provider.
    /// </summary>
    public class PskAuthenticationProvider : IAuthenticationProvider
    {
        /// <summary>
        /// The psk.
        /// </summary>
        private readonly string _psk;

        /// <summary>
        /// Initializes a new instance of the <see cref="PskAuthenticationProvider"/> class.
        /// </summary>
        /// <param name="psk">The psk.</param>
        /// <exception cref="ArgumentException">Thrown when PSK is null, empty, or too short.</exception>
        public PskAuthenticationProvider(string psk)
        {
            ArgumentException.ThrowIfNullOrEmpty(psk);

            // Validate PSK length for security (minimum 8 characters, recommended 32+)
            if (psk.Length < 8)
            {
                throw new ArgumentException("PSK must be at least 8 characters long for security", nameof(psk));
            }

            _psk = psk;
        }

        /// <summary>
        /// Authenticates a token or key against the stored PSK.
        /// </summary>
        /// <param name="tokenOrKey">The token or key to authenticate.</param>
        /// <returns>True if authentication succeeds, false otherwise.</returns>
        public bool Authenticate(string tokenOrKey)
        {
            // Validate input length to prevent timing attacks and ensure minimum security
            if (string.IsNullOrEmpty(tokenOrKey) || tokenOrKey.Length < 8)
            {
                return false;
            }

            // Use constant-time comparison to prevent timing attacks
            return CryptographicOperations.FixedTimeEquals(
                System.Text.Encoding.UTF8.GetBytes(tokenOrKey),
                System.Text.Encoding.UTF8.GetBytes(_psk));
        }

        // Add this method to implement the interface
        /// <summary>
        /// Authenticates and return a <see cref="Task"/> of type <see cref="bool"/> asynchronously.
        /// </summary>
        /// <param name="tokenOrKey">The token or key to authenticate.</param>
        /// <returns>A <see cref="Task"/> of type <see cref="bool"/></returns>
        public Task<bool> AuthenticateAsync(string tokenOrKey)
        {
            // If you have synchronous logic, wrap it in Task.FromResult
            return Task.FromResult(Authenticate(tokenOrKey));
        }
    }
}