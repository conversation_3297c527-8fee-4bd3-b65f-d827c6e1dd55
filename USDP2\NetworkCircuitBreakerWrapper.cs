using System;
using System.Threading;
using System.Threading.Tasks;

namespace USDP2
{
    /// <summary>
    /// Wraps network senders with circuit breaker functionality for improved resilience.
    /// 
    /// This wrapper provides automatic circuit breaker protection for any INetworkSender
    /// implementation, adding failure detection, fast-fail behavior, and automatic recovery
    /// testing without requiring changes to existing network sender implementations.
    /// </summary>
    public class NetworkCircuitBreakerWrapper : INetworkSender, IDisposable
    {
        private readonly INetworkSender _innerSender;
        private readonly CircuitBreaker _circuitBreaker;
        private readonly string _senderType;
        private bool _disposed;

        /// <summary>
        /// Initializes a new instance of the NetworkCircuitBreakerWrapper class.
        /// </summary>
        /// <param name="innerSender">The network sender to wrap with circuit breaker functionality.</param>
        /// <param name="circuitBreakerName">Optional custom name for the circuit breaker. If null, uses sender type.</param>
        /// <param name="options">Optional circuit breaker configuration. If null, uses defaults based on sender type.</param>
        public NetworkCircuitBreakerWrapper(INetworkSender innerSender, string? circuitBreakerName = null, CircuitBreakerOptions? options = null)
        {
            _innerSender = innerSender ?? throw new ArgumentNullException(nameof(innerSender));
            _senderType = innerSender.GetType().Name;

            var breakerName = circuitBreakerName ?? $"NetworkSender.{_senderType}";
            _circuitBreaker = CircuitBreakerManager.Instance.GetOrCreateCircuitBreaker(breakerName, options);

            UsdpLogger.Log("NetworkCircuitBreaker.Created", new
            {
                SenderType = _senderType,
                CircuitBreakerName = breakerName,
                HasCustomOptions = options != null
            });
        }

        /// <summary>
        /// Sends data asynchronously through the circuit breaker.
        /// </summary>
        /// <param name="data">The data to send.</param>
        /// <param name="address">The target address.</param>
        /// <param name="port">The target port.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>A task representing the asynchronous send operation.</returns>
        /// <exception cref="CircuitBreakerOpenException">Thrown when the circuit breaker is open.</exception>
        public async Task SendAsync(byte[] data, string address, int port, CancellationToken cancellationToken = default)
        {
            ObjectDisposedException.ThrowIf(_disposed, this);

            try
            {
                await _circuitBreaker.ExecuteAsync(async ct =>
                {
                    UsdpLogger.Log($"NetworkCircuitBreaker.{_senderType}.Sending", new
                    {
                        Address = address,
                        Port = port,
                        DataSize = data.Length,
                        CircuitState = _circuitBreaker.State
                    });

                    await _innerSender.SendAsync(data, address, port, ct);

                    UsdpLogger.Log($"NetworkCircuitBreaker.{_senderType}.Sent", new
                    {
                        Address = address,
                        Port = port,
                        DataSize = data.Length,
                        Success = true
                    });

                }, cancellationToken);
            }
            catch (CircuitBreakerOpenException)
            {
                // Log circuit breaker block and re-throw
                UsdpLogger.Log($"NetworkCircuitBreaker.{_senderType}.Blocked", new
                {
                    Address = address,
                    Port = port,
                    CircuitState = _circuitBreaker.State,
                    Statistics = _circuitBreaker.GetStatistics()
                });
                throw;
            }
            catch (Exception ex)
            {
                // Log failure and re-throw
                UsdpLogger.Log($"NetworkCircuitBreaker.{_senderType}.Failed", new
                {
                    Address = address,
                    Port = port,
                    Error = ex.Message,
                    ExceptionType = ex.GetType().Name,
                    CircuitState = _circuitBreaker.State
                });
                throw;
            }
        }

        /// <summary>
        /// Gets the current circuit breaker statistics.
        /// </summary>
        /// <returns>Circuit breaker statistics.</returns>
        public CircuitBreakerStatistics GetCircuitBreakerStatistics()
        {
            return _circuitBreaker.GetStatistics();
        }

        /// <summary>
        /// Gets the underlying network sender.
        /// </summary>
        /// <returns>The wrapped network sender.</returns>
        public INetworkSender GetInnerSender()
        {
            return _innerSender;
        }

        /// <summary>
        /// Disposes the wrapper and the underlying sender if it implements IDisposable.
        /// </summary>
        public void Dispose()
        {
            if (!_disposed)
            {
                UsdpLogger.Log($"NetworkCircuitBreaker.{_senderType}.Disposing", new
                {
                    Statistics = _circuitBreaker.GetStatistics()
                });

                if (_innerSender is IDisposable disposableSender)
                {
                    disposableSender.Dispose();
                }

                _disposed = true;
            }
            GC.SuppressFinalize(this);
        }
    }

    /// <summary>
    /// Factory for creating circuit breaker-wrapped network senders.
    /// </summary>
    public static class NetworkCircuitBreakerFactory
    {
        /// <summary>
        /// Creates a circuit breaker-wrapped network sender based on the specified scope.
        /// </summary>
        /// <param name="scope">The network scope (Local or Global).</param>
        /// <param name="customOptions">Optional custom circuit breaker options.</param>
        /// <returns>A circuit breaker-wrapped network sender.</returns>
        public static INetworkSender CreateSender(NetworkScope scope, CircuitBreakerOptions? customOptions = null)
        {
            var baseSender = NetworkSenderFactory.CreateSender(scope);
            return new NetworkCircuitBreakerWrapper(baseSender, $"NetworkSender.{scope}", customOptions);
        }

        /// <summary>
        /// Creates a circuit breaker-wrapped HTTP network sender with HTTP-specific configuration.
        /// </summary>
        /// <param name="customOptions">Optional custom circuit breaker options.</param>
        /// <returns>A circuit breaker-wrapped HTTP network sender.</returns>
        public static INetworkSender CreateHttpSender(CircuitBreakerOptions? customOptions = null)
        {
            var httpSender = new HttpNetworkSender();

            // Use HTTP-specific defaults if no custom options provided
            var options = customOptions ?? new CircuitBreakerOptions
            {
                FailureThreshold = 5,
                OpenTimeout = TimeSpan.FromSeconds(30),
                OperationTimeout = UsdpConfiguration.Instance.NetworkTimeout,
                SuccessThreshold = 3,
                ShouldHandleException = ex => ex switch
                {
                    OperationCanceledException => false,
                    System.Net.Http.HttpRequestException => true,
                    TimeoutException => true,
                    _ => true
                }
            };

            return new NetworkCircuitBreakerWrapper(httpSender, "NetworkSender.Http", options);
        }

        /// <summary>
        /// Creates a circuit breaker-wrapped UDP network sender with UDP-specific configuration.
        /// </summary>
        /// <param name="customOptions">Optional custom circuit breaker options.</param>
        /// <returns>A circuit breaker-wrapped UDP network sender.</returns>
        public static INetworkSender CreateUdpSender(CircuitBreakerOptions? customOptions = null)
        {
            var udpSender = new UdpNetworkSender();

            // Use UDP-specific defaults if no custom options provided
            var options = customOptions ?? new CircuitBreakerOptions
            {
                FailureThreshold = 3,
                OpenTimeout = TimeSpan.FromSeconds(15),
                OperationTimeout = TimeSpan.FromSeconds(5),
                SuccessThreshold = 2,
                ShouldHandleException = ex => ex switch
                {
                    OperationCanceledException => false,
                    System.Net.Sockets.SocketException => true,
                    TimeoutException => true,
                    _ => true
                }
            };

            return new NetworkCircuitBreakerWrapper(udpSender, "NetworkSender.Udp", options);
        }

        /// <summary>
        /// Creates a circuit breaker-wrapped TCP network sender with TCP-specific configuration.
        /// </summary>
        /// <param name="customOptions">Optional custom circuit breaker options.</param>
        /// <returns>A circuit breaker-wrapped TCP network sender.</returns>
        public static INetworkSender CreateTcpSender(CircuitBreakerOptions? customOptions = null)
        {
            var tcpSender = new TcpNetworkSender();

            // Use TCP-specific defaults if no custom options provided
            var options = customOptions ?? new CircuitBreakerOptions
            {
                FailureThreshold = 4,
                OpenTimeout = TimeSpan.FromSeconds(20),
                OperationTimeout = UsdpConfiguration.Instance.NetworkTimeout,
                SuccessThreshold = 2,
                ShouldHandleException = ex => ex switch
                {
                    OperationCanceledException => false,
                    System.Net.Sockets.SocketException => true,
                    TimeoutException => true,
                    _ => true
                }
            };

            return new NetworkCircuitBreakerWrapper(tcpSender, "NetworkSender.Tcp", options);
        }

        /// <summary>
        /// Wraps an existing network sender with circuit breaker functionality.
        /// </summary>
        /// <param name="sender">The network sender to wrap.</param>
        /// <param name="circuitBreakerName">Optional custom name for the circuit breaker.</param>
        /// <param name="options">Optional circuit breaker configuration.</param>
        /// <returns>A circuit breaker-wrapped network sender.</returns>
        public static INetworkSender WrapSender(INetworkSender sender, string? circuitBreakerName = null, CircuitBreakerOptions? options = null)
        {
            return new NetworkCircuitBreakerWrapper(sender, circuitBreakerName, options);
        }
    }
}
