using System;
using System.Collections.Generic;
using System.Text;
using System.Text.Json;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using NSec.Cryptography;

namespace USDP2.Tests
{
    [TestClass]
    public class SerializationTests
    {
        [TestMethod]
        public void ServiceAdvertisement_JsonSerialization_RoundTrip()
        {
            // Arrange - Use centralized configuration for test values
            var config = UsdpConfiguration.Instance;
            var serviceId = new ServiceIdentifier(config.DefaultServiceType, config.DefaultServiceInstance);
            var endpoint = new TransportEndpoint
            {
                Protocol = config.DefaultProtocol,
                Address = config.DefaultServiceAddress,
                Port = config.DefaultServicePort,
                Security = config.DefaultSecurity
            };

            var original = new ServiceAdvertisement(serviceId, endpoint)
            {
                Metadata = new Dictionary<string, object>
                {
                    { "type", config.DefaultMetadataType },
                    { "location", config.DefaultMetadataLocation }
                },
                Ttl = config.DefaultTtl
            };

            // Act
            var json = original.ToJson();
            var deserialized = ServiceAdvertisement.FromJson(json);

            // Assert
            Assert.IsNotNull(deserialized);
            Assert.AreEqual(original.ServiceId.FullName, deserialized.ServiceId.FullName);
            Assert.AreEqual(original.Endpoint.Protocol, deserialized.Endpoint.Protocol);
            Assert.AreEqual(original.Endpoint.Address, deserialized.Endpoint.Address);
            Assert.AreEqual(original.Endpoint.Port, deserialized.Endpoint.Port);
            Assert.AreEqual(original.Endpoint.Security, deserialized.Endpoint.Security);
            Assert.AreEqual(original.Ttl, deserialized.Ttl);
            Assert.IsTrue(deserialized.Metadata.ContainsKey("type"));
            Assert.AreEqual(config.DefaultMetadataType, deserialized.Metadata["type"]);
        }

        [TestMethod]
        public void ServiceAdvertisement_CborSerialization_RoundTrip()
        {
            // Arrange - Use centralized configuration for test values
            var config = UsdpConfiguration.Instance;
            var serviceId = new ServiceIdentifier(config.DefaultServiceType, config.DefaultServiceInstance);
            var endpoint = new TransportEndpoint
            {
                Protocol = config.DefaultProtocol,
                Address = config.DefaultServiceAddress,
                Port = config.DefaultServicePort,
                Security = config.DefaultSecurity
            };

            var original = new ServiceAdvertisement(serviceId, endpoint)
            {
                Metadata = new Dictionary<string, object>
                {
                    { "type", config.DefaultMetadataType },
                    { "location", config.DefaultMetadataLocation }
                },
                Ttl = config.DefaultTtl
            };

            // Act
            var cbor = original.ToCbor();
            var deserialized = ServiceAdvertisement.FromCbor(cbor);

            // Assert
            Assert.IsNotNull(deserialized);
            Assert.AreEqual(original.ServiceId.FullName, deserialized.ServiceId.FullName);
            Assert.AreEqual(original.Endpoint.Protocol, deserialized.Endpoint.Protocol);
            Assert.AreEqual(original.Endpoint.Address, deserialized.Endpoint.Address);
            Assert.AreEqual(original.Endpoint.Port, deserialized.Endpoint.Port);
            Assert.AreEqual(original.Endpoint.Security, deserialized.Endpoint.Security);

            // Debug output for TTL issue
            Console.WriteLine($"Original TTL: {original.Ttl}");
            Console.WriteLine($"Deserialized TTL: {deserialized.Ttl}");

            Assert.AreEqual(original.Ttl, deserialized.Ttl);
            Assert.IsTrue(deserialized.Metadata.ContainsKey("type"));
            Assert.AreEqual(config.DefaultMetadataType, deserialized.Metadata["type"]);
        }

        [TestMethod]
        public void ServiceQuery_JsonSerialization_RoundTrip()
        {
            // Arrange - Use centralized configuration for test values
            var config = UsdpConfiguration.Instance;
            var original = new ServiceQuery
            {
                SidFilter = config.DefaultServiceType,
                MetadataFilter = new Dictionary<string, string>
                {
                    { "type", config.DefaultMetadataType },
                    { "location", config.DefaultMetadataLocation }
                }
            };

            // Act
            var json = original.ToJson();
            var deserialized = ServiceQuery.FromJson(json);

            // Assert
            Assert.IsNotNull(deserialized);
            Assert.AreEqual(original.SidFilter, deserialized.SidFilter);
            Assert.IsNotNull(deserialized.MetadataFilter);
            Assert.AreEqual(2, deserialized.MetadataFilter.Count);
            Assert.AreEqual(config.DefaultMetadataType, deserialized.MetadataFilter["type"]);
            Assert.AreEqual(config.DefaultMetadataLocation, deserialized.MetadataFilter["location"]);
        }

        [TestMethod]
        public void ServiceQuery_CborSerialization_RoundTrip()
        {
            // Arrange - Use centralized configuration for test values
            var config = UsdpConfiguration.Instance;
            var original = new ServiceQuery
            {
                SidFilter = config.DefaultServiceType,
                MetadataFilter = new Dictionary<string, string>
                {
                    { "type", config.DefaultMetadataType },
                    { "location", config.DefaultMetadataLocation }
                }
            };

            // Act
            var cbor = original.ToCbor();
            var deserialized = ServiceQuery.FromCbor(cbor);

            // Assert
            Assert.IsNotNull(deserialized);
            Assert.AreEqual(original.SidFilter, deserialized.SidFilter);
            Assert.IsNotNull(deserialized.MetadataFilter);
            Assert.AreEqual(2, deserialized.MetadataFilter.Count);
            Assert.AreEqual(config.DefaultMetadataType, deserialized.MetadataFilter["type"]);
            Assert.AreEqual(config.DefaultMetadataLocation, deserialized.MetadataFilter["location"]);
        }

        [TestMethod]
        public void ServiceAdvertisement_SignAndVerify_Success()
        {
            // Arrange - Use centralized configuration for test values
            var config = UsdpConfiguration.Instance;
            var serviceId = new ServiceIdentifier(config.DefaultServiceType, config.DefaultServiceInstance);
            var endpoint = new TransportEndpoint
            {
                Protocol = config.DefaultProtocol,
                Address = config.DefaultServiceAddress,
                Port = config.DefaultServicePort,
                Security = config.DefaultSecurity
            };

            var advertisement = new ServiceAdvertisement(serviceId, endpoint)
            {
                Metadata = new Dictionary<string, object>
                {
                    { "type", config.DefaultMetadataType },
                    { "location", config.DefaultMetadataLocation }
                },
                Ttl = config.DefaultTtl
            };

            // Create a key pair for testing
            var privateKey = new Key(SignatureAlgorithm.Ed25519);
            var publicKey = privateKey.PublicKey;

            // Act
            advertisement.Sign(privateKey);
            bool isValid = advertisement.Verify(publicKey);

            // Assert
            Assert.IsNotNull(advertisement.Signature);
            Assert.IsTrue(isValid);
        }

        [TestMethod]
        public void ServiceAdvertisement_SignAndVerify_FailsWithTampering()
        {
            // Arrange - Use centralized configuration for test values
            var config = UsdpConfiguration.Instance;
            var serviceId = new ServiceIdentifier(config.DefaultServiceType, config.DefaultServiceInstance);
            var endpoint = new TransportEndpoint
            {
                Protocol = config.DefaultProtocol,
                Address = config.DefaultServiceAddress,
                Port = config.DefaultServicePort,
                Security = config.DefaultSecurity
            };

            var advertisement = new ServiceAdvertisement(serviceId, endpoint)
            {
                Metadata = new Dictionary<string, object>
                {
                    { "type", config.DefaultMetadataType },
                    { "location", config.DefaultMetadataLocation }
                },
                Ttl = config.DefaultTtl
            };

            // Create a key pair for testing
            var privateKey = new Key(SignatureAlgorithm.Ed25519);
            var publicKey = privateKey.PublicKey;

            // Act
            advertisement.Sign(privateKey);

            // Tamper with the advertisement after signing
            advertisement.Metadata["type"] = "tampered";

            bool isValid = advertisement.Verify(publicKey);

            // Assert
            Assert.IsFalse(isValid);
        }

        [TestMethod]
        public void DefaultUSDPSerializer_HandlesNullValues()
        {
            // Arrange
            var serializer = new DefaultUSDPSerializer();
            ServiceAdvertisement? nullObj = null;
            string? nullJson = null;
            byte[]? nullCbor = null;

            // Act & Assert - Should not throw exceptions
            var jsonResult = serializer.SerializeToJson(nullObj);
            var cborResult = serializer.SerializeToCbor(nullObj);
            var fromJsonResult = serializer.DeserializeFromJson<ServiceAdvertisement>(nullJson);
            var fromCborResult = serializer.DeserializeFromCbor<ServiceAdvertisement>(nullCbor);

            // Verify results - Check that operations handle null gracefully
            Assert.IsFalse(jsonResult.IsSuccess);
            Assert.IsFalse(cborResult.IsSuccess);
            Assert.IsFalse(fromJsonResult.IsSuccess);
            Assert.IsFalse(fromCborResult.IsSuccess);

            // Verify error handling
            Assert.IsNotNull(jsonResult.ErrorMessage);
            Assert.IsNotNull(cborResult.ErrorMessage);
            Assert.IsNotNull(fromJsonResult.ErrorMessage);
            Assert.IsNotNull(fromCborResult.ErrorMessage);
        }

        [TestMethod]
        public void DefaultUSDPSerializer_HandlesInvalidInput()
        {
            // Arrange
            var serializer = new DefaultUSDPSerializer();
            string invalidJson = "{invalid json}";
            byte[] invalidCbor = Encoding.UTF8.GetBytes("not cbor data");

            // Act
            var fromJsonResult = serializer.DeserializeFromJson<ServiceAdvertisement>(invalidJson);
            var fromCborResult = serializer.DeserializeFromCbor<ServiceAdvertisement>(invalidCbor);

            // Assert - Should handle invalid input gracefully
            Assert.IsFalse(fromJsonResult.IsSuccess);
            Assert.IsFalse(fromCborResult.IsSuccess);
            Assert.IsNotNull(fromJsonResult.ErrorMessage);
            Assert.IsNotNull(fromCborResult.ErrorMessage);
        }
    }
}