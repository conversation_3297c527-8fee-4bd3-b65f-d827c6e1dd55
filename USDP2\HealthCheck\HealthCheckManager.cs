using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace USDP2.HealthCheck
{
    /// <summary>
    /// Manages health checks for all USDP2 components and provides centralized health monitoring.
    /// </summary>
    public class HealthCheckManager : IDisposable
    {
        private readonly ConcurrentDictionary<string, HealthCheckRegistration> _healthChecks;
        private readonly Timer? _periodicHealthCheckTimer;
        private readonly SemaphoreSlim _healthCheckSemaphore;
        private readonly HealthCheckOptions _globalOptions;
        private volatile bool _disposed;

        /// <summary>
        /// Gets the last health report generated.
        /// </summary>
        public HealthReport? LastHealthReport { get; private set; }

        /// <summary>
        /// Event raised when a health check completes.
        /// </summary>
        public event EventHandler<HealthCheckCompletedEventArgs>? HealthCheckCompleted;

        /// <summary>
        /// Event raised when a health report is generated.
        /// </summary>
        public event EventHandler<HealthReportGeneratedEventArgs>? HealthReportGenerated;

        /// <summary>
        /// Initializes a new instance of the <see cref="HealthCheckManager"/> class.
        /// </summary>
        /// <param name="globalOptions">Global options for health checks.</param>
        public HealthCheckManager(HealthCheckOptions? globalOptions = null)
        {
            _healthChecks = new ConcurrentDictionary<string, HealthCheckRegistration>();
            _healthCheckSemaphore = new SemaphoreSlim(Environment.ProcessorCount, Environment.ProcessorCount);
            _globalOptions = globalOptions ?? new HealthCheckOptions();

            if (_globalOptions.Interval > TimeSpan.Zero)
            {
                _periodicHealthCheckTimer = new Timer(PeriodicHealthCheckCallback, null, _globalOptions.Interval, _globalOptions.Interval);
            }
        }

        /// <summary>
        /// Registers a health check.
        /// </summary>
        /// <param name="name">The name of the health check.</param>
        /// <param name="healthCheck">The health check instance.</param>
        /// <param name="options">The health check options.</param>
        /// <param name="tags">The tags associated with the health check.</param>
        public void RegisterHealthCheck(
            string name,
            IHealthCheck healthCheck,
            HealthCheckOptions? options = null,
            IEnumerable<string>? tags = null)
        {
            if (string.IsNullOrEmpty(name))
                throw new ArgumentException("Health check name cannot be null or empty", nameof(name));

            if (healthCheck == null)
                throw new ArgumentNullException(nameof(healthCheck));

            var registration = new HealthCheckRegistration(name, healthCheck, options, tags);
            _healthChecks.AddOrUpdate(name, registration, (key, existing) => registration);

            UsdpLogger.Log("HealthCheck.Registered", new
            {
                Name = name,
                Type = healthCheck.GetType().Name,
                Timeout = registration.Options.Timeout.TotalSeconds,
                Enabled = registration.Options.Enabled,
                Tags = registration.Tags
            });
        }

        /// <summary>
        /// Unregisters a health check.
        /// </summary>
        /// <param name="name">The name of the health check to unregister.</param>
        /// <returns>True if the health check was unregistered, false if it was not found.</returns>
        public bool UnregisterHealthCheck(string name)
        {
            if (string.IsNullOrEmpty(name))
                return false;

            var removed = _healthChecks.TryRemove(name, out var registration);
            
            if (removed)
            {
                UsdpLogger.Log("HealthCheck.Unregistered", new
                {
                    Name = name,
                    Type = registration?.HealthCheck.GetType().Name
                });
            }

            return removed;
        }

        /// <summary>
        /// Runs all registered health checks and generates a health report.
        /// </summary>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>A <see cref="Task{HealthReport}"/> representing the health report.</returns>
        public async Task<HealthReport> CheckHealthAsync(CancellationToken cancellationToken = default)
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(HealthCheckManager));

            var results = new Dictionary<string, HealthCheckResult>();
            var tasks = new List<Task>();

            foreach (var registration in _healthChecks.Values)
            {
                if (!registration.Options.Enabled)
                {
                    results[registration.Name] = HealthCheckResult.Unknown(
                        "Health check is disabled",
                        TimeSpan.Zero);
                    continue;
                }

                var task = RunHealthCheckAsync(registration, results, cancellationToken);
                tasks.Add(task);
            }

            // Wait for all health checks to complete
            await Task.WhenAll(tasks).ConfigureAwait(false);

            var report = new HealthReport(results);
            LastHealthReport = report;

            // Raise event
            HealthReportGenerated?.Invoke(this, new HealthReportGeneratedEventArgs(report));

            UsdpLogger.Log("HealthCheck.ReportGenerated", new
            {
                OverallStatus = report.Status.ToString(),
                TotalChecks = report.Entries.Count,
                HealthyChecks = report.Entries.Values.Count(r => r.IsHealthy),
                DegradedChecks = report.Entries.Values.Count(r => r.IsDegraded),
                UnhealthyChecks = report.Entries.Values.Count(r => r.IsUnhealthy),
                UnknownChecks = report.Entries.Values.Count(r => r.IsUnknown),
                TotalDuration = report.TotalDuration.TotalMilliseconds
            });

            return report;
        }

        /// <summary>
        /// Runs a specific health check by name.
        /// </summary>
        /// <param name="name">The name of the health check to run.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>A <see cref="Task{HealthCheckResult}"/> representing the health check result.</returns>
        public async Task<HealthCheckResult?> CheckHealthAsync(string name, CancellationToken cancellationToken = default)
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(HealthCheckManager));

            if (!_healthChecks.TryGetValue(name, out var registration))
                return null;

            if (!registration.Options.Enabled)
            {
                return HealthCheckResult.Unknown("Health check is disabled", TimeSpan.Zero);
            }

            await _healthCheckSemaphore.WaitAsync(cancellationToken).ConfigureAwait(false);
            try
            {
                var result = await registration.HealthCheck.CheckHealthAsync(cancellationToken).ConfigureAwait(false);
                
                // Raise event
                HealthCheckCompleted?.Invoke(this, new HealthCheckCompletedEventArgs(registration.Name, result));

                return result;
            }
            finally
            {
                _healthCheckSemaphore.Release();
            }
        }

        /// <summary>
        /// Gets all registered health check names.
        /// </summary>
        /// <returns>A collection of health check names.</returns>
        public IEnumerable<string> GetHealthCheckNames()
        {
            return _healthChecks.Keys.ToList();
        }

        /// <summary>
        /// Gets health checks by tag.
        /// </summary>
        /// <param name="tag">The tag to filter by.</param>
        /// <returns>A collection of health check registrations with the specified tag.</returns>
        public IEnumerable<HealthCheckRegistration> GetHealthChecksByTag(string tag)
        {
            return _healthChecks.Values.Where(r => r.Tags.Contains(tag, StringComparer.OrdinalIgnoreCase));
        }

        /// <summary>
        /// Runs a health check asynchronously and stores the result.
        /// </summary>
        private async Task RunHealthCheckAsync(
            HealthCheckRegistration registration,
            Dictionary<string, HealthCheckResult> results,
            CancellationToken cancellationToken)
        {
            await _healthCheckSemaphore.WaitAsync(cancellationToken).ConfigureAwait(false);
            try
            {
                var result = await registration.HealthCheck.CheckHealthAsync(cancellationToken).ConfigureAwait(false);
                
                lock (results)
                {
                    results[registration.Name] = result;
                }

                // Raise event
                HealthCheckCompleted?.Invoke(this, new HealthCheckCompletedEventArgs(registration.Name, result));
            }
            catch (Exception ex)
            {
                var result = HealthCheckResult.Unhealthy(
                    $"Health check threw an exception: {ex.Message}",
                    TimeSpan.Zero,
                    ex);

                lock (results)
                {
                    results[registration.Name] = result;
                }

                // Raise event
                HealthCheckCompleted?.Invoke(this, new HealthCheckCompletedEventArgs(registration.Name, result));
            }
            finally
            {
                _healthCheckSemaphore.Release();
            }
        }

        /// <summary>
        /// Periodic health check callback.
        /// </summary>
        private async void PeriodicHealthCheckCallback(object? state)
        {
            if (_disposed)
                return;

            try
            {
                using var cts = new CancellationTokenSource(TimeSpan.FromMinutes(5));
                await CheckHealthAsync(cts.Token).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                UsdpLogger.Log("HealthCheck.PeriodicCheckError", new
                {
                    Message = "Error during periodic health check",
                    Exception = ex.Message
                });
            }
        }

        /// <summary>
        /// Disposes the health check manager.
        /// </summary>
        public void Dispose()
        {
            if (_disposed)
                return;

            _disposed = true;
            _periodicHealthCheckTimer?.Dispose();
            _healthCheckSemaphore.Dispose();

            UsdpLogger.Log("HealthCheck.ManagerDisposed", new
            {
                Message = "Health check manager disposed",
                RegisteredChecks = _healthChecks.Count
            });
        }
    }

    /// <summary>
    /// Event arguments for health check completed events.
    /// </summary>
    public class HealthCheckCompletedEventArgs : EventArgs
    {
        /// <summary>
        /// Gets the name of the health check.
        /// </summary>
        public string Name { get; }

        /// <summary>
        /// Gets the health check result.
        /// </summary>
        public HealthCheckResult Result { get; }

        /// <summary>
        /// Initializes a new instance of the <see cref="HealthCheckCompletedEventArgs"/> class.
        /// </summary>
        /// <param name="name">The name of the health check.</param>
        /// <param name="result">The health check result.</param>
        public HealthCheckCompletedEventArgs(string name, HealthCheckResult result)
        {
            Name = name ?? throw new ArgumentNullException(nameof(name));
            Result = result ?? throw new ArgumentNullException(nameof(result));
        }
    }

    /// <summary>
    /// Event arguments for health report generated events.
    /// </summary>
    public class HealthReportGeneratedEventArgs : EventArgs
    {
        /// <summary>
        /// Gets the health report.
        /// </summary>
        public HealthReport Report { get; }

        /// <summary>
        /// Initializes a new instance of the <see cref="HealthReportGeneratedEventArgs"/> class.
        /// </summary>
        /// <param name="report">The health report.</param>
        public HealthReportGeneratedEventArgs(HealthReport report)
        {
            Report = report ?? throw new ArgumentNullException(nameof(report));
        }
    }
}
