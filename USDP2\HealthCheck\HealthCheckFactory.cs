using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace USDP2.HealthCheck
{
    /// <summary>
    /// Factory for creating and configuring health checks for USDP2 components.
    /// </summary>
    public static class HealthCheckFactory
    {
        /// <summary>
        /// Creates a comprehensive health check manager with all USDP2 component health checks.
        /// </summary>
        /// <param name="chordNode">The ChordNode to monitor (optional).</param>
        /// <param name="directoryNode">The DirectoryNode to monitor (optional).</param>
        /// <param name="networkSender">The network sender to monitor (optional).</param>
        /// <param name="networkReceiver">The network receiver to monitor (optional).</param>
        /// <param name="serviceCache">The ServiceAdvertisementCache to monitor (optional).</param>
        /// <returns>A configured <see cref="HealthCheckManager"/> with all applicable health checks.</returns>
        public static HealthCheckManager CreateComprehensiveHealthCheckManager(
            ChordNode? chordNode = null,
            DirectoryNode? directoryNode = null,
            INetworkSender? networkSender = null,
            INetworkReceiver? networkReceiver = null,
            ServiceAdvertisementCache? serviceCache = null)
        {
            var globalOptions = CreateGlobalHealthCheckOptions();
            var manager = new HealthCheckManager(globalOptions);

            // Register ChordNode health check if available and enabled
            if (chordNode != null && UsdpConfiguration.Instance.EnableChordNodeHealthCheck)
            {
                var chordHealthCheck = CreateChordNodeHealthCheck(chordNode);
                manager.RegisterHealthCheck("ChordNode", chordHealthCheck, null, new[] { "chord", "distributed", "core" });
            }

            // Register DirectoryNode health check if available and enabled
            if (directoryNode != null && UsdpConfiguration.Instance.EnableDirectoryNodeHealthCheck)
            {
                var directoryHealthCheck = CreateDirectoryNodeHealthCheck(directoryNode);
                manager.RegisterHealthCheck("DirectoryNode", directoryHealthCheck, null, new[] { "directory", "service-discovery", "core" });
            }

            // Register Network Components health check if available and enabled
            if ((networkSender != null || networkReceiver != null) && UsdpConfiguration.Instance.EnableNetworkComponentsHealthCheck)
            {
                var networkHealthCheck = CreateNetworkComponentsHealthCheck(networkSender, networkReceiver);
                manager.RegisterHealthCheck("NetworkComponents", networkHealthCheck, null, new[] { "network", "connectivity", "infrastructure" });
            }

            // Register ServiceAdvertisementCache health check if available and enabled
            if (serviceCache != null && UsdpConfiguration.Instance.EnableServiceCacheHealthCheck)
            {
                var cacheHealthCheck = CreateServiceCacheHealthCheck(serviceCache);
                manager.RegisterHealthCheck("ServiceCache", cacheHealthCheck, null, new[] { "cache", "performance", "memory" });
            }

            // Register system-level health checks
            RegisterSystemHealthChecks(manager);

            UsdpLogger.Log("HealthCheck.FactoryCreated", new
            {
                Message = "Comprehensive health check manager created",
                RegisteredChecks = manager.GetHealthCheckNames().Count(),
                ChordNodeEnabled = chordNode != null && UsdpConfiguration.Instance.EnableChordNodeHealthCheck,
                DirectoryNodeEnabled = directoryNode != null && UsdpConfiguration.Instance.EnableDirectoryNodeHealthCheck,
                NetworkComponentsEnabled = (networkSender != null || networkReceiver != null) && UsdpConfiguration.Instance.EnableNetworkComponentsHealthCheck,
                ServiceCacheEnabled = serviceCache != null && UsdpConfiguration.Instance.EnableServiceCacheHealthCheck
            });

            return manager;
        }

        /// <summary>
        /// Creates a ChordNode health check with appropriate configuration.
        /// </summary>
        /// <param name="chordNode">The ChordNode to monitor.</param>
        /// <returns>A configured <see cref="ChordNodeHealthCheck"/>.</returns>
        public static ChordNodeHealthCheck CreateChordNodeHealthCheck(ChordNode chordNode)
        {
            var options = new HealthCheckOptions
            {
                Timeout = UsdpConfiguration.Instance.HealthCheckTimeout,
                Interval = UsdpConfiguration.Instance.HealthCheckInterval,
                Enabled = UsdpConfiguration.Instance.EnableChordNodeHealthCheck,
                FailureThreshold = UsdpConfiguration.Instance.HealthCheckFailureThreshold,
                SuccessThreshold = UsdpConfiguration.Instance.HealthCheckSuccessThreshold,
                LogResults = UsdpConfiguration.Instance.LogHealthCheckResults,
                IncludeDetailedData = UsdpConfiguration.Instance.IncludeDetailedHealthData
            };

            return new ChordNodeHealthCheck(chordNode, options);
        }

        /// <summary>
        /// Creates a DirectoryNode health check with appropriate configuration.
        /// </summary>
        /// <param name="directoryNode">The DirectoryNode to monitor.</param>
        /// <returns>A configured <see cref="DirectoryNodeHealthCheck"/>.</returns>
        public static DirectoryNodeHealthCheck CreateDirectoryNodeHealthCheck(DirectoryNode directoryNode)
        {
            var options = new HealthCheckOptions
            {
                Timeout = UsdpConfiguration.Instance.HealthCheckTimeout,
                Interval = UsdpConfiguration.Instance.HealthCheckInterval,
                Enabled = UsdpConfiguration.Instance.EnableDirectoryNodeHealthCheck,
                FailureThreshold = UsdpConfiguration.Instance.HealthCheckFailureThreshold,
                SuccessThreshold = UsdpConfiguration.Instance.HealthCheckSuccessThreshold,
                LogResults = UsdpConfiguration.Instance.LogHealthCheckResults,
                IncludeDetailedData = UsdpConfiguration.Instance.IncludeDetailedHealthData
            };

            return new DirectoryNodeHealthCheck(directoryNode, options);
        }

        /// <summary>
        /// Creates a NetworkComponents health check with appropriate configuration.
        /// </summary>
        /// <param name="networkSender">The network sender to monitor (optional).</param>
        /// <param name="networkReceiver">The network receiver to monitor (optional).</param>
        /// <returns>A configured <see cref="NetworkComponentsHealthCheck"/>.</returns>
        public static NetworkComponentsHealthCheck CreateNetworkComponentsHealthCheck(
            INetworkSender? networkSender = null,
            INetworkReceiver? networkReceiver = null)
        {
            var options = new HealthCheckOptions
            {
                Timeout = UsdpConfiguration.Instance.HealthCheckTimeout,
                Interval = UsdpConfiguration.Instance.HealthCheckInterval,
                Enabled = UsdpConfiguration.Instance.EnableNetworkComponentsHealthCheck,
                FailureThreshold = UsdpConfiguration.Instance.HealthCheckFailureThreshold,
                SuccessThreshold = UsdpConfiguration.Instance.HealthCheckSuccessThreshold,
                LogResults = UsdpConfiguration.Instance.LogHealthCheckResults,
                IncludeDetailedData = UsdpConfiguration.Instance.IncludeDetailedHealthData
            };

            return new NetworkComponentsHealthCheck(networkSender, networkReceiver, options);
        }

        /// <summary>
        /// Creates a ServiceAdvertisementCache health check with appropriate configuration.
        /// </summary>
        /// <param name="serviceCache">The ServiceAdvertisementCache to monitor.</param>
        /// <returns>A configured <see cref="ServiceAdvertisementCacheHealthCheck"/>.</returns>
        public static ServiceAdvertisementCacheHealthCheck CreateServiceCacheHealthCheck(ServiceAdvertisementCache serviceCache)
        {
            var options = new HealthCheckOptions
            {
                Timeout = UsdpConfiguration.Instance.HealthCheckTimeout,
                Interval = UsdpConfiguration.Instance.HealthCheckInterval,
                Enabled = UsdpConfiguration.Instance.EnableServiceCacheHealthCheck,
                FailureThreshold = UsdpConfiguration.Instance.HealthCheckFailureThreshold,
                SuccessThreshold = UsdpConfiguration.Instance.HealthCheckSuccessThreshold,
                LogResults = UsdpConfiguration.Instance.LogHealthCheckResults,
                IncludeDetailedData = UsdpConfiguration.Instance.IncludeDetailedHealthData
            };

            return new ServiceAdvertisementCacheHealthCheck(serviceCache, options);
        }

        /// <summary>
        /// Creates global health check options based on configuration.
        /// </summary>
        /// <returns>A configured <see cref="HealthCheckOptions"/> instance.</returns>
        private static HealthCheckOptions CreateGlobalHealthCheckOptions()
        {
            return new HealthCheckOptions
            {
                Timeout = UsdpConfiguration.Instance.HealthCheckTimeout,
                Interval = UsdpConfiguration.Instance.HealthCheckInterval,
                Enabled = UsdpConfiguration.Instance.HealthChecksEnabled,
                FailureThreshold = UsdpConfiguration.Instance.HealthCheckFailureThreshold,
                SuccessThreshold = UsdpConfiguration.Instance.HealthCheckSuccessThreshold,
                LogResults = UsdpConfiguration.Instance.LogHealthCheckResults,
                IncludeDetailedData = UsdpConfiguration.Instance.IncludeDetailedHealthData
            };
        }

        /// <summary>
        /// Registers system-level health checks that don't require specific components.
        /// </summary>
        /// <param name="manager">The health check manager to register checks with.</param>
        private static void RegisterSystemHealthChecks(HealthCheckManager manager)
        {
            // Register serialization health check
            var serializationHealthCheck = CreateSerializationHealthCheck();
            manager.RegisterHealthCheck("Serialization", serializationHealthCheck, null, new[] { "serialization", "performance", "system" });

            // Register circuit breaker health check
            var circuitBreakerHealthCheck = CreateCircuitBreakerHealthCheck();
            manager.RegisterHealthCheck("CircuitBreakers", circuitBreakerHealthCheck, null, new[] { "circuit-breaker", "resilience", "system" });
        }

        /// <summary>
        /// Creates a serialization system health check.
        /// </summary>
        /// <returns>A configured serialization health check.</returns>
        private static IHealthCheck CreateSerializationHealthCheck()
        {
            return new SerializationSystemHealthCheck();
        }

        /// <summary>
        /// Creates a circuit breaker system health check.
        /// </summary>
        /// <returns>A configured circuit breaker health check.</returns>
        private static IHealthCheck CreateCircuitBreakerHealthCheck()
        {
            return new CircuitBreakerSystemHealthCheck();
        }
    }

    /// <summary>
    /// Health check for the serialization system.
    /// </summary>
    internal class SerializationSystemHealthCheck : HealthCheckBase
    {
        public SerializationSystemHealthCheck()
            : base("SerializationSystem", "Monitors serialization performance and cache effectiveness")
        {
        }

        protected override async Task<HealthCheckResult> PerformHealthCheckAsync(CancellationToken cancellationToken)
        {
            var healthData = new Dictionary<string, object>();
            var issues = new List<string>();

            try
            {
                // Check basic serialization functionality
                var testServiceId = new ServiceIdentifier("health-check", "serialization-test");
                var testEndpoint = new TransportEndpoint
                {
                    Address = "127.0.0.1",
                    Port = 8080,
                    Protocol = "http"
                };
                var testAdvertisement = new ServiceAdvertisement(testServiceId, testEndpoint);

                // Test JSON serialization
                var jsonResult = testAdvertisement.ToJsonWithResult();
                healthData["jsonSerializationWorking"] = jsonResult.IsSuccess;

                // Test CBOR serialization
                var cborResult = testAdvertisement.ToCborWithResult();
                healthData["cborSerializationWorking"] = cborResult.IsSuccess;

                // Evaluate health
                if (!jsonResult.IsSuccess)
                {
                    issues.Add($"JSON serialization failed: {jsonResult.ErrorMessage}");
                }

                if (!cborResult.IsSuccess)
                {
                    issues.Add($"CBOR serialization failed: {cborResult.ErrorMessage}");
                }

                await Task.Delay(1, cancellationToken).ConfigureAwait(false);

                var status = issues.Count == 0 ? HealthStatus.Healthy :
                           issues.Count > 2 ? HealthStatus.Degraded : HealthStatus.Healthy;

                return new HealthCheckResult(status,
                    issues.Count == 0 ? "Serialization system is healthy" : $"Serialization system has {issues.Count} issue(s)",
                    TimeSpan.Zero, null, healthData);
            }
            catch (Exception ex)
            {
                return HealthCheckResult.Unhealthy("Serialization system health check failed", TimeSpan.Zero, ex, healthData);
            }
        }
    }

    /// <summary>
    /// Health check for the circuit breaker system.
    /// </summary>
    internal class CircuitBreakerSystemHealthCheck : HealthCheckBase
    {
        public CircuitBreakerSystemHealthCheck()
            : base("CircuitBreakerSystem", "Monitors circuit breaker states and failure rates")
        {
        }

        protected override async Task<HealthCheckResult> PerformHealthCheckAsync(CancellationToken cancellationToken)
        {
            var healthData = new Dictionary<string, object>();
            var issues = new List<string>();

            try
            {
                var summary = CircuitBreakerManager.Instance.GetHealthSummary();

                healthData["totalCircuitBreakers"] = summary.TotalCircuitBreakers;
                healthData["openCircuits"] = summary.OpenCircuits;
                healthData["halfOpenCircuits"] = summary.HalfOpenCircuits;
                healthData["closedCircuits"] = summary.ClosedCircuits;
                healthData["overallFailureRate"] = summary.OverallFailureRate;

                if (summary.OpenCircuits > summary.TotalCircuitBreakers * 0.5)
                {
                    issues.Add($"Many circuit breakers are open: {summary.OpenCircuits}/{summary.TotalCircuitBreakers}");
                }

                if (summary.OverallFailureRate > 20)
                {
                    issues.Add($"High overall failure rate: {summary.OverallFailureRate:F1}%");
                }

                await Task.Delay(1, cancellationToken).ConfigureAwait(false);

                var status = issues.Count == 0 ? HealthStatus.Healthy :
                           summary.OpenCircuits > 0 ? HealthStatus.Degraded : HealthStatus.Healthy;

                return new HealthCheckResult(status,
                    issues.Count == 0 ? "Circuit breaker system is healthy" : $"Circuit breaker system has {issues.Count} issue(s)",
                    TimeSpan.Zero, null, healthData);
            }
            catch (Exception ex)
            {
                return HealthCheckResult.Unhealthy("Circuit breaker system health check failed", TimeSpan.Zero, ex, healthData);
            }
        }
    }
}
