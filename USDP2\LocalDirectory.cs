using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;

namespace USDP2
{
    /// <summary>
    /// Local Directory (LD): In-memory cache with multicast listener/announcer.
    /// </summary>
    public class LocalDirectory : IAsyncDisposable, IDisposable
    {
        /// <summary>
        /// The multicast sender.
        /// </summary>
        private readonly INetworkSender _multicastSender;
        /// <summary>
        /// The multicast receiver.
        /// </summary>
        private readonly INetworkReceiver _multicastReceiver;
        /// <summary>
        /// The cache.
        /// </summary>
        private readonly ConcurrentDictionary<string, ServiceAdvertisement> _cache = new();
        /// <summary>
        /// The multicast port.
        /// </summary>
        private readonly int _multicastPort;
        /// <summary>
        /// The multicast address.
        /// </summary>
        private readonly string _multicastAddress;
        /// <summary>
        /// The Bloom filter manager for optimizing service discovery.
        /// </summary>
        private readonly BloomFilterManager _bloomFilterManager;

        /// <summary>
        /// Initializes a new instance of the <see cref="LocalDirectory"/> class.
        /// </summary>
        /// <param name="sender">The network sender for multicast announcements.</param>
        /// <param name="receiver">The network receiver for multicast messages.</param>
        /// <param name="multicastAddress">The multicast address to use.</param>
        /// <param name="multicastPort">The multicast port to use.</param>
        /// <exception cref="ArgumentNullException">Thrown when sender, receiver, or multicastAddress is null.</exception>
        /// <exception cref="ArgumentException">Thrown when multicastAddress is empty or whitespace.</exception>
        /// <exception cref="ArgumentOutOfRangeException">Thrown when multicastPort is not in valid range.</exception>
        public LocalDirectory(INetworkSender sender, INetworkReceiver receiver, string multicastAddress, int multicastPort)
        {
            _multicastSender = sender ?? throw new ArgumentNullException(nameof(sender));
            _multicastReceiver = receiver ?? throw new ArgumentNullException(nameof(receiver));

            if (string.IsNullOrWhiteSpace(multicastAddress))
                throw new ArgumentException("Multicast address cannot be null or empty.", nameof(multicastAddress));

            if (multicastPort <= 0 || multicastPort > 65535)
                throw new ArgumentOutOfRangeException(nameof(multicastPort), "Port must be between 1 and 65535.");

            _multicastAddress = multicastAddress;
            _multicastPort = multicastPort;
            _bloomFilterManager = new BloomFilterManager();
        }

        /// <summary>
        /// Start listening for multicast advertisements and queries.
        /// </summary>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        /// <exception cref="ObjectDisposedException">Thrown when the object has been disposed.</exception>
        public async Task StartAsync(CancellationToken cancellationToken = default)
        {
            ThrowIfDisposed();
            await _multicastReceiver.StartReceivingAsync(OnMessageReceivedAsync, cancellationToken).ConfigureAwait(false);
        }

        /// <summary>
        /// Throws an ObjectDisposedException if this object has been disposed.
        /// </summary>
        /// <exception cref="ObjectDisposedException">Thrown when the object has been disposed.</exception>
        private void ThrowIfDisposed()
        {
            if (_disposed)
            {
                throw new ObjectDisposedException(nameof(LocalDirectory));
            }
        }

        /// <summary>
        /// Announce a service on the multicast group and cache it locally.
        /// </summary>
        /// <param name="advertisement">The service advertisement to announce.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        /// <exception cref="ObjectDisposedException">Thrown when the object has been disposed.</exception>
        /// <exception cref="ArgumentNullException">Thrown when the advertisement is null.</exception>
        /// <exception cref="SerializationException">Thrown when the advertisement cannot be serialized.</exception>
        public async Task AnnounceServiceAsync(ServiceAdvertisement advertisement, CancellationToken cancellationToken = default)
        {
            ThrowIfDisposed();

            if (advertisement == null)
            {
                throw new ArgumentNullException(nameof(advertisement));
            }

            _cache[advertisement.ServiceId.FullName] = advertisement;
            _bloomFilterManager.AddService(advertisement);
            Diagnostics.Log("ServiceAnnounced", new { ServiceId = advertisement.ServiceId.FullName });

            // Use the new error handling approach
            var serializationResult = advertisement.ToCborWithResult();
            if (!serializationResult.IsSuccess)
            {
                throw new SerializationException($"Failed to serialize service advertisement: {serializationResult.ErrorMessage}",
                    serializationResult.ErrorCode, serializationResult.Exception);
            }

            await _multicastSender.SendAsync(serializationResult.Value ?? Array.Empty<byte>(), _multicastAddress, _multicastPort, cancellationToken).ConfigureAwait(false);
        }

        /// <summary>
        /// Handle incoming multicast messages (advertisements or queries).
        /// </summary>
        private async Task OnMessageReceivedAsync(byte[] data, string remoteAddress, int remotePort)
        {
            if (TryHandleAdvertisement(data, remoteAddress, remotePort))
            {
                return;
            }

            if (TryHandleQuery(data, remoteAddress, remotePort))
            {
                return;
            }

            LogUnknownMessage(data, remoteAddress, remotePort);
        }

        private bool TryHandleAdvertisement(byte[] data, string remoteAddress, int remotePort)
        {
            // First validate the incoming network data
            var dataValidation = NetworkDataValidator.ValidateIncomingData(data, remoteAddress, remotePort);
            if (!dataValidation.IsValid)
            {
                UsdpLogger.Log("LocalDirectory.InvalidNetworkData", new
                {
                    RemoteAddress = remoteAddress,
                    RemotePort = remotePort,
                    ErrorType = dataValidation.ErrorType,
                    ErrorMessage = dataValidation.ErrorMessage,
                    DataSize = data?.Length ?? 0,
                    Severity = "Warning"
                });
                return false;
            }

            var advResult = ServiceAdvertisement.FromCborWithResult(data);
            if (advResult.IsSuccess && advResult.Value != null)
            {
                // Validate and sanitize the service advertisement
                var advValidation = NetworkDataValidator.ValidateServiceAdvertisement(
                    advResult.Value, remoteAddress, remotePort);

                if (advValidation.IsValid && advValidation.SanitizedAdvertisement != null)
                {
                    var sanitizedAdv = advValidation.SanitizedAdvertisement;
                    _cache[sanitizedAdv.ServiceId.FullName] = sanitizedAdv;
                    _bloomFilterManager.AddService(sanitizedAdv);

                    UsdpLogger.Log("LocalDirectory.AdvertisementReceived", new
                    {
                        ServiceId = sanitizedAdv.ServiceId.FullName,
                        RemoteAddress = remoteAddress,
                        RemotePort = remotePort,
                        Sanitized = true
                    });
                    return true;
                }
                else
                {
                    UsdpLogger.Log("LocalDirectory.AdvertisementValidationFailed", new
                    {
                        RemoteAddress = remoteAddress,
                        RemotePort = remotePort,
                        ErrorMessage = advValidation.ErrorMessage,
                        Severity = "Warning"
                    });
                    return false;
                }
            }
            else if (advResult.ErrorCode != SerializationErrorCode.InvalidFormat)
            {
                UsdpLogger.Log("LocalDirectory.AdvertisementDeserializationError", new
                {
                    RemoteAddress = remoteAddress,
                    RemotePort = remotePort,
                    Error = advResult.ErrorMessage
                });
            }
            return false;
        }

        private bool TryHandleQuery(byte[] data, string remoteAddress, int remotePort)
        {
            var queryResult = ServiceQuery.FromCborWithResult(data);
            if (queryResult.IsSuccess && queryResult.Value != null)
            {
                var matches = FilterAdvertisements(queryResult.Value);
                _ = SendQueryResponsesAsync(matches, remoteAddress, remotePort);
                return true;
            }
            else if (queryResult.ErrorCode != SerializationErrorCode.InvalidFormat)
            {
                Diagnostics.Log("QueryDeserializationError", new { RemoteAddress = remoteAddress, RemotePort = remotePort, Error = queryResult.ErrorMessage });
            }
            return false;
        }

        private IEnumerable<ServiceAdvertisement> FilterAdvertisements(ServiceQuery query)
        {
            var candidates = _cache.Values.AsEnumerable();

            // Use Bloom filter for initial filtering if enabled
            if (_bloomFilterManager.IsEnabled)
            {
                candidates = candidates.Where(a => PassesBloomFilterCheck(a, query));
            }

            // Apply detailed filtering on remaining candidates
            return candidates.Where(a =>
                (query.SidFilter == null || a.ServiceId.FullName.Contains(query.SidFilter, StringComparison.OrdinalIgnoreCase)) &&
                (query.MetadataFilter == null || query.MetadataFilter.All(f =>
                    a.Metadata.TryGetValue(f.Key, out var v) && v is string strValue && strValue == f.Value))
            ).ToList();
        }

        /// <summary>
        /// Checks if a service advertisement passes the Bloom filter check for a given query.
        /// This provides fast elimination of services that definitely don't match.
        /// </summary>
        /// <param name="advertisement">The service advertisement to check.</param>
        /// <param name="query">The service query to match against.</param>
        /// <returns>True if the service might match (subject to false positives), false if it definitely doesn't match.</returns>
        private bool PassesBloomFilterCheck(ServiceAdvertisement advertisement, ServiceQuery query)
        {
            // Check service identifier filter
            if (!string.IsNullOrEmpty(query.SidFilter))
            {
                // Extract namespace and name from the filter
                var parts = query.SidFilter.Split('/', StringSplitOptions.RemoveEmptyEntries);
                if (parts.Length >= 2)
                {
                    string filterNamespace = parts[0];
                    string filterName = parts[1];

                    if (!_bloomFilterManager.MightContainService(filterNamespace, filterName))
                    {
                        return false; // Definitely doesn't match
                    }
                }
                else if (parts.Length == 1)
                {
                    // Single part filter - check both namespace and name
                    string filterPart = parts[0];
                    if (!_bloomFilterManager.MightContainService(filterPart, "*") &&
                        !_bloomFilterManager.MightContainService("*", filterPart))
                    {
                        return false; // Definitely doesn't match
                    }
                }
            }

            // Check metadata filters
            if (query.MetadataFilter != null)
            {
                foreach (var metadataFilter in query.MetadataFilter)
                {
                    if (!_bloomFilterManager.MightContainMetadata(metadataFilter.Key, metadataFilter.Value))
                    {
                        return false; // Definitely doesn't match this metadata
                    }
                }
            }

            return true; // Might match (subject to false positives)
        }

        private async Task SendQueryResponsesAsync(IEnumerable<ServiceAdvertisement> matches, string remoteAddress, int remotePort)
        {
            foreach (var match in matches)
            {
                var serializationResult = match.ToCborWithResult();
                if (serializationResult.IsSuccess)
                {
                    await _multicastSender.SendAsync(serializationResult.Value ?? Array.Empty<byte>(), remoteAddress, remotePort).ConfigureAwait(false);
                    Diagnostics.Log("QueryResponseSent", new { ServiceId = match.ServiceId.FullName, RemoteAddress = remoteAddress, RemotePort = remotePort });
                }
                else
                {
                    Diagnostics.Log("QueryResponseSerializationError", new { ServiceId = match.ServiceId.FullName, RemoteAddress = remoteAddress, RemotePort = remotePort, Error = serializationResult.ErrorMessage });
                }
            }
        }

        private static void LogUnknownMessage(byte[] data, string remoteAddress, int remotePort)
        {
            Diagnostics.Log("UnknownMessageReceived", new { RemoteAddress = remoteAddress, RemotePort = remotePort, DataLength = data.Length });
        }


        /// <summary>
        /// Get all cached advertisements.
        /// </summary>
        /// <returns>An enumerable collection of all cached service advertisements.</returns>
        /// <exception cref="ObjectDisposedException">Thrown when the object has been disposed.</exception>
        public IEnumerable<ServiceAdvertisement> GetCachedAdvertisements()
        {
            ThrowIfDisposed();
            return _cache.Values;
        }

        /// <summary>
        /// Gets performance statistics for the Bloom filter optimization.
        /// </summary>
        /// <returns>Dictionary containing Bloom filter performance metrics.</returns>
        /// <exception cref="ObjectDisposedException">Thrown when the object has been disposed.</exception>
        public Dictionary<string, object> GetBloomFilterStatistics()
        {
            ThrowIfDisposed();
            return _bloomFilterManager.GetStatistics();
        }

        /// <summary>
        /// Refreshes the Bloom filters with current cached services.
        /// This can be useful after bulk service updates or to reset filter accuracy.
        /// </summary>
        /// <exception cref="ObjectDisposedException">Thrown when the object has been disposed.</exception>
        public void RefreshBloomFilters()
        {
            ThrowIfDisposed();
            _bloomFilterManager.RefreshFilters(_cache.Values);
        }

        #region IDisposable and IAsyncDisposable Implementation

        /// <summary>
        /// Tracks whether the object has been disposed to prevent multiple disposal attempts.
        /// Uses volatile to ensure thread-safe reads across multiple threads.
        /// </summary>
        private volatile bool _disposed;

        /// <summary>
        /// Synchronization object to ensure thread-safe disposal operations.
        /// Prevents race conditions when multiple threads attempt disposal simultaneously.
        /// </summary>
        private readonly object _disposeLock = new object();

        /// <summary>
        /// Releases all resources used by the <see cref="LocalDirectory"/>.
        ///
        /// This method implements the standard IDisposable pattern and can be called
        /// multiple times safely. It performs synchronous disposal of resources.
        /// </summary>
        public void Dispose()
        {
            Dispose(disposing: true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Asynchronously releases the resources used by the <see cref="LocalDirectory"/>.
        ///
        /// This method implements the standard IAsyncDisposable pattern and provides
        /// proper async disposal with exception handling and resource cleanup ordering.
        /// It can be called multiple times safely.
        /// </summary>
        /// <returns>A <see cref="ValueTask"/> representing the asynchronous dispose operation.</returns>
        public async ValueTask DisposeAsync()
        {
            await DisposeAsyncCore().ConfigureAwait(false);

            // Call Dispose(false) to clean up any remaining synchronous resources
            // and ensure the disposal state is properly set
            Dispose(disposing: false);

            // Suppress finalization since we've already cleaned up
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Core async disposal logic with proper exception handling and resource cleanup ordering.
        ///
        /// This method handles the actual async resource disposal with the following guarantees:
        /// - Thread-safe disposal (only disposes once)
        /// - Proper exception handling (continues disposal even if individual resources fail)
        /// - Correct disposal order (async resources first, then sync resources)
        /// - Resource nullification to prevent further use
        /// </summary>
        /// <returns>A <see cref="ValueTask"/> representing the async disposal operation.</returns>
#pragma warning disable VSTHRD200 // DisposeAsyncCore is a standard pattern method and doesn't need Async suffix
        protected virtual async ValueTask DisposeAsyncCore()
#pragma warning restore VSTHRD200
        {
            // Use lock to ensure thread-safe disposal
            lock (_disposeLock)
            {
                if (_disposed)
                {
                    return; // Already disposed
                }
                _disposed = true;
            }

            // List to collect any disposal exceptions for aggregate reporting
            var disposalExceptions = new List<Exception>();

            try
            {
                // Phase 1: Dispose async resources first (network components)
                // These may need to send final messages or complete ongoing operations

                UsdpLogger.Log("LocalDirectory.DisposeAsync.Start", new
                {
                    HasAsyncReceiver = _multicastReceiver is IAsyncDisposable,
                    HasAsyncSender = _multicastSender is IAsyncDisposable,
                    HasSyncReceiver = _multicastReceiver is IDisposable,
                    HasSyncSender = _multicastSender is IDisposable
                });

                // Dispose multicast receiver (async first, then sync fallback)
                if (_multicastReceiver is IAsyncDisposable asyncReceiver)
                {
                    try
                    {
                        await asyncReceiver.DisposeAsync().ConfigureAwait(false);
                        UsdpLogger.Log("LocalDirectory.ReceiverAsyncDisposed", new { Success = true });
                    }
                    catch (Exception ex)
                    {
                        disposalExceptions.Add(new InvalidOperationException("Failed to dispose multicast receiver asynchronously", ex));
                        UsdpLogger.Log("LocalDirectory.ReceiverAsyncDisposeFailed", new { Error = ex.Message });

                        // Try sync disposal as fallback
                        try
                        {
                            if (_multicastReceiver is IDisposable syncReceiver)
                            {
                                syncReceiver.Dispose();
                                UsdpLogger.Log("LocalDirectory.ReceiverSyncFallbackSuccess", new { Success = true });
                            }
                        }
                        catch (Exception syncEx)
                        {
                            disposalExceptions.Add(new InvalidOperationException("Failed to dispose multicast receiver synchronously (fallback)", syncEx));
                            UsdpLogger.Log("LocalDirectory.ReceiverSyncFallbackFailed", new { Error = syncEx.Message });
                        }
                    }
                }
                else if (_multicastReceiver is IDisposable syncReceiver)
                {
                    try
                    {
                        syncReceiver.Dispose();
                        UsdpLogger.Log("LocalDirectory.ReceiverSyncDisposed", new { Success = true });
                    }
                    catch (Exception ex)
                    {
                        disposalExceptions.Add(new InvalidOperationException("Failed to dispose multicast receiver", ex));
                        UsdpLogger.Log("LocalDirectory.ReceiverSyncDisposeFailed", new { Error = ex.Message });
                    }
                }

                // Dispose multicast sender (async first, then sync fallback)
                if (_multicastSender is IAsyncDisposable asyncSender)
                {
                    try
                    {
                        await asyncSender.DisposeAsync().ConfigureAwait(false);
                        UsdpLogger.Log("LocalDirectory.SenderAsyncDisposed", new { Success = true });
                    }
                    catch (Exception ex)
                    {
                        disposalExceptions.Add(new InvalidOperationException("Failed to dispose multicast sender asynchronously", ex));
                        UsdpLogger.Log("LocalDirectory.SenderAsyncDisposeFailed", new { Error = ex.Message });

                        // Try sync disposal as fallback
                        try
                        {
                            if (_multicastSender is IDisposable syncSender)
                            {
                                syncSender.Dispose();
                                UsdpLogger.Log("LocalDirectory.SenderSyncFallbackSuccess", new { Success = true });
                            }
                        }
                        catch (Exception syncEx)
                        {
                            disposalExceptions.Add(new InvalidOperationException("Failed to dispose multicast sender synchronously (fallback)", syncEx));
                            UsdpLogger.Log("LocalDirectory.SenderSyncFallbackFailed", new { Error = syncEx.Message });
                        }
                    }
                }
                else if (_multicastSender is IDisposable syncSender)
                {
                    try
                    {
                        syncSender.Dispose();
                        UsdpLogger.Log("LocalDirectory.SenderSyncDisposed", new { Success = true });
                    }
                    catch (Exception ex)
                    {
                        disposalExceptions.Add(new InvalidOperationException("Failed to dispose multicast sender", ex));
                        UsdpLogger.Log("LocalDirectory.SenderSyncDisposeFailed", new { Error = ex.Message });
                    }
                }

                // Phase 2: Dispose synchronous managed resources
                try
                {
                    _bloomFilterManager?.Dispose();
                    UsdpLogger.Log("LocalDirectory.BloomFilterDisposed", new { Success = true });
                }
                catch (Exception ex)
                {
                    disposalExceptions.Add(new InvalidOperationException("Failed to dispose bloom filter manager", ex));
                    UsdpLogger.Log("LocalDirectory.BloomFilterDisposeFailed", new { Error = ex.Message });
                }

                // Phase 3: Clear cache and nullify large objects to help GC
                try
                {
                    _cache.Clear();
                    UsdpLogger.Log("LocalDirectory.CacheCleared", new { Success = true });
                }
                catch (Exception ex)
                {
                    disposalExceptions.Add(new InvalidOperationException("Failed to clear service cache", ex));
                    UsdpLogger.Log("LocalDirectory.CacheClearFailed", new { Error = ex.Message });
                }

                UsdpLogger.Log("LocalDirectory.DisposeAsync.Complete", new
                {
                    Success = disposalExceptions.Count == 0,
                    ExceptionCount = disposalExceptions.Count
                });
            }
            catch (Exception ex)
            {
                // Catch any unexpected exceptions during disposal
                disposalExceptions.Add(new InvalidOperationException("Unexpected error during async disposal", ex));
                UsdpLogger.Log("LocalDirectory.DisposeAsync.UnexpectedError", new { Error = ex.Message });
            }

            // If we collected any disposal exceptions, throw an aggregate exception
            // This ensures that disposal failures are reported while still completing cleanup
            if (disposalExceptions.Count > 0)
            {
                throw new AggregateException("One or more errors occurred during LocalDirectory disposal", disposalExceptions);
            }
        }

        /// <summary>
        /// Releases the unmanaged resources used by the <see cref="LocalDirectory"/> and optionally releases the managed resources.
        ///
        /// This method implements the standard disposal pattern with proper exception handling
        /// and thread safety. It's called by both Dispose() and DisposeAsync().
        /// </summary>
        /// <param name="disposing">
        /// true to release both managed and unmanaged resources;
        /// false to release only unmanaged resources (called from finalizer or after async disposal).
        /// </param>
        protected virtual void Dispose(bool disposing)
        {
            // Use lock to ensure thread-safe disposal
            lock (_disposeLock)
            {
                if (_disposed)
                {
                    return; // Already disposed
                }

                if (disposing)
                {
                    // Only set disposed to true when disposing managed resources
                    // If called from DisposeAsync, this will already be true
                    _disposed = true;
                }
            }

            if (disposing)
            {
                // Dispose managed resources with exception handling
                // Note: If called after DisposeAsync, these resources should already be disposed

                var disposalExceptions = new List<Exception>();

                try
                {
                    UsdpLogger.Log("LocalDirectory.Dispose.Start", new { DisposingManagedResources = disposing });

                    // Dispose bloom filter manager
                    try
                    {
                        _bloomFilterManager?.Dispose();
                    }
                    catch (Exception ex)
                    {
                        disposalExceptions.Add(new InvalidOperationException("Failed to dispose bloom filter manager in sync disposal", ex));
                    }

                    // Dispose network components (sync only - async should have been handled by DisposeAsync)
                    try
                    {
                        if (_multicastReceiver is IDisposable disposableReceiver)
                        {
                            disposableReceiver.Dispose();
                        }
                    }
                    catch (Exception ex)
                    {
                        disposalExceptions.Add(new InvalidOperationException("Failed to dispose multicast receiver in sync disposal", ex));
                    }

                    try
                    {
                        if (_multicastSender is IDisposable disposableSender)
                        {
                            disposableSender.Dispose();
                        }
                    }
                    catch (Exception ex)
                    {
                        disposalExceptions.Add(new InvalidOperationException("Failed to dispose multicast sender in sync disposal", ex));
                    }

                    // Clear cache
                    try
                    {
                        _cache.Clear();
                    }
                    catch (Exception ex)
                    {
                        disposalExceptions.Add(new InvalidOperationException("Failed to clear cache in sync disposal", ex));
                    }

                    UsdpLogger.Log("LocalDirectory.Dispose.Complete", new
                    {
                        Success = disposalExceptions.Count == 0,
                        ExceptionCount = disposalExceptions.Count
                    });

                    // Report any disposal exceptions
                    if (disposalExceptions.Count > 0)
                    {
                        throw new AggregateException("One or more errors occurred during LocalDirectory synchronous disposal", disposalExceptions);
                    }
                }
                catch (Exception ex)
                {
                    UsdpLogger.Log("LocalDirectory.Dispose.Failed", new { Error = ex.Message });
                    // In Dispose, we typically don't re-throw exceptions to avoid issues in finalizers
                    // But we log them for debugging purposes
                }
            }

            // Free unmanaged resources (none in this class currently)
            // This section would handle unmanaged resources like file handles, native memory, etc.
        }

        /// <summary>
        /// Finalizer to ensure resources are cleaned up if Dispose is not called.
        ///
        /// This provides a safety net for resource cleanup, though proper disposal
        /// via Dispose() or DisposeAsync() is strongly recommended.
        /// </summary>
        ~LocalDirectory()
        {
            // Call Dispose(false) to clean up unmanaged resources only
            // Don't dispose managed resources from finalizer as they may have already been collected
            Dispose(disposing: false);
        }

        #endregion
    }
}