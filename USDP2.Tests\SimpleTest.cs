using Microsoft.VisualStudio.TestTools.UnitTesting;

namespace USDP2.Tests
{
    [TestClass]
    public class SimpleTest
    {
        [TestMethod]
        public void SimpleTest_BasicAssertion_Passes()
        {
            // Arrange
            var expected = 42;
            var actual = 42;

            // Act & Assert
            Assert.AreEqual(expected, actual);
        }

        [TestMethod]
        public void SimpleTest_StringComparison_Passes()
        {
            // Arrange
            var expected = "Hello World";
            var actual = "Hello World";

            // Act & Assert
            Assert.AreEqual(expected, actual);
        }
    }
}
