using System;

namespace USDP2
{
    /// <summary>
    /// The network receiver factory.
    /// </summary>
    public static class NetworkReceiverFactory
    {
        /// <summary>
        /// Creates the receiver.
        /// </summary>
        /// <param name="scope">The scope.</param>
        /// <param name="port">The port.</param>
        /// <param name="multicastAddress">The multicast address.</param>
        /// <param name="config">The USDP configuration instance. If null, uses UsdpConfiguration.Instance.</param>
        /// <exception cref="ArgumentOutOfRangeException"></exception>
        /// <returns>An <see cref="INetworkReceiver"/></returns>
        public static INetworkReceiver CreateReceiver(NetworkScope scope, int port, string? multicastAddress = null, UsdpConfiguration? config = null)
        {
            try
            {
                INetworkReceiver receiver = scope switch
                {
                    NetworkScope.Local => new UdpNetworkReceiver(port, isMulticast: multicastAddress != null, multicastAddress, config),
                    NetworkScope.Global => new HttpNetworkReceiver(port), // or new TcpNetworkReceiver(port)
                    _ => throw new ArgumentOutOfRangeException(nameof(scope), scope, null)
                };
                Diagnostics.Log("NetworkReceiverCreated", new { Scope = scope, Port = port, MulticastAddress = multicastAddress });
                return receiver;
            }
            catch (Exception ex)
            {
                Diagnostics.Log("NetworkReceiverCreationError", new { Scope = scope, Port = port, MulticastAddress = multicastAddress, Error = ex.Message });
                throw;
            }
        }
    }
}