using System;
using System.Text.Json;
using System.Threading;

namespace USDP2
{
    /// <summary>
    /// A secure USDP serializer that includes comprehensive input validation and sanitization
    /// to prevent security issues and DoS attacks. This serializer wraps the default serializer
    /// with additional security measures.
    /// </summary>
    public class SecureUSDPSerializer : IUSDPSerializer
    {
        private readonly IUSDPSerializer _innerSerializer;
        private readonly NetworkDataValidationStats _validationStats;
        private readonly object _statsLock = new object();

        /// <summary>
        /// Gets the current validation statistics.
        /// </summary>
        public NetworkDataValidationStats ValidationStats
        {
            get
            {
                lock (_statsLock)
                {
                    return new NetworkDataValidationStats
                    {
                        TotalValidations = _validationStats.TotalValidations,
                        SuccessfulValidations = _validationStats.SuccessfulValidations,
                        FailedValidations = _validationStats.FailedValidations,
                        OversizedPayloadRejections = _validationStats.OversizedPayloadRejections,
                        MaliciousContentDetections = _validationStats.MaliciousContentDetections,
                        SuspiciousContentDetections = _validationStats.SuspiciousContentDetections,
                        LastValidation = _validationStats.LastValidation,
                        StatsResetTime = _validationStats.StatsResetTime
                    };
                }
            }
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="SecureUSDPSerializer"/> class.
        /// </summary>
        /// <param name="innerSerializer">The inner serializer to wrap. If null, uses DefaultUSDPSerializer.</param>
        public SecureUSDPSerializer(IUSDPSerializer? innerSerializer = null)
        {
            _innerSerializer = innerSerializer ?? new DefaultUSDPSerializer();
            _validationStats = new NetworkDataValidationStats();
        }

        /// <summary>
        /// Serializes an object to a JSON string with validation.
        /// </summary>
        /// <typeparam name="T">The type of the object to serialize.</typeparam>
        /// <param name="obj">The object to serialize.</param>
        /// <returns>A <see cref="SerializationResult{String}"/> containing the JSON string or error information.</returns>
        public SerializationResult<string> SerializeToJson<T>(T obj) where T : class
        {
            try
            {
                // Validate input object
                if (obj == null)
                {
                    RecordFailure(NetworkDataValidationError.NullData);
                    return SerializationResult<string>.NullInput();
                }

                // Perform serialization
                var result = _innerSerializer.SerializeToJson(obj);

                if (result.IsSuccess && result.Value != null)
                {
                    // Validate output size
                    if (result.Value.Length > NetworkDataValidator.MaxJsonSize)
                    {
                        RecordFailure(NetworkDataValidationError.OversizedPayload);
                        UsdpLogger.Log("SecureSerializer.OversizedJsonOutput", new
                        {
                            Type = typeof(T).Name,
                            OutputSize = result.Value.Length,
                            MaxAllowed = NetworkDataValidator.MaxJsonSize,
                            Severity = "Warning"
                        });

                        return SerializationResult<string>.UnknownError(
                            $"Serialized JSON size {result.Value.Length} exceeds maximum allowed size {NetworkDataValidator.MaxJsonSize}");
                    }

                    RecordSuccess();
                }
                else
                {
                    RecordFailure(NetworkDataValidationError.InvalidFormat);
                }

                return result;
            }
            catch (Exception ex)
            {
                RecordFailure(NetworkDataValidationError.InvalidFormat);
                UsdpLogger.Log("SecureSerializer.JsonSerializationError", new
                {
                    Type = typeof(T).Name,
                    Error = ex.Message,
                    Severity = "Error"
                });
                return SerializationResult<string>.UnknownError(ex.Message, ex);
            }
        }

        /// <summary>
        /// Deserializes an object from a JSON string with comprehensive validation.
        /// </summary>
        /// <typeparam name="T">The type to deserialize to.</typeparam>
        /// <param name="json">The JSON string to deserialize.</param>
        /// <returns>A <see cref="SerializationResult{T}"/> containing the deserialized object or error information.</returns>
        public SerializationResult<T> DeserializeFromJson<T>(string json) where T : class
        {
            try
            {
                // Validate input JSON
                var validation = ValidateJsonInput(json);
                if (!validation.IsValid)
                {
                    RecordFailure(validation.ErrorType ?? NetworkDataValidationError.InvalidFormat);
                    return SerializationResult<T>.InvalidFormat(validation.ErrorMessage ?? "JSON validation failed");
                }

                // Perform deserialization
                var result = _innerSerializer.DeserializeFromJson<T>(json);

                if (result.IsSuccess && result.Value != null)
                {
                    // Additional validation for specific types
                    if (typeof(T) == typeof(ServiceAdvertisement) && result.Value is ServiceAdvertisement advertisement)
                    {
                        var advValidation = NetworkDataValidator.ValidateServiceAdvertisement(
                            advertisement, "localhost", 0);

                        if (!advValidation.IsValid)
                        {
                            RecordFailure(NetworkDataValidationError.SecurityValidationFailure);
                            return SerializationResult<T>.InvalidFormat(
                                advValidation.ErrorMessage ?? "Service advertisement validation failed");
                        }

                        // Return sanitized version
                        RecordSuccess();
                        return SerializationResult<T>.Success((T)(object)advValidation.SanitizedAdvertisement!);
                    }

                    RecordSuccess();
                }
                else
                {
                    RecordFailure(NetworkDataValidationError.InvalidFormat);
                }

                return result;
            }
            catch (Exception ex)
            {
                RecordFailure(NetworkDataValidationError.InvalidFormat);
                UsdpLogger.Log("SecureSerializer.JsonDeserializationError", new
                {
                    Type = typeof(T).Name,
                    Error = ex.Message,
                    JsonLength = json?.Length ?? 0,
                    Severity = "Error"
                });
                return SerializationResult<T>.UnknownError(ex.Message, ex);
            }
        }

        /// <summary>
        /// Serializes an object to CBOR binary format with validation.
        /// </summary>
        /// <typeparam name="T">The type of the object to serialize.</typeparam>
        /// <param name="obj">The object to serialize.</param>
        /// <returns>A <see cref="SerializationResult{Byte[]}"/> containing the CBOR data or error information.</returns>
        public SerializationResult<byte[]> SerializeToCbor<T>(T obj) where T : class
        {
            try
            {
                // Validate input object
                if (obj == null)
                {
                    RecordFailure(NetworkDataValidationError.NullData);
                    return SerializationResult<byte[]>.NullInput();
                }

                // Perform serialization
                var result = _innerSerializer.SerializeToCbor(obj);

                if (result.IsSuccess && result.Value != null)
                {
                    // Validate output size
                    if (result.Value.Length > NetworkDataValidator.MaxCborSize)
                    {
                        RecordFailure(NetworkDataValidationError.OversizedPayload);
                        UsdpLogger.Log("SecureSerializer.OversizedCborOutput", new
                        {
                            Type = typeof(T).Name,
                            OutputSize = result.Value.Length,
                            MaxAllowed = NetworkDataValidator.MaxCborSize,
                            Severity = "Warning"
                        });

                        return SerializationResult<byte[]>.UnknownError(
                            $"Serialized CBOR size {result.Value.Length} exceeds maximum allowed size {NetworkDataValidator.MaxCborSize}");
                    }

                    RecordSuccess();
                }
                else
                {
                    RecordFailure(NetworkDataValidationError.InvalidFormat);
                }

                return result;
            }
            catch (Exception ex)
            {
                RecordFailure(NetworkDataValidationError.InvalidFormat);
                UsdpLogger.Log("SecureSerializer.CborSerializationError", new
                {
                    Type = typeof(T).Name,
                    Error = ex.Message,
                    Severity = "Error"
                });
                return SerializationResult<byte[]>.UnknownError(ex.Message, ex);
            }
        }

        /// <summary>
        /// Deserializes an object from CBOR binary data with comprehensive validation.
        /// </summary>
        /// <typeparam name="T">The type to deserialize to.</typeparam>
        /// <param name="cbor">The CBOR binary data to deserialize.</param>
        /// <returns>A <see cref="SerializationResult{T}"/> containing the deserialized object or error information.</returns>
        public SerializationResult<T> DeserializeFromCbor<T>(byte[] cbor) where T : class
        {
            try
            {
                // Validate input CBOR data
                var validation = NetworkDataValidator.ValidateIncomingData(cbor, "localhost", 0);
                if (!validation.IsValid)
                {
                    RecordFailure(validation.ErrorType ?? NetworkDataValidationError.InvalidFormat);
                    return SerializationResult<T>.InvalidFormat(validation.ErrorMessage ?? "CBOR validation failed");
                }

                // Perform deserialization
                var result = _innerSerializer.DeserializeFromCbor<T>(cbor);

                if (result.IsSuccess && result.Value != null)
                {
                    // Additional validation for specific types
                    if (typeof(T) == typeof(ServiceAdvertisement) && result.Value is ServiceAdvertisement advertisement)
                    {
                        var advValidation = NetworkDataValidator.ValidateServiceAdvertisement(
                            advertisement, "localhost", 0);

                        if (!advValidation.IsValid)
                        {
                            RecordFailure(NetworkDataValidationError.SecurityValidationFailure);
                            return SerializationResult<T>.InvalidFormat(
                                advValidation.ErrorMessage ?? "Service advertisement validation failed");
                        }

                        // Return sanitized version
                        RecordSuccess();
                        return SerializationResult<T>.Success((T)(object)advValidation.SanitizedAdvertisement!);
                    }

                    RecordSuccess();
                }
                else
                {
                    RecordFailure(NetworkDataValidationError.InvalidFormat);
                }

                return result;
            }
            catch (Exception ex)
            {
                RecordFailure(NetworkDataValidationError.InvalidFormat);
                UsdpLogger.Log("SecureSerializer.CborDeserializationError", new
                {
                    Type = typeof(T).Name,
                    Error = ex.Message,
                    DataLength = cbor?.Length ?? 0,
                    Severity = "Error"
                });
                return SerializationResult<T>.UnknownError(ex.Message, ex);
            }
        }

        /// <summary>
        /// Validates JSON input for security issues.
        /// </summary>
        private static NetworkDataValidationResult ValidateJsonInput(string json)
        {
            if (string.IsNullOrEmpty(json))
            {
                return NetworkDataValidationResult.Failure(
                    NetworkDataValidationError.EmptyData,
                    "JSON input is null or empty",
                    "localhost", 0);
            }

            if (json.Length > NetworkDataValidator.MaxJsonSize)
            {
                return NetworkDataValidationResult.Failure(
                    NetworkDataValidationError.OversizedPayload,
                    $"JSON size {json.Length} exceeds maximum allowed size {NetworkDataValidator.MaxJsonSize}",
                    "localhost", 0);
            }

            // Check for excessive nesting by counting braces
            int openBraces = 0;
            int maxNesting = 0;
            foreach (char c in json)
            {
                if (c == '{' || c == '[')
                {
                    openBraces++;
                    maxNesting = Math.Max(maxNesting, openBraces);
                }
                else if (c == '}' || c == ']')
                {
                    openBraces--;
                }
            }

            if (maxNesting > NetworkDataValidator.MaxNestingDepth)
            {
                return NetworkDataValidationResult.Failure(
                    NetworkDataValidationError.ExcessiveNesting,
                    $"JSON nesting depth {maxNesting} exceeds maximum allowed depth {NetworkDataValidator.MaxNestingDepth}",
                    "localhost", 0);
            }

            return NetworkDataValidationResult.Success("localhost", 0);
        }

        /// <summary>
        /// Records a successful validation.
        /// </summary>
        private void RecordSuccess()
        {
            lock (_statsLock)
            {
                _validationStats.RecordSuccess();
            }
        }

        /// <summary>
        /// Records a failed validation.
        /// </summary>
        private void RecordFailure(NetworkDataValidationError errorType)
        {
            lock (_statsLock)
            {
                _validationStats.RecordFailure(errorType);
            }
        }

        /// <summary>
        /// Resets validation statistics.
        /// </summary>
        public void ResetValidationStats()
        {
            lock (_statsLock)
            {
                _validationStats.Reset();
            }
        }

        /// <summary>
        /// Serializes an object to a JSON string using the legacy approach (for backward compatibility).
        /// </summary>
        /// <typeparam name="T">The type of the object to serialize.</typeparam>
        /// <param name="obj">The object to serialize.</param>
        /// <returns>A JSON string representation of the object, or an empty string if serialization fails.</returns>
        public string SerializeToJsonLegacy<T>(T obj) where T : class
        {
            var result = SerializeToJson(obj);
            return result.IsSuccess ? result.Value ?? string.Empty : string.Empty;
        }

        /// <summary>
        /// Deserializes an object from a JSON string using the legacy approach (for backward compatibility).
        /// </summary>
        /// <typeparam name="T">The type to deserialize to.</typeparam>
        /// <param name="json">The JSON string to deserialize.</param>
        /// <returns>The deserialized object, or null if deserialization fails.</returns>
        public T? DeserializeFromJsonLegacy<T>(string json) where T : class
        {
            var result = DeserializeFromJson<T>(json);
            return result.IsSuccess ? result.Value : null;
        }

        /// <summary>
        /// Serializes an object to CBOR binary format using the legacy approach (for backward compatibility).
        /// </summary>
        /// <typeparam name="T">The type of the object to serialize.</typeparam>
        /// <param name="obj">The object to serialize.</param>
        /// <returns>A byte array containing the CBOR representation of the object, or an empty array if serialization fails.</returns>
        public byte[] SerializeToCborLegacy<T>(T obj) where T : class
        {
            var result = SerializeToCbor(obj);
            return result.IsSuccess ? result.Value ?? Array.Empty<byte>() : Array.Empty<byte>();
        }

        /// <summary>
        /// Deserializes an object from CBOR binary data using the legacy approach (for backward compatibility).
        /// </summary>
        /// <typeparam name="T">The type to deserialize to.</typeparam>
        /// <param name="cbor">The CBOR binary data to deserialize.</param>
        /// <returns>The deserialized object, or null if deserialization fails.</returns>
        public T? DeserializeFromCborLegacy<T>(byte[] cbor) where T : class
        {
            var result = DeserializeFromCbor<T>(cbor);
            return result.IsSuccess ? result.Value : null;
        }
    }
}
