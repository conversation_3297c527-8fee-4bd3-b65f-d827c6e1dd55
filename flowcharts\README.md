# USDP2 UDP Security Flowcharts

This folder contains Mermaid diagrams illustrating the communication flows and architecture of the USDP2 library with UDP security implementation.

## Diagrams

### 1. [System Architecture Overview](system-architecture-overview.md)
High-level view of three USDP2 instances communicating over a secured multicast network.

**Key Features:**
- Component relationships within each instance
- Multicast network communication patterns
- Security layer integration

### 2. [Service Advertisement Flow](service-advertisement-flow.md)
Detailed sequence diagram showing how service advertisements flow through a secured network.

**Key Features:**
- Complete message lifecycle from creation to consumption
- Security processing steps (timestamp + HMAC)
- Multi-instance reception and verification

### 3. [Service Query with Mixed Security](service-query-mixed-security.md)
Demonstrates communication behavior in mixed security environments.

**Key Features:**
- Secured vs unsecured instance interactions
- Security isolation and boundaries
- Graceful degradation patterns

### 4. [Message Structure with Security](message-structure-security.md)
Shows the transformation of messages through the security pipeline.

**Key Features:**
- Message size evolution (200 → 240 bytes)
- Security component breakdown
- Verification process flow

## Viewing the Diagrams

These diagrams are written in Mermaid syntax and can be viewed using:

1. **GitHub**: Automatically renders Mermaid diagrams in markdown files
2. **Mermaid Live Editor**: https://mermaid.live/
3. **VS Code**: With Mermaid preview extensions
4. **Documentation sites**: GitBook, Notion, etc. with Mermaid support

## Security Implementation Summary

The UDP security implementation provides:

- **Message Authentication**: HMAC-SHA256 verification
- **Replay Protection**: Timestamp-based validation (5-minute window)
- **Graceful Degradation**: Fallback to unsecured communication
- **Minimal Overhead**: ~20% message size increase
- **Removable Design**: Can be completely removed if not needed

## Communication Scenarios

1. **Fully Secured**: All instances have `EnableUdpSecurity = true`
2. **Mixed Security**: Some instances secured, others not
3. **Security Failure**: Graceful fallback to unsecured communication

Each scenario is illustrated in the respective flowcharts with detailed explanations of the behavior and security implications.
