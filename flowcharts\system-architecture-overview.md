# USDP2 System Architecture Overview

```mermaid
graph TB
    subgraph "Instance A (192.168.1.100)"
        A_LD[LocalDirectory A]
        A_US[UdpNetworkSender]
        A_UR[UdpNetworkReceiver]
        A_SEC[UdpSecurityOverride A]
        A_CACHE[Service Cache A]
        A_BF[BloomFilter A]
    end
    
    subgraph "Instance B (192.168.1.101)"
        B_LD[LocalDirectory B]
        B_US[UdpNetworkSender]
        B_UR[UdpNetworkReceiver]
        B_SEC[UdpSecurityOverride B]
        B_CACHE[Service Cache B]
        B_BF[BloomFilter B]
    end
    
    subgraph "Instance C (192.168.1.102)"
        C_LD[LocalDirectory C]
        C_US[UdpNetworkSender]
        C_UR[UdpNetworkReceiver]
        C_SEC[UdpSecurityOverride C]
        C_CACHE[Service Cache C]
        C_BF[BloomFilter C]
    end
    
    subgraph "Multicast Network (***************:5353)"
        MC[Multicast Channel]
    end
    
    A_US -.->|Secured Messages| MC
    B_US -.->|Secured Messages| MC
    C_US -.->|Secured Messages| MC
    
    MC -.->|Secured Messages| A_UR
    MC -.->|Secured Messages| B_UR
    MC -.->|Secured Messages| C_UR
```

## Description

This architecture diagram shows the high-level structure of three USDP2 instances communicating over a multicast network with UDP security enabled.

### Components per Instance:

- **LocalDirectory**: Main service discovery coordinator
- **UdpNetworkSender**: Handles outgoing UDP messages with optional security
- **UdpNetworkReceiver**: Processes incoming UDP messages with optional security verification
- **UdpSecurityOverride**: Provides message authentication and integrity protection
- **Service Cache**: Local storage for discovered services
- **BloomFilter**: Efficient service filtering and lookup

### Network Communication:

- **Multicast Channel**: ***************:5353 (default USDP2 multicast address)
- **Secured Messages**: All communication protected with HMAC-SHA256 authentication
- **Bidirectional Flow**: Each instance can both send and receive secured messages

### Security Features:

- **Message Authentication**: HMAC-SHA256 ensures message integrity
- **Replay Protection**: Timestamp-based validation prevents replay attacks
- **Key Management**: Integrated with existing KeyManagementProvider infrastructure
- **Graceful Degradation**: System continues working if security fails

This architecture enables secure, distributed service discovery across multiple USDP2 instances while maintaining the efficiency of UDP multicast communication.
