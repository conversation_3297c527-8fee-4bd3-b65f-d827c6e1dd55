using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Cryptography.X509Certificates;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using USDP2;

namespace USDP2
{
    /// <summary>
    /// The example implementation.
    /// </summary>
    public static class ExampleImplementation
    {
        /// <summary>
        /// Run local directory asynchronously.
        /// </summary>
        /// <param name="multicastAddress">The multicast address.</param>
        /// <param name="multicastPort">The multicast port.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>A <see cref="Task"/></returns>
        public static async Task RunLocalDirectoryAsync(
            string multicastAddress,
            int multicastPort,
            CancellationToken cancellationToken = default)
        {
            // --- Select your backend and load a key ---
            // Example: Windows DPAPI
            // byte[] key = await KeyManagementProvider.LoadKeyAsync(
            //     KeyManagementBackend.WindowsDPAPI, @"C:\keys\ed25519.key.protected");

            // Example: Azure Key Vault
            // byte[] key = await KeyManagementProvider.LoadKeyAsync(
            //     KeyManagementBackend.AzureKeyVault, "Ed25519PrivateKey", vaultUri: "https://<your-keyvault-name>.vault.azure.net/");

            // Example: HashiCorp Vault
            // byte[] key = await KeyManagementProvider.LoadKeyAsync(
            //     KeyManagementBackend.HashiCorpVault, "http://localhost:8200/v1/secret/data/ed25519", hashicorpToken: "<your-token>");

            // Example: Google Cloud Secret Manager
            // byte[] key = await KeyManagementProvider.LoadKeyAsync(
            //     KeyManagementBackend.GoogleCloudSecretManager, "ed25519", gcpProjectId: "<your-gcp-project-id>");

            // Use ConfigurationProvider to load secrets securely
            var configPath = UsdpConfiguration.Instance.DefaultConfigPath;
            var configProvider = new ConfigurationProvider(configPath, KeyManagementBackend.WindowsDPAPI);

            try
            {
                await configProvider.LoadAsync();
            }
            catch (FileNotFoundException)
            {
                // Create default configuration if it doesn't exist
                configProvider.SetValue("PskEnabled", true);
                configProvider.SetValue("CertificateEnabled", false);

                // Generate a secure random PSK
                var keyRotationManager = new KeyRotationManager(configProvider);
                await keyRotationManager.RotateKeyAsync("AuthPsk", KeyType.PSK);
                await configProvider.SaveAsync();
            }

            // Get PSK from configuration
            string psk = configProvider.GetValue<string>("Keys:AuthPsk");
            var authProvider = new PskAuthenticationProvider(psk);

            // 2. Create UDP multicast sender/receiver with input validation
            InputValidator.ValidateIpAddress(multicastAddress, nameof(multicastAddress));
            InputValidator.ValidatePort(multicastPort, nameof(multicastPort));

            var sender = new UdpNetworkSender();
            var receiver = new UdpNetworkReceiver(multicastPort, isMulticast: true, multicastAddress);

            // 3. Create the Local Directory
            var localDirectory = new LocalDirectory(sender, receiver, multicastAddress, multicastPort);

            // 4. Start listening for multicast advertisements and queries
            _ = localDirectory.StartAsync(cancellationToken);

            // 5. Announce a sample service (with error handling)
            var endpoint = new TransportEndpoint
            {
                Protocol = UsdpConfiguration.Instance.DefaultProtocol,
                Address = UsdpConfiguration.Instance.DefaultServiceAddress,
                Port = UsdpConfiguration.Instance.DefaultServicePort,
                Security = UsdpConfiguration.Instance.DefaultSecurity
            };

            try
            {
                var serviceId = new ServiceIdentifier(
                    UsdpConfiguration.Instance.DefaultServiceType,
                    UsdpConfiguration.Instance.DefaultServiceInstance);
                var advertisement = new ServiceAdvertisement(serviceId, endpoint)
                {
                    ServiceId = serviceId,
                    Endpoint = endpoint,
                    Metadata = new Dictionary<string, object>
                    {
                        { "type", UsdpConfiguration.Instance.DefaultMetadataType },
                        { "location", UsdpConfiguration.Instance.DefaultMetadataLocation }
                    },
                    Ttl = UsdpConfiguration.Instance.DefaultTtl
                };

                // Authenticate before announcing (example)
                if (await authProvider.AuthenticateAsync(psk))
                {
                    await localDirectory.AnnounceServiceAsync(advertisement, cancellationToken);
                    Console.WriteLine("Service announced.");
                }
                else
                {
                    Console.WriteLine("Authentication failed. Service not announced.");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error announcing service: {ex.Message}");
            }

            // 6. Send a sample query and handle responses asynchronously
            var query = new ServiceQuery
            {
                SidFilter = null,
                MetadataFilter = new Dictionary<string, string> { { "type", "lighting" } }
            };
            var queryData = query.ToCbor();

            // Listen for responses in the background
            var responseCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
            var responseTask = Task.Run(async () =>
            {
                try
                {
                    var responseReceiver = new UdpNetworkReceiver(
                        multicastPort + UsdpConfiguration.Instance.ResponsePortOffset, false, null); // Use a different port for responses
                    await responseReceiver.StartReceivingAsync(async (data, remoteAddr, remotePort) =>
                    {
                        var adv = ServiceAdvertisement.FromCbor(data);
                        if (adv != null)
                        {
                            Console.WriteLine($"Received response: {adv.ServiceId.FullName} @ {adv.Endpoint.Address}:{adv.Endpoint.Port} ({string.Join(", ", adv.Metadata)})");
                        }
                        await Task.Yield();
                    }, responseCts.Token);
                }
                catch (OperationCanceledException)
                {
                    // Expected on cancellation
                }
            }, responseCts.Token);

            // Send the query
            try
            {
                await sender.SendAsync(queryData, multicastAddress, multicastPort, cancellationToken);
                Console.WriteLine("Query sent.");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error sending query: {ex.Message}");
            }

            // Wait for responses for a short period
            try
            {
                await Task.Delay(UsdpConfiguration.Instance.QueryResponseTimeout, cancellationToken);
            }
            catch (OperationCanceledException) { }

            // Stop response listener
            await responseCts.CancelAsync();
            try { await responseTask; } catch { }

            // 7. Access and print cached advertisements
            foreach (var adv in localDirectory.GetCachedAdvertisements())
            {
                Console.WriteLine($"Cached: {adv.ServiceId.FullName} @ {adv.Endpoint.Address}:{adv.Endpoint.Port} ({string.Join(", ", adv.Metadata)})");
            }

            // 8. Transport negotiation and fallback example
            var endpoints = new List<TransportEndpoint> { endpoint /*, ... more endpoints */ };
            var preferredProtocols = new[] { "coap+udp", "https", "http" };
            var selectedEndpoint = TransportNegotiator.Negotiate(endpoints, preferredProtocols);

            // 9. Protocol translation example (if needed)
            var translator = new CoapToHttpTranslator(new HttpClient());
            await translator.TranslateAsync(queryData, "coap+udp", "http", "cloud.example.com", 80, cancellationToken);

            // 10. NAT traversal example
            var publicEp = await NatTraversalHelper.GetPublicEndPointAsync("stun.l.google.com", 19302);
            if (publicEp != null)
                Console.WriteLine($"NAT public endpoint: {publicEp.Address}:{publicEp.Port}");

            // --- Further extension points ---
            // - Integrate with a global directory (GD) for cross-network discovery.
            // - Add Ed25519 signing/verification for advertisements and queries.
            // - Implement advanced query/response logic (e.g., filtering, TTL, etc.)

            // Initialize the new Microsoft.Extensions.Logging-based logging system
            InitializeLogging();

            // Configure legacy logging to a file for backward compatibility
            Diagnostics.LogFilePath = "LOGFILE";
        }

        /// <summary>
        /// Initializes the Microsoft.Extensions.Logging-based logging system with appropriate providers.
        ///
        /// This method demonstrates how to set up the new UsdpLogger system with various logging providers
        /// based on the configuration settings. It shows best practices for production logging setup.
        /// </summary>
        private static void InitializeLogging()
        {
            var config = UsdpConfiguration.Instance;

            // Create a logger factory with providers based on configuration
            var loggerFactory = LoggerFactory.Create(builder =>
            {
                // Set minimum log level from configuration
                builder.SetMinimumLevel(config.MinimumLogLevel);

                // Add console provider if console logging is enabled
                if (config.LogMode == UsdpConfiguration.LoggingMode.Console ||
                    config.LogMode == UsdpConfiguration.LoggingMode.Both)
                {
                    builder.AddConsole(options =>
                    {
                        options.FormatterName = "simple";
                    })
                    .AddConsoleFormatter<Microsoft.Extensions.Logging.Console.SimpleConsoleFormatter, Microsoft.Extensions.Logging.Console.SimpleConsoleFormatterOptions>(options =>
                    {
                        options.IncludeScopes = true;
                        options.TimestampFormat = "yyyy-MM-dd HH:mm:ss.fff ";
                        options.UseUtcTimestamp = true;
                    });
                }

                // Add debug provider for development
                builder.AddDebug();

                // Add file logging if file mode is enabled
                // Note: For production, consider using Serilog, NLog, or other file providers
                if (config.LogMode == UsdpConfiguration.LoggingMode.File ||
                    config.LogMode == UsdpConfiguration.LoggingMode.Both)
                {
                    // For this example, we'll use debug output
                    // In production, add a proper file logging provider
                    builder.AddDebug();
                }

                // Add filters for specific categories if needed
                builder.AddFilter("USDP2.Network", LogLevel.Information);
                builder.AddFilter("USDP2.Tls", LogLevel.Information);
                builder.AddFilter("USDP2.BloomFilter", LogLevel.Warning); // Reduce bloom filter verbosity
            });

            // Initialize UsdpLogger with the configured factory
            UsdpLogger.Initialize(loggerFactory);

            // Configure UsdpLogger settings from configuration
            UsdpLogger.EnableMetrics = config.EnableLoggingMetrics;
            UsdpLogger.MinimumLogLevel = config.MinimumLogLevel;
            UsdpLogger.EnableConsoleFallback = true; // Enable console fallback for example code

            // Log initialization success
            UsdpLogger.Log("LoggingSystemInitialized", new
            {
                MinimumLogLevel = config.MinimumLogLevel.ToString(),
                LogMode = config.LogMode.ToString(),
                MetricsEnabled = config.EnableLoggingMetrics,
                StructuredLogging = config.EnableStructuredLogging
            });
        }
    }

    /// <summary>
    /// The transport negotiator.
    /// </summary>
    public static class TransportNegotiator
    {
        // Selects the optimal endpoint based on protocol preference and availability
        /// <summary>
        /// Negotiates the optimal transport endpoint from a collection of available endpoints based on protocol preferences.
        /// The method attempts to find the first endpoint that matches a protocol from the preferred protocols list,
        /// in the order of preference. If no match is found, it falls back to the first available endpoint.
        /// </summary>
        /// <param name="endpoints">A collection of available transport endpoints to choose from.</param>
        /// <param name="preferredProtocols">An ordered collection of protocol names, with the most preferred protocol first.</param>
        /// <returns>A <see cref="TransportEndpoint"/> that best matches the preferred protocols, or the first available endpoint if no match is found.</returns>
        /// <exception cref="System.InvalidOperationException">Thrown when the endpoints collection is empty.</exception>
        public static TransportEndpoint Negotiate(IEnumerable<TransportEndpoint> endpoints, IEnumerable<string> preferredProtocols)
        {
            foreach (var protocol in preferredProtocols)
            {
                var match = endpoints.FirstOrDefault(e => e.Protocol.Equals(protocol, StringComparison.OrdinalIgnoreCase));
                if (match != null)
                    return match;
            }
            // Fallback: return the first available endpoint
            return endpoints.First();
        }
    }

    /// <summary>
    /// The dictionary extensions.
    /// </summary>
    public static class DictionaryExtensions
    {
        /// <summary>
        /// Converts to object dictionary.
        /// </summary>
        /// <param name="stringDict">The string dict.</param>
        /// <returns>A dictionary with a key of type string and a value of type object.</returns>
        public static Dictionary<string, object> ToObjectDictionary(this Dictionary<string, string> stringDict)
        {
            return stringDict.ToDictionary(kvp => kvp.Key, kvp => (object)kvp.Value);
        }
    }

    /// <summary>
    /// The example usage demonstrating secure authentication methods.
    /// </summary>
    public class ExampleUsage
    {
        /// <summary>
        /// Demonstrates various authentication methods with secure practices.
        /// </summary>
        /// <returns>A task representing the asynchronous operation.</returns>
        public static async Task ExecuteAsync()
        {
            // Load configuration securely
            var configPath = UsdpConfiguration.Instance.DefaultConfigPath;
            var configProvider = new ConfigurationProvider(configPath, KeyManagementBackend.WindowsDPAPI);

            try
            {
                await configProvider.LoadAsync();
            }
            catch (FileNotFoundException)
            {
                // Initialize configuration with defaults
                await InitializeConfigurationAsync(configProvider);
            }

            // Example 1: PSK Authentication
            if (configProvider.GetValue<bool>("PskEnabled"))
            {
                string psk = await configProvider.GetSecretAsync("AuthPsk");
                var pskProvider = new PskAuthenticationProvider(psk);

                // Validate client-provided PSK
                string clientPsk = "client-provided-psk"; // In real code, this would come from the client
                bool isPskValid = await pskProvider.AuthenticateAsync(clientPsk);

                Console.WriteLine($"PSK Authentication: {(isPskValid ? "Success" : "Failed")}");
            }

            // Example 2: Certificate Authentication
            if (configProvider.GetValue<bool>("CertificateEnabled"))
            {
                // Load trusted certificates
                var trustedCertificates = new X509Certificate2Collection();
                string certPath = configProvider.GetValue<string>("CertificatePath");

                if (!string.IsNullOrEmpty(certPath) && File.Exists(certPath))
                {
                    trustedCertificates.Add(new X509Certificate2(certPath));
                }

                var certProvider = new CertificateAuthenticationProvider(
                    trustedCertificates,
                    checkRevocation: true,
                    validateChain: true
                );

                // Validate client-provided certificate
                string clientCertBase64 = "client-certificate-base64"; // In real code, this would come from the client
                bool isCertValid = await certProvider.AuthenticateAsync(clientCertBase64);

                Console.WriteLine($"Certificate Authentication: {(isCertValid ? "Success" : "Failed")}");
            }

            // Check if keys need rotation
            await CheckAndRotateKeysAsync(configProvider);
        }

        /// <summary>
        /// Initializes the configuration with secure defaults.
        /// </summary>
        /// <param name="configProvider">The configuration provider.</param>
        /// <returns>A task representing the asynchronous operation.</returns>
        private static async Task InitializeConfigurationAsync(ConfigurationProvider configProvider)
        {
            // Set authentication methods
            configProvider.SetValue("PskEnabled", true);
            configProvider.SetValue("CertificateEnabled", false);

            // Set certificate configuration (if needed)
            configProvider.SetValue("CertificatePath", Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "trusted_certs"));

            // Set key rotation configuration
            configProvider.SetValue("KeyRotation:Enabled", true);
            configProvider.SetValue("KeyRotation:PeriodDays", 90);

            // Generate initial keys
            var keyRotationManager = new KeyRotationManager(configProvider);
            await keyRotationManager.RotateKeyAsync("AuthPsk", KeyType.PSK);

            // Save configuration
            await configProvider.SaveAsync();
        }

        /// <summary>
        /// Checks if keys need rotation and rotates them if necessary.
        /// </summary>
        /// <param name="configProvider">The configuration provider.</param>
        /// <returns>A task representing the asynchronous operation.</returns>
        private static async Task CheckAndRotateKeysAsync(ConfigurationProvider configProvider)
        {
            if (!configProvider.GetValue<bool>("KeyRotation:Enabled"))
            {
                return;
            }

            int rotationPeriodDays = configProvider.GetValue<int>("KeyRotation:PeriodDays", 90);
            var rotationPeriod = TimeSpan.FromDays(rotationPeriodDays);

            var keyRotationManager = new KeyRotationManager(configProvider, rotationPeriod);

            // Check and rotate PSK if needed
            if (configProvider.GetValue<bool>("PskEnabled") && keyRotationManager.NeedsRotation("AuthPsk"))
            {
                await keyRotationManager.RotateKeyAsync("AuthPsk", KeyType.PSK);
                Console.WriteLine("PSK rotated successfully.");
            }

            // Save updated configuration
            await configProvider.SaveAsync();
        }
    }
}