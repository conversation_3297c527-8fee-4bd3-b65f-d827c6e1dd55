using System;
using System.Text;
using System.Collections.Generic;

namespace USDP2
{
    /// <summary>
    /// Demonstrates the network data validation and sanitization system.
    /// Shows how the validation system protects against various attack vectors
    /// and ensures data integrity.
    /// </summary>
    public static class NetworkValidationDemo
    {
        /// <summary>
        /// Runs a comprehensive demonstration of the network validation system.
        /// </summary>
        public static void RunDemo()
        {
            Console.WriteLine("=".PadRight(70, '='));
            Console.WriteLine("USDP2 Network Data Validation and Sanitization Demo");
            Console.WriteLine("=".PadRight(70, '='));
            Console.WriteLine();

            DemoBasicValidation();
            DemoSecurityProtection();
            DemoDataSanitization();
            DemoConfigurationSettings();
            DemoValidationStatistics();

            Console.WriteLine();
            Console.WriteLine("✅ Network data validation demo completed successfully!");
            Console.WriteLine("The validation system provides comprehensive protection against:");
            Console.WriteLine("  • DoS attacks through oversized payloads");
            Console.WriteLine("  • Injection attacks through malicious content");
            Console.WriteLine("  • Buffer overflow attacks through size limits");
            Console.WriteLine("  • Data integrity issues through sanitization");
            Console.WriteLine("  • Security monitoring through detailed logging");
        }

        /// <summary>
        /// Demonstrates basic network data validation.
        /// </summary>
        private static void DemoBasicValidation()
        {
            Console.WriteLine("📋 Demo 1: Basic Network Data Validation");
            Console.WriteLine("-".PadRight(50, '-'));

            // Test valid data
            var validJson = "{\"name\": \"test-service\", \"port\": 8080}";
            var validData = Encoding.UTF8.GetBytes(validJson);
            
            var result = NetworkDataValidator.ValidateIncomingData(validData, "127.0.0.1", 8080);
            Console.WriteLine($"✅ Valid data validation: {(result.IsValid ? "PASSED" : "FAILED")}");

            // Test null data
            var nullResult = NetworkDataValidator.ValidateIncomingData(null!, "127.0.0.1", 8080);
            Console.WriteLine($"❌ Null data validation: {(!nullResult.IsValid ? "CORRECTLY REJECTED" : "INCORRECTLY ACCEPTED")}");
            Console.WriteLine($"   Error: {nullResult.ErrorMessage}");

            // Test empty data
            var emptyResult = NetworkDataValidator.ValidateIncomingData(new byte[0], "127.0.0.1", 8080);
            Console.WriteLine($"❌ Empty data validation: {(!emptyResult.IsValid ? "CORRECTLY REJECTED" : "INCORRECTLY ACCEPTED")}");
            Console.WriteLine($"   Error: {emptyResult.ErrorMessage}");

            Console.WriteLine();
        }

        /// <summary>
        /// Demonstrates security protection features.
        /// </summary>
        private static void DemoSecurityProtection()
        {
            Console.WriteLine("🛡️  Demo 2: Security Protection Features");
            Console.WriteLine("-".PadRight(50, '-'));

            // Test oversized payload protection
            var originalMaxSize = UsdpConfiguration.Instance.MaxNetworkDataSize;
            UsdpConfiguration.Instance.MaxNetworkDataSize = 1024; // Temporarily reduce for demo

            var oversizedData = new byte[2048];
            Array.Fill(oversizedData, (byte)'A');
            
            var oversizeResult = NetworkDataValidator.ValidateIncomingData(oversizedData, "*************", 8080);
            Console.WriteLine($"🚫 Oversized payload: {(!oversizeResult.IsValid ? "CORRECTLY BLOCKED" : "INCORRECTLY ALLOWED")}");
            Console.WriteLine($"   Error: {oversizeResult.ErrorMessage}");

            // Restore original size
            UsdpConfiguration.Instance.MaxNetworkDataSize = originalMaxSize;

            // Test malicious content detection
            var maliciousJson = "{\"script\": \"<script>alert('xss')</script>\", \"name\": \"test\"}";
            var maliciousData = Encoding.UTF8.GetBytes(maliciousJson);
            
            var maliciousResult = NetworkDataValidator.ValidateIncomingData(maliciousData, "*********", 8080);
            Console.WriteLine($"🚫 Malicious content: {(!maliciousResult.IsValid ? "CORRECTLY BLOCKED" : "INCORRECTLY ALLOWED")}");
            if (!maliciousResult.IsValid)
            {
                Console.WriteLine($"   Error: {maliciousResult.ErrorMessage}");
            }

            // Test suspicious binary patterns
            var suspiciousData = new byte[1000];
            Array.Fill(suspiciousData, (byte)0); // All null bytes
            
            var suspiciousResult = NetworkDataValidator.ValidateIncomingData(suspiciousData, "***********", 8080);
            Console.WriteLine($"🚫 Suspicious binary: {(!suspiciousResult.IsValid ? "CORRECTLY BLOCKED" : "INCORRECTLY ALLOWED")}");
            if (!suspiciousResult.IsValid)
            {
                Console.WriteLine($"   Error: {suspiciousResult.ErrorMessage}");
            }

            Console.WriteLine();
        }

        /// <summary>
        /// Demonstrates data sanitization capabilities.
        /// </summary>
        private static void DemoDataSanitization()
        {
            Console.WriteLine("🧹 Demo 3: Data Sanitization");
            Console.WriteLine("-".PadRight(50, '-'));

            try
            {
                // Create a service advertisement with potentially problematic data
                var serviceId = new ServiceIdentifier("test/namespace", "test-service");
                var endpoint = new TransportEndpoint
                {
                    Protocol = "http",
                    Address = "127.0.0.1",
                    Port = 8080,
                    Security = "none"
                };

                var advertisement = new ServiceAdvertisement(serviceId, endpoint)
                {
                    Metadata = new Dictionary<string, object>
                    {
                        { "type", "test" },
                        { "description", "A test service for demonstration" },
                        { "version", "1.0.0" }
                    }
                };

                // Validate and sanitize the service advertisement
                var validation = NetworkDataValidator.ValidateServiceAdvertisement(
                    advertisement, "127.0.0.1", 8080);

                if (validation.IsValid && validation.SanitizedAdvertisement != null)
                {
                    Console.WriteLine("✅ Service advertisement sanitization: SUCCESS");
                    Console.WriteLine($"   Original service: {advertisement.ServiceId.FullName}");
                    Console.WriteLine($"   Sanitized service: {validation.SanitizedAdvertisement.ServiceId.FullName}");
                    Console.WriteLine($"   Metadata entries: {validation.SanitizedAdvertisement.Metadata.Count}");
                }
                else
                {
                    Console.WriteLine("❌ Service advertisement sanitization: FAILED");
                    Console.WriteLine($"   Error: {validation.ErrorMessage}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Sanitization demo error: {ex.Message}");
            }

            Console.WriteLine();
        }

        /// <summary>
        /// Demonstrates configuration settings for validation.
        /// </summary>
        private static void DemoConfigurationSettings()
        {
            Console.WriteLine("⚙️  Demo 4: Configuration Settings");
            Console.WriteLine("-".PadRight(50, '-'));

            var config = UsdpConfiguration.Instance;

            Console.WriteLine("Current validation configuration:");
            Console.WriteLine($"  Network data validation: {(config.EnableNetworkDataValidation ? "ENABLED" : "DISABLED")}");
            Console.WriteLine($"  Max network data size: {config.MaxNetworkDataSize:N0} bytes");
            Console.WriteLine($"  Max JSON data size: {config.MaxJsonDataSize:N0} bytes");
            Console.WriteLine($"  Max CBOR data size: {config.MaxCborDataSize:N0} bytes");
            Console.WriteLine($"  Max string field length: {config.MaxStringFieldLength:N0} characters");
            Console.WriteLine($"  Max metadata entries: {config.MaxMetadataEntries}");
            Console.WriteLine($"  Max nesting depth: {config.MaxNestingDepth}");
            Console.WriteLine($"  Use secure serializer: {(config.UseSecureSerializer ? "YES" : "NO")}");
            Console.WriteLine($"  Log validation failures: {(config.LogValidationFailures ? "YES" : "NO")}");
            Console.WriteLine($"  Reject suspicious patterns: {(config.RejectSuspiciousBinaryPatterns ? "YES" : "NO")}");

            Console.WriteLine();
            Console.WriteLine("Configuration recommendations by environment:");
            Console.WriteLine("  Development: Enable validation with moderate limits");
            Console.WriteLine("  Production: Enable all security features with strict limits");
            Console.WriteLine("  IoT/Constrained: Reduce limits to conserve memory");
            Console.WriteLine("  High-throughput: Balance security with performance needs");

            Console.WriteLine();
        }

        /// <summary>
        /// Demonstrates validation statistics tracking.
        /// </summary>
        private static void DemoValidationStatistics()
        {
            Console.WriteLine("📊 Demo 5: Validation Statistics");
            Console.WriteLine("-".PadRight(50, '-'));

            try
            {
                var serializer = new SecureUSDPSerializer();

                // Perform some operations to generate statistics
                var serviceId = new ServiceIdentifier("demo/service", "stats-test");
                var endpoint = new TransportEndpoint
                {
                    Protocol = "http",
                    Address = "127.0.0.1",
                    Port = 8080,
                    Security = "none"
                };
                var advertisement = new ServiceAdvertisement(serviceId, endpoint);

                // Successful operations
                serializer.SerializeToJson(advertisement);
                serializer.SerializeToCbor(advertisement);

                // Failed operation (invalid JSON)
                serializer.DeserializeFromJson<ServiceAdvertisement>("{invalid json}");

                var stats = serializer.ValidationStats;

                Console.WriteLine("Validation statistics:");
                Console.WriteLine($"  Total validations: {stats.TotalValidations}");
                Console.WriteLine($"  Successful validations: {stats.SuccessfulValidations}");
                Console.WriteLine($"  Failed validations: {stats.FailedValidations}");
                Console.WriteLine($"  Success rate: {stats.SuccessRate:F1}%");
                Console.WriteLine($"  Failure rate: {stats.FailureRate:F1}%");
                Console.WriteLine($"  Oversized payload rejections: {stats.OversizedPayloadRejections}");
                Console.WriteLine($"  Malicious content detections: {stats.MaliciousContentDetections}");
                Console.WriteLine($"  Suspicious content detections: {stats.SuspiciousContentDetections}");
                Console.WriteLine($"  Last validation: {stats.LastValidation:yyyy-MM-dd HH:mm:ss} UTC");

                Console.WriteLine();
                Console.WriteLine("Statistics usage:");
                Console.WriteLine("  • Monitor success/failure rates for system health");
                Console.WriteLine("  • Track security events for threat detection");
                Console.WriteLine("  • Analyze patterns for performance optimization");
                Console.WriteLine("  • Generate alerts for unusual activity");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Statistics demo error: {ex.Message}");
            }

            Console.WriteLine();
        }

        /// <summary>
        /// Entry point for running the network validation demo.
        /// </summary>
        public static void Main(string[] args)
        {
            if (args.Length > 0 && args[0] == "--demo-network-validation")
            {
                RunDemo();
                return;
            }

            Console.WriteLine("USDP2 Network Data Validation System");
            Console.WriteLine("====================================");
            Console.WriteLine();
            Console.WriteLine("Use --demo-network-validation to run the comprehensive demo");
            Console.WriteLine();
            Console.WriteLine("The network data validation system provides:");
            Console.WriteLine("  ✓ Protection against DoS attacks");
            Console.WriteLine("  ✓ Malicious content detection and blocking");
            Console.WriteLine("  ✓ Data sanitization and normalization");
            Console.WriteLine("  ✓ Comprehensive security logging");
            Console.WriteLine("  ✓ Configurable validation behavior");
            Console.WriteLine("  ✓ Performance monitoring and statistics");
        }
    }
}
