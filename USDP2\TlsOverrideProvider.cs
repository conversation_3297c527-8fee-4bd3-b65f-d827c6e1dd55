using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Security;
using System.Security.Authentication;
using System.Security.Cryptography.X509Certificates;

namespace USDP2
{
    /// <summary>
    /// Provides manual TLS/SSL configuration override capabilities.
    /// 
    /// ⚠️  REMOVABLE CLASS NOTICE ⚠️
    /// This class is designed to be easily removable from the codebase if manual
    /// TLS override functionality is not needed. It is completely isolated from
    /// core USDP2 functionality and only activated when EnableManualTlsOverride
    /// is set to true in the configuration.
    /// 
    /// To remove this functionality:
    /// 1. Delete this file (TlsOverrideProvider.cs)
    /// 2. Remove references to TlsOverrideProvider in other classes
    /// 3. Set EnableManualTlsOverride to false in UsdpConfiguration.cs
    /// 
    /// This class provides advanced TLS configuration options for scenarios where
    /// OS-managed TLS is insufficient, such as compliance requirements, testing,
    /// or integration with systems requiring specific TLS settings.
    /// </summary>
    public class TlsOverrideProvider
    {
        private readonly TlsOverrideConfiguration _overrideConfig;
        private readonly UsdpConfiguration _config;

        /// <summary>
        /// Initializes a new instance of the TlsOverrideProvider class.
        /// </summary>
        /// <param name="overrideConfig">The manual TLS override configuration.</param>
        /// <param name="config">The USDP configuration instance. If null, uses UsdpConfiguration.Instance.</param>
        public TlsOverrideProvider(TlsOverrideConfiguration overrideConfig, UsdpConfiguration? config = null)
        {
            _overrideConfig = overrideConfig ?? throw new ArgumentNullException(nameof(overrideConfig));
            _config = config ?? UsdpConfiguration.Instance;

            Diagnostics.Log("TlsOverride", new
            {
                Message = "TlsOverrideProvider initialized with manual configuration",
                TlsVersions = string.Join(", ", _overrideConfig.AllowedTlsVersions),
                CustomCipherSuites = _overrideConfig.CustomCipherSuites?.Count ?? 0,
                StrictCertificateValidation = _overrideConfig.StrictCertificateValidation,
                EnableLowSecurityTlsFallback = _config.EnableLowSecurityTlsFallback
            });
        }

        /// <summary>
        /// Creates an HttpClient with manual TLS override configuration.
        /// 
        /// This method creates an HttpClient configured with the specific TLS
        /// settings defined in the override configuration, bypassing OS-managed
        /// TLS settings entirely.
        /// </summary>
        /// <returns>An HttpClient configured with manual TLS overrides.</returns>
        public HttpClient CreateOverrideHttpClient()
        {
            var handler = CreateOverrideHttpClientHandler();
            var client = new HttpClient(handler);

            // Configure timeout from override settings or use default
            client.Timeout = _overrideConfig.ConnectionTimeout ?? TimeSpan.FromSeconds(30);

            Diagnostics.Log("TlsOverride", new
            {
                Message = "Created HttpClient with manual TLS overrides",
                Configuration = "Manual Override Active"
            });

            return client;
        }

        /// <summary>
        /// Creates an HttpClientHandler with manual TLS override settings.
        /// </summary>
        /// <returns>A configured HttpClientHandler with manual overrides.</returns>
        private HttpClientHandler CreateOverrideHttpClientHandler()
        {
            var handler = new HttpClientHandler();

            // Configure specific TLS versions
            handler.SslProtocols = GetSslProtocolsFromConfig();

            // Configure certificate validation
            if (_overrideConfig.StrictCertificateValidation)
            {
                handler.ServerCertificateCustomValidationCallback = StrictCertificateValidation;
            }
            else if (_overrideConfig.CustomCertificateValidation != null)
            {
                handler.ServerCertificateCustomValidationCallback = _overrideConfig.CustomCertificateValidation;
            }

            // Configure additional security settings
            handler.UseCookies = false;
            handler.UseDefaultCredentials = false;
            handler.CheckCertificateRevocationList = _overrideConfig.CheckCertificateRevocation;

            // Configure client certificates if specified
            if (_overrideConfig.ClientCertificates != null)
            {
                handler.ClientCertificates.AddRange(_overrideConfig.ClientCertificates);
            }

            Diagnostics.Log("TlsOverride", new
            {
                Message = "Configured HttpClientHandler with manual TLS settings",
                SslProtocols = handler.SslProtocols.ToString(),
                CertificateValidation = _overrideConfig.StrictCertificateValidation ? "Strict" : "Custom",
                ClientCertificates = _overrideConfig.ClientCertificates?.Count ?? 0
            });

            return handler;
        }

        /// <summary>
        /// Converts the configured TLS versions to SslProtocols enum values.
        /// </summary>
        /// <returns>The combined SslProtocols flags.</returns>
        private SslProtocols GetSslProtocolsFromConfig()
        {
            SslProtocols protocols = SslProtocols.None;

            foreach (var version in _overrideConfig.AllowedTlsVersions)
            {
                var versionLower = version.ToLowerInvariant();
                bool isDeprecatedVersion = versionLower == "1.0" || versionLower == "tls10" ||
                                         versionLower == "1.1" || versionLower == "tls11";

                if (isDeprecatedVersion && !_config.EnableLowSecurityTlsFallback)
                {
                    // Skip deprecated TLS versions unless explicitly enabled
                    Diagnostics.Log("TlsOverrideSecurityPolicy", new
                    {
                        Message = "Deprecated TLS version blocked by security policy",
                        TlsVersion = version,
                        EnableLowSecurityTlsFallback = _config.EnableLowSecurityTlsFallback,
                        SecurityRisk = "TLS 1.0/1.1 have known vulnerabilities"
                    });
                    continue;
                }

                if (isDeprecatedVersion && _config.EnableLowSecurityTlsFallback)
                {
                    // Log security warning when deprecated versions are used
                    Diagnostics.Log("TlsOverrideSecurityWarning", new
                    {
                        Message = "Using deprecated TLS version in manual override - security risk",
                        TlsVersion = version,
                        SecurityRisk = "TLS 1.0/1.1 have known vulnerabilities",
                        Recommendation = "Upgrade to TLS 1.2 or higher as soon as possible"
                    });
                }

                protocols |= versionLower switch
                {
                    "1.3" or "tls13" => SslProtocols.Tls13,
                    "1.2" or "tls12" => SslProtocols.Tls12,
                    "1.1" or "tls11" => SslProtocols.Tls11,
                    "1.0" or "tls10" => SslProtocols.Tls,
                    _ => SslProtocols.None
                };
            }

            // Default to TLS 1.2 if no valid protocols were specified
            if (protocols == SslProtocols.None)
            {
                protocols = SslProtocols.Tls12;

                Diagnostics.Log("TlsOverride", new
                {
                    Message = "No valid TLS versions specified, defaulting to TLS 1.2",
                    SpecifiedVersions = string.Join(", ", _overrideConfig.AllowedTlsVersions),
                    EnableLowSecurityTlsFallback = _config.EnableLowSecurityTlsFallback
                });
            }

            return protocols;
        }

        /// <summary>
        /// Provides strict certificate validation for enhanced security.
        /// </summary>
        /// <param name="message">The HTTP request message.</param>
        /// <param name="certificate">The server certificate.</param>
        /// <param name="chain">The certificate chain.</param>
        /// <param name="sslPolicyErrors">SSL policy errors.</param>
        /// <returns>True if the certificate is valid.</returns>
        private bool StrictCertificateValidation(
            HttpRequestMessage message,
            X509Certificate2? certificate,
            X509Chain? chain,
            SslPolicyErrors sslPolicyErrors)
        {
            // Strict validation: no SSL policy errors allowed
            if (sslPolicyErrors != SslPolicyErrors.None)
            {
                Diagnostics.Log("TlsOverride", new
                {
                    Message = "Certificate validation failed in strict mode",
                    Url = message.RequestUri?.ToString(),
                    SslPolicyErrors = sslPolicyErrors.ToString(),
                    CertificateSubject = certificate?.Subject
                });

                return false;
            }

            // Additional custom validation can be added here
            if (certificate != null && _overrideConfig.RequiredCertificateProperties != null)
            {
                return ValidateCustomCertificateProperties(certificate);
            }

            return true;
        }

        /// <summary>
        /// Validates custom certificate properties if specified in configuration.
        /// </summary>
        /// <param name="certificate">The certificate to validate.</param>
        /// <returns>True if the certificate meets custom requirements.</returns>
        private bool ValidateCustomCertificateProperties(X509Certificate2 certificate)
        {
            if (_overrideConfig.RequiredCertificateProperties == null)
            {
                return true;
            }

            foreach (var requirement in _overrideConfig.RequiredCertificateProperties)
            {
                var propertyValue = GetCertificateProperty(certificate, requirement.Key);
                if (propertyValue != requirement.Value)
                {
                    Diagnostics.Log("TlsOverride", new
                    {
                        Message = "Certificate custom property validation failed",
                        Property = requirement.Key,
                        Expected = requirement.Value,
                        Actual = propertyValue,
                        CertificateSubject = certificate.Subject
                    });

                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// Gets a certificate property value by name.
        /// </summary>
        /// <param name="certificate">The certificate.</param>
        /// <param name="propertyName">The property name.</param>
        /// <returns>The property value or null if not found.</returns>
        private static string? GetCertificateProperty(X509Certificate2 certificate, string propertyName)
        {
            return propertyName.ToLowerInvariant() switch
            {
                "subject" => certificate.Subject,
                "issuer" => certificate.Issuer,
                "thumbprint" => certificate.Thumbprint,
                "serialnumber" => certificate.SerialNumber,
                "version" => certificate.Version.ToString(),
                _ => null
            };
        }
    }

    /// <summary>
    /// Configuration class for manual TLS overrides.
    /// 
    /// This class contains all the settings needed for manual TLS configuration.
    /// It is designed to be easily serializable and configurable through
    /// external configuration files or programmatic setup.
    /// </summary>
    public class TlsOverrideConfiguration
    {
        /// <summary>
        /// Gets or sets the allowed TLS versions for manual override.
        /// Default: ["1.3", "1.2"] (modern TLS versions)
        /// </summary>
        public List<string> AllowedTlsVersions { get; set; } = new() { "1.3", "1.2" };

        /// <summary>
        /// Gets or sets custom cipher suites (platform-dependent implementation).
        /// Note: Cipher suite specification is platform-specific and may not be
        /// supported on all .NET implementations.
        /// </summary>
        public List<string>? CustomCipherSuites { get; set; }

        /// <summary>
        /// Gets or sets whether to use strict certificate validation.
        /// When true, no SSL policy errors are tolerated.
        /// </summary>
        public bool StrictCertificateValidation { get; set; } = true;

        /// <summary>
        /// Gets or sets a custom certificate validation callback.
        /// This allows for completely custom certificate validation logic.
        /// </summary>
        public Func<HttpRequestMessage, X509Certificate2?, X509Chain?, SslPolicyErrors, bool>? CustomCertificateValidation { get; set; }

        /// <summary>
        /// Gets or sets whether to check certificate revocation lists.
        /// </summary>
        public bool CheckCertificateRevocation { get; set; } = true;

        /// <summary>
        /// Gets or sets client certificates for mutual TLS authentication.
        /// </summary>
        public X509Certificate2Collection? ClientCertificates { get; set; }

        /// <summary>
        /// Gets or sets the connection timeout for override connections.
        /// If null, uses the default timeout from UsdpConfiguration.
        /// </summary>
        public TimeSpan? ConnectionTimeout { get; set; }

        /// <summary>
        /// Gets or sets required certificate properties for additional validation.
        /// Key-value pairs where the key is the property name and value is the expected value.
        /// </summary>
        public Dictionary<string, string>? RequiredCertificateProperties { get; set; }
    }
}
