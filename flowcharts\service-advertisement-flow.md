# Service Advertisement Flow with UDP Security

```mermaid
sequenceDiagram
    participant A as Instance A<br/>(Service Provider)
    participant Net as Multicast Network<br/>(***************:5353)
    participant B as Instance B<br/>(Service Consumer)
    participant C as Instance C<br/>(Service Consumer)
    
    Note over A,C: All instances have EnableUdpSecurity = true
    
    A->>A: Create ServiceAdvertisement<br/>{"serviceId": "home/lighting/bulb1", "endpoint": {...}}
    A->>A: Serialize to CBOR<br/>(~200 bytes)
    A->>A: UdpSecurityOverride.SecureData()<br/>- Add timestamp<br/>- Generate HMAC-SHA256<br/>- Result: ~240 bytes
    A->>Net: UDP Multicast Send<br/>[Original Data][Timestamp][MAC]
    
    Net->>B: Receive secured UDP packet
    Net->>C: Receive secured UDP packet
    
    B->>B: UdpSecurityOverride.VerifyAndExtractData()<br/>- Verify timestamp (within 5min window)<br/>- Verify HMAC-SHA256<br/>- Extract original data
    B->>B: Deserialize CBOR to ServiceAdvertisement
    B->>B: Add to local cache & BloomFilter
    
    C->>C: UdpSecurityOverride.VerifyAndExtractData()<br/>- Verify timestamp (within 5min window)<br/>- Verify HMAC-SHA256<br/>- Extract original data
    C->>C: Deserialize CBOR to ServiceAdvertisement
    C->>C: Add to local cache & BloomFilter
    
    Note over A,C: Service now discoverable by B and C
```

## Description

This diagram shows the complete flow of a service advertisement in a secured USDP2 network where all three instances have UDP security enabled. The flow demonstrates:

1. **Service Creation**: Instance A creates a service advertisement
2. **Serialization**: The advertisement is serialized to CBOR format
3. **Security Processing**: UdpSecurityOverride adds timestamp and HMAC-SHA256 authentication
4. **Network Transmission**: Secured message is sent via UDP multicast
5. **Reception & Verification**: Instances B and C receive, verify, and process the secured message
6. **Local Storage**: Verified advertisements are stored in local cache and BloomFilter

The security overhead adds approximately 40 bytes (20% increase) to the message size while providing message authentication and replay protection.
