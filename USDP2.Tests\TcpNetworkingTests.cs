using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.VisualStudio.TestTools.UnitTesting;

namespace USDP2.Tests
{
    [TestClass]
    public class TcpNetworkingTests
    {
        // Use centralized configuration for test values
        private static int TestPort => UsdpConfiguration.Instance.DefaultServicePort + 1000; // Offset to avoid conflicts
        private const string TestMessage = "Hello, TCP USDP!";

        [TestMethod]
        public async Task TcpNetworkSender_SendAsync_ValidData_Success()
        {
            // Arrange
            var sender = new TcpNetworkSender();
            var data = Encoding.UTF8.GetBytes(TestMessage);
            var receivedData = new TaskCompletionSource<byte[]>();

            // Set up TCP listener
            var listener = new TcpListener(IPAddress.Loopback, TestPort);
            listener.Start();

            var listenerTask = Task.Run(async () =>
            {
                try
                {
                    using var client = await listener.AcceptTcpClientAsync();
                    using var stream = client.GetStream();

                    var buffer = new byte[1024];
                    var bytesRead = await stream.ReadAsync(buffer, 0, buffer.Length);

                    var received = new byte[bytesRead];
                    Array.Copy(buffer, received, bytesRead);
                    receivedData.SetResult(received);
                }
                catch (Exception ex)
                {
                    receivedData.SetException(ex);
                }
                finally
                {
                    listener.Stop();
                }
            });

            // Act
            await sender.SendAsync(data, "127.0.0.1", TestPort);

            // Assert
            var received = await receivedData.Task.WaitAsync(TimeSpan.FromSeconds(5));
            var receivedMessage = Encoding.UTF8.GetString(received);
            Assert.AreEqual(TestMessage, receivedMessage);
        }

        [TestMethod]
        public async Task TcpNetworkSender_SendAsync_InvalidAddress_ThrowsArgumentOutOfRangeException()
        {
            // Arrange
            var sender = new TcpNetworkSender();
            var data = Encoding.UTF8.GetBytes(TestMessage);

            // Act & Assert - Invalid port throws ArgumentOutOfRangeException
            await Assert.ThrowsExceptionAsync<ArgumentOutOfRangeException>(
                () => sender.SendAsync(data, "127.0.0.1", 99999)); // Invalid port
        }

        [TestMethod]
        public async Task TcpNetworkSender_SendAsync_CancellationRequested_ThrowsTaskCanceledException()
        {
            // Arrange
            var sender = new TcpNetworkSender();
            var data = Encoding.UTF8.GetBytes(TestMessage);
            var cts = new CancellationTokenSource();
            cts.Cancel();

            // Act & Assert - TaskCanceledException is a subclass of OperationCanceledException
            await Assert.ThrowsExceptionAsync<TaskCanceledException>(
                () => sender.SendAsync(data, "127.0.0.1", TestPort + 1, cts.Token));
        }

        [TestMethod]
        public async Task TcpNetworkSender_SendAsync_ConnectionRefused_ThrowsSocketException()
        {
            // Arrange
            var sender = new TcpNetworkSender();
            var data = Encoding.UTF8.GetBytes(TestMessage);

            // Act & Assert - Try to connect to a port that's not listening
            await Assert.ThrowsExceptionAsync<SocketException>(
                () => sender.SendAsync(data, "127.0.0.1", TestPort + 2));
        }

        [TestMethod]
        public async Task TcpNetworkSender_SendAsync_LargeData_Success()
        {
            // Arrange
            var sender = new TcpNetworkSender();
            var largeMessage = new string('A', 10000); // 10KB message
            var data = Encoding.UTF8.GetBytes(largeMessage);
            var receivedData = new TaskCompletionSource<byte[]>();

            // Set up TCP listener
            var listener = new TcpListener(IPAddress.Loopback, TestPort + 3);
            listener.Start();

            var listenerTask = Task.Run(async () =>
            {
                try
                {
                    using var client = await listener.AcceptTcpClientAsync();
                    using var stream = client.GetStream();

                    var buffer = new byte[20000]; // Larger buffer
                    var totalBytesRead = 0;
                    int bytesRead;

                    while ((bytesRead = await stream.ReadAsync(buffer, totalBytesRead, buffer.Length - totalBytesRead)) > 0)
                    {
                        totalBytesRead += bytesRead;
                        if (totalBytesRead >= data.Length)
                            break;
                    }

                    var received = new byte[totalBytesRead];
                    Array.Copy(buffer, received, totalBytesRead);
                    receivedData.SetResult(received);
                }
                catch (Exception ex)
                {
                    receivedData.SetException(ex);
                }
                finally
                {
                    listener.Stop();
                }
            });

            // Act
            await sender.SendAsync(data, "127.0.0.1", TestPort + 3);

            // Assert
            var received = await receivedData.Task.WaitAsync(TimeSpan.FromSeconds(10));
            var receivedMessage = Encoding.UTF8.GetString(received);
            Assert.AreEqual(largeMessage, receivedMessage);
        }

        [TestMethod]
        public async Task TcpNetworkSender_SendAsync_MultipleConnections_Success()
        {
            // Arrange
            var sender = new TcpNetworkSender();
            var data = Encoding.UTF8.GetBytes(TestMessage);
            var connectionsReceived = 0;
            var allConnectionsReceived = new TaskCompletionSource<bool>();

            // Set up TCP listener
            var listener = new TcpListener(IPAddress.Loopback, TestPort + 4);
            listener.Start();

            var listenerTask = Task.Run(async () =>
            {
                try
                {
                    while (connectionsReceived < 3)
                    {
                        using var client = await listener.AcceptTcpClientAsync();
                        using var stream = client.GetStream();

                        var buffer = new byte[1024];
                        await stream.ReadAsync(buffer, 0, buffer.Length);

                        Interlocked.Increment(ref connectionsReceived);
                        if (connectionsReceived >= 3)
                        {
                            allConnectionsReceived.SetResult(true);
                        }
                    }
                }
                catch (Exception ex)
                {
                    allConnectionsReceived.SetException(ex);
                }
                finally
                {
                    listener.Stop();
                }
            });

            // Act - Send multiple messages
            var sendTasks = new List<Task>();
            for (int i = 0; i < 3; i++)
            {
                sendTasks.Add(sender.SendAsync(data, "127.0.0.1", TestPort + 4));
            }
            await Task.WhenAll(sendTasks);

            // Assert
            await allConnectionsReceived.Task.WaitAsync(TimeSpan.FromSeconds(10));
            Assert.AreEqual(3, connectionsReceived);
        }
    }
}
