# USDP2 Agent Guide

## Build Commands
- `dotnet build` - Build the project
- `dotnet build --configuration Release` - Release build
- `dotnet test` - Run all tests
- `dotnet test --filter "FullyQualifiedName~TestMethodName"` - Run specific test
- `dotnet run` - Run the application

## Project Structure
- C# .NET 8.0 library project with nullable reference types enabled
- Main source: `USDP2/` directory
- Solution file: `USDP2.sln`
- Project file: `USDP2/USDP2.csproj`

## Code Style
- **Namespace**: Single namespace `USDP2`
- **XML Documentation**: Required for all public members with `<summary>` tags
- **Nullable Types**: Enabled - use `?` for nullable parameters/returns
- **Imports**: Standard `using` statements at top, grouped by System first
- **Naming**: PascalCase for public members, camelCase with `_` prefix for private fields
- **Interfaces**: Prefix with `I` (e.g., `IAuthenticationProvider`)
- **Error Handling**: Use try-catch blocks, return `false` or throw exceptions appropriately
- **Async**: Prefer async methods with `Async` suffix, return `Task<T>`
- **Primary Constructors**: Used for simple parameter initialization

## Development Guidelines
- **Security**: No hardcoded secrets, use configuration for credentials
- **Serialization**: Standardize on System.Text.Json or USDPSerializer (consider IMessageSerializer interface)
- **Testing**: Add unit tests for new components (test project missing)
- **Disposal**: Implement IAsyncDisposable/IDisposable properly for network resources
- **Documentation**: Complete all "TODO: Add Summary" comments before committing
- **Logging**: Use Microsoft.Extensions.Logging for structured logging
- **Configuration**: Use IOptions pattern for centralized configuration
- **Metrics**: Consider Prometheus-compatible endpoints or .NET EventCounters
- **Plugins**: Define clear contracts via interfaces for extensibility
