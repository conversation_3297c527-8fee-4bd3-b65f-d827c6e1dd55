using System;
using System.Linq;
using System.Reflection;

namespace USDP2
{
    /// <summary>
    /// Simple demonstration of configuration validation with invalid configurations.
    /// This shows how the validation system catches and reports various types of errors.
    /// </summary>
    public static class SimpleInvalidConfigDemo
    {
        /// <summary>
        /// Creates a test configuration instance using reflection.
        /// </summary>
        private static UsdpConfiguration CreateTestConfiguration()
        {
            var constructor = typeof(UsdpConfiguration).GetConstructor(
                BindingFlags.NonPublic | BindingFlags.Instance,
                null,
                Type.EmptyTypes,
                null);

            if (constructor == null)
                throw new InvalidOperationException("Cannot create test configuration instance");

            return (UsdpConfiguration)constructor.Invoke(null);
        }

        /// <summary>
        /// Demonstrates validation with intentionally invalid configurations.
        /// </summary>
        public static void RunInvalidConfigurationDemo()
        {
            Console.WriteLine("=".PadRight(70, '='));
            Console.WriteLine("USDP2 Configuration Validation - Invalid Configuration Demo");
            Console.WriteLine("=".PadRight(70, '='));
            Console.WriteLine();

            // Test 1: Critical Error - Invalid IP Address
            Console.WriteLine("🚨 Test 1: Critical Error - Invalid Multicast Address");
            Console.WriteLine("-".PadRight(50, '-'));

            var config1 = CreateTestConfiguration();
            config1.DefaultMulticastAddress = "invalid-ip-address";

            var results1 = ValidateConfiguration.ValidateAll(config1);
            var criticalErrors1 = results1.Where(r => r.Severity == ValidationSeverity.Critical).ToList();

            Console.WriteLine($"Critical Errors Found: {criticalErrors1.Count}");
            if (criticalErrors1.Count > 0)
            {
                var error = criticalErrors1.First();
                Console.WriteLine($"❌ {error.Message}");
                Console.WriteLine($"💡 {error.RecommendedAction}");
            }
            Console.WriteLine();

            // Test 2: Error - Port Conflict
            Console.WriteLine("❌ Test 2: Configuration Error - Port Conflict");
            Console.WriteLine("-".PadRight(50, '-'));

            var config2 = CreateTestConfiguration();
            config2.DefaultHttpPort = 8080;
            config2.DefaultHttpsPort = 8080; // Same port - conflict!

            var results2 = ValidateConfiguration.ValidateAll(config2);
            var errors2 = results2.Where(r => r.Severity == ValidationSeverity.Error).ToList();

            Console.WriteLine($"Errors Found: {errors2.Count}");
            if (errors2.Count > 0)
            {
                var error = errors2.First();
                Console.WriteLine($"❌ {error.Message}");
                Console.WriteLine($"💡 {error.RecommendedAction}");
            }
            Console.WriteLine();

            // Test 3: Warning - Security Disabled
            Console.WriteLine("⚠️  Test 3: Warning - Security Disabled");
            Console.WriteLine("-".PadRight(50, '-'));

            var config3 = CreateTestConfiguration();
            config3.DefaultSecurity = "none"; // Security disabled

            var results3 = ValidateConfiguration.ValidateAll(config3);
            var warnings3 = results3.Where(r => r.Severity == ValidationSeverity.Warning).ToList();

            Console.WriteLine($"Warnings Found: {warnings3.Count}");
            if (warnings3.Count > 0)
            {
                var warning = warnings3.First();
                Console.WriteLine($"⚠️  {warning.Message}");
                Console.WriteLine($"💡 {warning.RecommendedAction}");
            }
            Console.WriteLine();

            // Test 4: Multiple Issues
            Console.WriteLine("🔥 Test 4: Multiple Configuration Issues");
            Console.WriteLine("-".PadRight(50, '-'));

            var config4 = CreateTestConfiguration();
            config4.DefaultMulticastAddress = "***********"; // Not multicast
            config4.DefaultHttpPort = 80; // Well-known port
            config4.RequireAuthentication = true;
            config4.DefaultSecurity = "none"; // Auth without security
            config4.DefaultBufferSize = 64 * 1024;
            config4.MaxBufferSize = 32 * 1024; // Buffer conflict

            var results4 = ValidateConfiguration.ValidateAll(config4);

            Console.WriteLine($"Total Issues: {results4.Count}");
            Console.WriteLine($"Critical: {results4.Count(r => r.Severity == ValidationSeverity.Critical)}");
            Console.WriteLine($"Errors: {results4.Count(r => r.Severity == ValidationSeverity.Error)}");
            Console.WriteLine($"Warnings: {results4.Count(r => r.Severity == ValidationSeverity.Warning)}");
            Console.WriteLine();

            Console.WriteLine("Top Issues:");
            var topIssues = results4
                .Where(r => r.Severity == ValidationSeverity.Error || r.Severity == ValidationSeverity.Critical)
                .Take(3)
                .ToList();

            foreach (var issue in topIssues)
            {
                Console.WriteLine($"  [{issue.Severity}] {issue.PropertyName}: {issue.Message}");
            }
            Console.WriteLine();

            // Test 5: Startup Validation Failure
            Console.WriteLine("🚀 Test 5: Startup Validation Failure");
            Console.WriteLine("-".PadRight(50, '-'));

            try
            {
                // This should fail with strict validation
                StartupConfigurationValidator.ValidateAtStartup(
                    config4,
                    StartupConfigurationValidator.ValidationBehavior.Strict);

                Console.WriteLine("✅ Validation passed (unexpected!)");
            }
            catch (InvalidOperationException ex)
            {
                Console.WriteLine("❌ Validation failed as expected");
                Console.WriteLine($"📝 Error: {ex.Message.Split('\n')[0]}");

                // Count issues in the error message
                var errorLines = ex.Message.Split('\n');
                var issueCount = errorLines.Where(line => line.Contains("CRITICAL:") || line.Contains("Error:")).Count();
                Console.WriteLine($"📊 Issues reported in exception: {issueCount}");
            }
            Console.WriteLine();

            // Test 6: Lenient vs Strict Behavior
            Console.WriteLine("⚖️  Test 6: Lenient vs Strict Validation Behavior");
            Console.WriteLine("-".PadRight(50, '-'));

            var configWithWarnings = CreateTestConfiguration();
            configWithWarnings.DefaultSecurity = "none"; // Warning only
            configWithWarnings.DefaultHttpPort = 80; // Warning only

            try
            {
                var strictResult = StartupConfigurationValidator.ValidateAtStartup(
                    configWithWarnings,
                    StartupConfigurationValidator.ValidationBehavior.Strict);
                Console.WriteLine("✅ Strict validation: PASSED (warnings only)");
            }
            catch (InvalidOperationException)
            {
                Console.WriteLine("❌ Strict validation: FAILED");
            }

            try
            {
                var lenientResult = StartupConfigurationValidator.ValidateAtStartup(
                    configWithWarnings,
                    StartupConfigurationValidator.ValidationBehavior.Lenient);
                Console.WriteLine("✅ Lenient validation: PASSED (warnings only)");
            }
            catch (InvalidOperationException)
            {
                Console.WriteLine("❌ Lenient validation: FAILED");
            }

            Console.WriteLine();
            Console.WriteLine("✅ Invalid configuration validation demo completed!");
            Console.WriteLine();
            Console.WriteLine("Summary:");
            Console.WriteLine("- Critical errors prevent application startup");
            Console.WriteLine("- Errors indicate potential runtime failures");
            Console.WriteLine("- Warnings suggest suboptimal configurations");
            Console.WriteLine("- Validation behavior can be configured per environment");
            Console.WriteLine("- Clear error messages help developers fix issues quickly");
        }

        /// <summary>
        /// Entry point for the simple demo.
        /// </summary>
        public static void Main(string[] args)
        {
            if (args.Length > 0 && args[0] == "--simple-invalid-demo")
            {
                RunInvalidConfigurationDemo();
                return;
            }

            Console.WriteLine("Use --simple-invalid-demo to run the simple invalid configuration demo");
        }
    }
}
