# Circuit Breaker Pattern Implementation Guide

## Overview

The USDP2 project now includes a comprehensive implementation of the Circuit Breaker pattern to improve system resilience and handle network failures gracefully. This implementation prevents cascading failures, provides fast-fail behavior during outages, and enables automatic recovery testing.

## 🎯 Benefits

### **System Resilience**
- **Prevents cascading failures** in distributed systems
- **Fast failure response** during network outages
- **Automatic recovery testing** when services become available
- **Resource protection** prevents exhaustion during failures

### **User Experience**
- **Predictable failure modes** with clear error messages
- **Reduced timeout delays** through fast-fail behavior
- **Improved responsiveness** during network instability
- **Graceful degradation** of service functionality

### **Monitoring & Operations**
- **Comprehensive statistics** collection and reporting
- **Health monitoring** with periodic status checks
- **Configurable thresholds** for different operation types
- **Detailed logging** for troubleshooting and analysis

## 🏗️ Architecture

### **Core Components**

#### **CircuitBreaker Class**
- Implements the core circuit breaker logic
- Manages state transitions (Closed → Open → Half-Open → Closed)
- Tracks failure/success statistics
- Provides configurable thresholds and timeouts

#### **CircuitBreakerManager Class**
- Centralized management of multiple circuit breakers
- Automatic circuit breaker creation with smart defaults
- System-wide health monitoring and reporting
- Periodic health checks and alerting

#### **NetworkCircuitBreakerWrapper Class**
- Wraps existing network senders with circuit breaker protection
- Transparent integration with existing code
- Protocol-specific configuration optimization
- Comprehensive logging and monitoring

#### **NetworkCircuitBreakerFactory Class**
- Factory methods for creating circuit breaker-protected network senders
- Pre-configured options for different protocols (HTTP, UDP, TCP)
- Easy integration with existing network operations

## 🔧 Configuration

### **Circuit Breaker Options**

```csharp
var options = new CircuitBreakerOptions
{
    FailureThreshold = 5,                           // Failures to open circuit
    OpenTimeout = TimeSpan.FromSeconds(30),         // Time to stay open
    SuccessThreshold = 3,                           // Successes to close circuit
    OperationTimeout = TimeSpan.FromSeconds(10),    // Individual operation timeout
    ShouldHandleException = ex => ex is not OperationCanceledException
};
```

### **USDP Configuration Integration**

```csharp
// Global circuit breaker settings in UsdpConfiguration
config.EnableCircuitBreakers = true;                    // Enable/disable globally
config.DefaultCircuitBreakerFailureThreshold = 5;       // Default failure threshold
config.DefaultCircuitBreakerOpenTimeout = TimeSpan.FromSeconds(30);
config.DefaultCircuitBreakerSuccessThreshold = 3;       // Default success threshold
config.EnableCircuitBreakerMonitoring = true;           // Enable health monitoring
```

### **Protocol-Specific Defaults**

| Protocol | Failure Threshold | Open Timeout | Operation Timeout | Success Threshold |
|----------|------------------|--------------|-------------------|-------------------|
| **HTTP** | 5 failures | 30 seconds | Network timeout | 3 successes |
| **UDP** | 3 failures | 15 seconds | 5 seconds | 2 successes |
| **TCP** | 4 failures | 20 seconds | Network timeout | 2 successes |
| **mDNS** | 7 failures | 45 seconds | 10 seconds | 3 successes |
| **STUN** | 6 failures | 60 seconds | 15 seconds | 2 successes |

## 🚀 Usage Examples

### **Basic Circuit Breaker Usage**

```csharp
// Create circuit breaker with custom options
var circuitBreaker = new CircuitBreaker("MyOperation", new CircuitBreakerOptions
{
    FailureThreshold = 3,
    OpenTimeout = TimeSpan.FromSeconds(10)
});

// Execute operation with protection
try
{
    var result = await circuitBreaker.ExecuteAsync(async ct =>
    {
        // Your network operation here
        return await SomeNetworkOperation(ct);
    });
}
catch (CircuitBreakerOpenException ex)
{
    // Circuit is open - handle gracefully
    Console.WriteLine($"Service unavailable. Retry in {ex.TimeUntilRetry}");
}
```

### **Network Sender Integration**

```csharp
// Create circuit breaker-protected network senders
var httpSender = NetworkCircuitBreakerFactory.CreateHttpSender();
var udpSender = NetworkCircuitBreakerFactory.CreateUdpSender();

// Use exactly like regular network senders
await httpSender.SendAsync(data, "example.com", 80);
await udpSender.SendAsync(data, "127.0.0.1", 12345);

// Automatic circuit breaker protection is applied
```

### **Wrapping Existing Senders**

```csharp
// Wrap existing network sender with circuit breaker
var existingSender = new HttpNetworkSender();
var protectedSender = NetworkCircuitBreakerFactory.WrapSender(
    existingSender, 
    "CustomHttpSender",
    customOptions
);
```

### **MdnsProxy Integration**

```csharp
// MdnsProxy now has built-in circuit breaker protection
var cache = new ServiceAdvertisementCache();
var mdnsProxy = new MdnsProxy(cache);

try
{
    await mdnsProxy.PublishToMdnsAsync();
    await mdnsProxy.ImportFromMdnsAsync();
}
catch (CircuitBreakerOpenException ex)
{
    // mDNS operations are protected by circuit breakers
    Console.WriteLine($"mDNS temporarily unavailable: {ex.Message}");
}
```

## 📊 Monitoring & Health Checks

### **Individual Circuit Breaker Statistics**

```csharp
var stats = circuitBreaker.GetStatistics();
Console.WriteLine($"State: {stats.State}");
Console.WriteLine($"Total Operations: {stats.TotalOperations}");
Console.WriteLine($"Failure Rate: {stats.FailureRate:P1}");
Console.WriteLine($"Time Until Retry: {stats.TimeUntilRetry}");
```

### **System-Wide Health Monitoring**

```csharp
// Get comprehensive health summary
var health = CircuitBreakerManager.Instance.GetHealthSummary();
Console.WriteLine($"Total Circuit Breakers: {health.TotalCircuitBreakers}");
Console.WriteLine($"Open Circuits: {health.OpenCircuits}");
Console.WriteLine($"Overall Failure Rate: {health.OverallFailureRate:P1}");

// Get detailed statistics for all circuit breakers
var allStats = CircuitBreakerManager.Instance.GetHealthStatistics();
foreach (var kvp in allStats)
{
    Console.WriteLine($"{kvp.Key}: {kvp.Value.State} - {kvp.Value.FailureRate:P1}");
}
```

### **Automatic Health Monitoring**

The CircuitBreakerManager automatically performs periodic health checks and logs:
- Overall system health summaries
- Alerts for open circuits
- Performance statistics and trends
- State transition notifications

## 🔄 State Transitions

### **Circuit Breaker States**

1. **Closed (Normal Operation)**
   - All operations are allowed
   - Failure count is tracked
   - Transitions to Open when failure threshold is exceeded

2. **Open (Fast-Fail Mode)**
   - All operations are blocked immediately
   - CircuitBreakerOpenException is thrown
   - Transitions to Half-Open after timeout period

3. **Half-Open (Recovery Testing)**
   - Limited operations are allowed
   - Success count is tracked
   - Transitions to Closed after success threshold
   - Transitions back to Open on any failure

### **State Transition Diagram**

```
    [Closed] ──failure threshold──> [Open]
        ↑                             │
        │                             │ timeout
        │                             ↓
    success threshold            [Half-Open]
        │                             │
        └─────────────────────────────┘
                    failure
```

## 🛠️ Integration Points

### **Existing Components Enhanced**

1. **MdnsProxy**
   - Built-in circuit breaker protection for publish/import operations
   - mDNS-specific configuration optimized for local network operations

2. **NetworkSenderFactory**
   - Extended with circuit breaker factory methods
   - Automatic protocol-specific configuration

3. **UsdpConfiguration**
   - New circuit breaker configuration section
   - Global enable/disable controls
   - Default threshold and timeout settings

### **New Components Added**

1. **CircuitBreaker** - Core pattern implementation
2. **CircuitBreakerManager** - Centralized management
3. **NetworkCircuitBreakerWrapper** - Network sender integration
4. **NetworkCircuitBreakerFactory** - Factory methods
5. **CircuitBreakerOptions** - Configuration class
6. **CircuitBreakerStatistics** - Monitoring data
7. **CircuitBreakerOpenException** - Specific exception type

## 🧪 Testing

### **Comprehensive Test Coverage**

- **State transition testing** - Verifies correct state changes
- **Failure threshold testing** - Confirms circuit opens at threshold
- **Recovery testing** - Validates half-open to closed transitions
- **Timeout handling** - Tests operation timeout behavior
- **Exception classification** - Verifies which exceptions count as failures
- **Statistics accuracy** - Confirms monitoring data correctness
- **Network wrapper testing** - Tests integration with network senders

### **Test Examples**

```csharp
[TestMethod]
public async Task CircuitBreaker_RepeatedFailures_OpensCircuit()
{
    var circuitBreaker = new CircuitBreaker("Test", new CircuitBreakerOptions
    {
        FailureThreshold = 3
    });

    // Cause 3 failures
    for (int i = 0; i < 3; i++)
    {
        await Assert.ThrowsExceptionAsync<InvalidOperationException>(async () =>
        {
            await circuitBreaker.ExecuteAsync<string>(async ct =>
            {
                throw new InvalidOperationException($"Failure {i}");
            });
        });
    }

    // Circuit should be open
    Assert.AreEqual(CircuitBreakerState.Open, circuitBreaker.State);
}
```

## 📈 Performance Considerations

### **Minimal Overhead**
- **Fast state checks** using volatile fields
- **Efficient statistics tracking** with minimal allocations
- **Optimized exception handling** for common scenarios
- **Lazy initialization** of circuit breakers

### **Memory Management**
- **Automatic cleanup** of unused circuit breakers
- **Efficient statistics storage** with primitive types
- **Minimal object allocations** during normal operation
- **Proper disposal patterns** for all components

## 🔒 Thread Safety

All circuit breaker components are fully thread-safe:
- **Atomic state transitions** using locks where necessary
- **Volatile fields** for fast reads
- **Concurrent collections** for circuit breaker management
- **Thread-safe statistics** updates

## 🎯 Best Practices

### **Configuration Guidelines**
1. **Start with defaults** and adjust based on monitoring
2. **Use protocol-specific settings** for optimal performance
3. **Monitor failure rates** and adjust thresholds accordingly
4. **Enable health monitoring** for production systems

### **Error Handling**
1. **Always handle CircuitBreakerOpenException** gracefully
2. **Provide meaningful user feedback** during outages
3. **Implement fallback mechanisms** where possible
4. **Log circuit breaker events** for analysis

### **Monitoring**
1. **Set up alerts** for open circuits
2. **Track failure rate trends** over time
3. **Monitor recovery patterns** after outages
4. **Use health summaries** for system overview

This circuit breaker implementation provides robust network resilience for USDP2, ensuring graceful handling of network failures and improved system stability.
