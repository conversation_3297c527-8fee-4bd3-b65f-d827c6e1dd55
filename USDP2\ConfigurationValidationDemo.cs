using System;
using System.Linq;
using System.Threading.Tasks;

namespace USDP2
{
    /// <summary>
    /// Demonstrates the configuration validation system in action.
    /// This class provides a simple way to test and showcase the validation features.
    /// </summary>
    public static class ConfigurationValidationDemo
    {
        /// <summary>
        /// Runs a comprehensive demonstration of the configuration validation system.
        /// </summary>
        public static async Task RunDemoAsync()
        {
            Console.WriteLine("=".PadRight(60, '='));
            Console.WriteLine("USDP2 Configuration Validation System Demo");
            Console.WriteLine("=".PadRight(60, '='));
            Console.WriteLine();

            try
            {
                // Demo 1: Basic validation of default configuration
                await DemoBasicValidationAsync();

                // Demo 2: Validation with different behaviors
                await DemoValidationBehaviorsAsync();

                // Demo 3: Validation reporting
                DemoValidationReporting();

                // Demo 4: Startup recommendations
                DemoStartupRecommendations();

                Console.WriteLine();
                Console.WriteLine("? Configuration validation demo completed successfully!");

            }
            catch (Exception ex)
            {
                Console.WriteLine();
                Console.WriteLine($"? Demo failed with error: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// Demonstrates basic configuration validation.
        /// </summary>
        private static Task DemoBasicValidationAsync()
        {
            Console.WriteLine("?? Demo 1: Basic Configuration Validation");
            Console.WriteLine("-".PadRight(50, '-'));

            try
            {
                // Validate the default configuration
                Console.WriteLine("Validating default USDP2 configuration...");

                var results = ValidateConfiguration.ValidateAll(UsdpConfiguration.Instance);

                Console.WriteLine($"? Validation completed successfully!");
                Console.WriteLine($"   Total validation results: {results.Count}");
                Console.WriteLine($"   Critical issues: {results.Count(r => r.Severity == ValidationSeverity.Critical)}");
                Console.WriteLine($"   Errors: {results.Count(r => r.Severity == ValidationSeverity.Error)}");
                Console.WriteLine($"   Warnings: {results.Count(r => r.Severity == ValidationSeverity.Warning)}");
                Console.WriteLine($"   Info messages: {results.Count(r => r.Severity == ValidationSeverity.Info)}");

                // Show a few sample results
                var sampleResults = results.Take(3).ToList();
                if (sampleResults.Count > 0)
                {
                    Console.WriteLine();
                    Console.WriteLine("Sample validation results:");
                    foreach (var result in sampleResults)
                    {
                        Console.WriteLine($"   [{result.Severity}] {result.PropertyName}: {result.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"? Basic validation failed: {ex.Message}");
            }

            Console.WriteLine();
            return Task.CompletedTask;
        }

        /// <summary>
        /// Demonstrates different validation behaviors.
        /// </summary>
        private static async Task DemoValidationBehaviorsAsync()
        {
            Console.WriteLine("?? Demo 2: Validation Behaviors");
            Console.WriteLine("-".PadRight(50, '-'));

            var behaviors = new[]
            {
                StartupConfigurationValidator.ValidationBehavior.Strict,
                StartupConfigurationValidator.ValidationBehavior.Lenient,
                StartupConfigurationValidator.ValidationBehavior.LogOnly,
                StartupConfigurationValidator.ValidationBehavior.Disabled
            };

            foreach (var behavior in behaviors)
            {
                try
                {
                    Console.WriteLine($"Testing {behavior} validation behavior...");

                    var result = await StartupConfigurationValidator.ValidateAtStartupAsync(
                        null, behavior);

                    Console.WriteLine($"   ? {behavior} validation: {(result ? "PASSED" : "FAILED")}");
                }
                catch (InvalidOperationException ex)
                {
                    Console.WriteLine($"   ? {behavior} validation failed: {ex.Message.Split('\n')[0]}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"   ?? {behavior} validation error: {ex.Message}");
                }
            }

            Console.WriteLine();
        }

        /// <summary>
        /// Demonstrates validation reporting capabilities.
        /// </summary>
        private static void DemoValidationReporting()
        {
            Console.WriteLine("?? Demo 3: Validation Reporting");
            Console.WriteLine("-".PadRight(50, '-'));

            try
            {
                // Generate a comprehensive validation report
                var report = StartupConfigurationValidator.CreateValidationReport();

                Console.WriteLine("Configuration Validation Report:");
                Console.WriteLine($"   Timestamp: {report.ValidationTimestamp:yyyy-MM-dd HH:mm:ss} UTC");
                Console.WriteLine($"   Overall Status: {(report.IsValid ? "? VALID" : "? INVALID")}");
                Console.WriteLine($"   Total Issues: {report.TotalIssues}");
                Console.WriteLine($"     Critical: {report.CriticalIssues}");
                Console.WriteLine($"     Errors: {report.ErrorIssues}");
                Console.WriteLine($"     Warnings: {report.WarningIssues}");
                Console.WriteLine($"     Info: {report.InfoIssues}");

                // Show some detailed issues if any
                if (report.ValidationResults.Count > 0)
                {
                    Console.WriteLine();
                    Console.WriteLine("Sample validation details:");

                    var sampleIssues = report.ValidationResults
                        .Where(r => r.Severity != ValidationSeverity.Info)
                        .Take(3)
                        .ToList();

                    if (sampleIssues.Count > 0)
                    {
                        foreach (var issue in sampleIssues)
                        {
                            Console.WriteLine($"   [{issue.Severity}] {issue.PropertyName}:");
                            Console.WriteLine($"      Message: {issue.Message}");
                            if (!string.IsNullOrEmpty(issue.RecommendedAction))
                            {
                                Console.WriteLine($"      Action: {issue.RecommendedAction}");
                            }
                        }
                    }
                    else
                    {
                        Console.WriteLine("   No critical issues or errors found! ??");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"? Reporting demo failed: {ex.Message}");
            }

            Console.WriteLine();
        }

        /// <summary>
        /// Demonstrates startup recommendations.
        /// </summary>
        private static void DemoStartupRecommendations()
        {
            Console.WriteLine("?? Demo 4: Startup Recommendations");
            Console.WriteLine("-".PadRight(50, '-'));

            try
            {
                var recommendations = StartupConfigurationValidator.GetStartupRecommendations();

                Console.WriteLine("Configuration Recommendations:");

                if (recommendations.SecurityRecommendations.Count > 0)
                {
                    Console.WriteLine("   ?? Security:");
                    foreach (var rec in recommendations.SecurityRecommendations.Take(2))
                    {
                        Console.WriteLine($"      � {rec}");
                    }
                }

                if (recommendations.PerformanceRecommendations.Count > 0)
                {
                    Console.WriteLine("   ? Performance:");
                    foreach (var rec in recommendations.PerformanceRecommendations.Take(2))
                    {
                        Console.WriteLine($"      � {rec}");
                    }
                }

                if (recommendations.ReliabilityRecommendations.Count > 0)
                {
                    Console.WriteLine("   ??? Reliability:");
                    foreach (var rec in recommendations.ReliabilityRecommendations.Take(2))
                    {
                        Console.WriteLine($"      � {rec}");
                    }
                }

                if (recommendations.MonitoringRecommendations.Count > 0)
                {
                    Console.WriteLine("   ?? Monitoring:");
                    foreach (var rec in recommendations.MonitoringRecommendations.Take(2))
                    {
                        Console.WriteLine($"      � {rec}");
                    }
                }

                if (recommendations.GeneralRecommendations.Count > 0)
                {
                    Console.WriteLine("   ?? General:");
                    foreach (var rec in recommendations.GeneralRecommendations.Take(3))
                    {
                        Console.WriteLine($"      � {rec}");
                    }
                }

                if (recommendations.SecurityRecommendations.Count == 0 &&
                    recommendations.PerformanceRecommendations.Count == 0 &&
                    recommendations.ReliabilityRecommendations.Count == 0 &&
                    recommendations.MonitoringRecommendations.Count == 0 &&
                    recommendations.GeneralRecommendations.Count == 0)
                {
                    Console.WriteLine("   ? No specific recommendations - configuration looks good!");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"? Recommendations demo failed: {ex.Message}");
            }

            Console.WriteLine();
        }

        /// <summary>
        /// Entry point for running the demo as a standalone program.
        /// </summary>
#pragma warning disable VSTHRD200 // Main method is a special case and doesn't need Async suffix
        public static async Task Main(string[] args)
#pragma warning restore VSTHRD200
        {
            // Check if this is being run as a demo
            if (args.Length > 0 && args[0] == "--demo-validation")
            {
                await RunDemoAsync();
                return;
            }

            // Check if this is being run as invalid config test
            if (args.Length > 0 && args[0] == "--test-invalid")
            {
                SimpleInvalidConfigDemo.RunInvalidConfigurationDemo();
                return;
            }

            // Check if this is being run as network validation demo
            if (args.Length > 0 && args[0] == "--demo-network-validation")
            {
                NetworkValidationDemo.RunDemo();
                return;
            }

            // Otherwise, just show a brief validation summary
            Console.WriteLine("USDP2 Configuration Validation");
            Console.WriteLine("==============================");
            Console.WriteLine();
            Console.WriteLine("Available options:");
            Console.WriteLine("  --demo-validation         : Run full configuration validation demo");
            Console.WriteLine("  --test-invalid            : Test with invalid configurations");
            Console.WriteLine("  --demo-network-validation : Run network data validation demo");
            Console.WriteLine();

            try
            {
                var results = ValidateConfiguration.ValidateAll(UsdpConfiguration.Instance);
                var criticalCount = results.Count(r => r.Severity == ValidationSeverity.Critical);
                var errorCount = results.Count(r => r.Severity == ValidationSeverity.Error);

                if (criticalCount == 0 && errorCount == 0)
                {
                    Console.WriteLine("? Current configuration validation passed!");
                    Console.WriteLine($"   Found {results.Count(r => r.Severity == ValidationSeverity.Warning)} warnings");
                    Console.WriteLine($"   Found {results.Count(r => r.Severity == ValidationSeverity.Info)} info messages");
                }
                else
                {
                    Console.WriteLine("? Current configuration validation found issues:");
                    Console.WriteLine($"   Critical: {criticalCount}");
                    Console.WriteLine($"   Errors: {errorCount}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"? Validation failed: {ex.Message}");
            }
        }
    }
}
