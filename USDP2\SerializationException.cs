using System;

namespace USDP2
{
    /// <summary>
    /// Exception thrown when serialization or deserialization fails.
    /// </summary>
    public class SerializationException : Exception
    {
        /// <summary>
        /// Gets the error code associated with this serialization exception.
        /// </summary>
        public SerializationErrorCode ErrorCode { get; }

        /// <summary>
        /// Initializes a new instance of the <see cref="SerializationException"/> class.
        /// </summary>
        public SerializationException() : base("A serialization error occurred.")
        {
            ErrorCode = SerializationErrorCode.Unknown;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="SerializationException"/> class with a specified error message.
        /// </summary>
        /// <param name="message">The message that describes the error.</param>
        public SerializationException(string message) : base(message)
        {
            ErrorCode = SerializationErrorCode.Unknown;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="SerializationException"/> class with a specified error message
        /// and a reference to the inner exception that is the cause of this exception.
        /// </summary>
        /// <param name="message">The message that describes the error.</param>
        /// <param name="innerException">The exception that is the cause of the current exception.</param>
        public SerializationException(string message, Exception? innerException) : base(message, innerException)
        {
            ErrorCode = SerializationErrorCode.Unknown;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="SerializationException"/> class with a specified error message
        /// and error code.
        /// </summary>
        /// <param name="message">The message that describes the error.</param>
        /// <param name="errorCode">The error code that categorizes the error.</param>
        public SerializationException(string message, SerializationErrorCode errorCode) : base(message)
        {
            ErrorCode = errorCode;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="SerializationException"/> class with a specified error message,
        /// error code, and a reference to the inner exception that is the cause of this exception.
        /// </summary>
        /// <param name="message">The message that describes the error.</param>
        /// <param name="errorCode">The error code that categorizes the error.</param>
        /// <param name="innerException">The exception that is the cause of the current exception.</param>
        public SerializationException(string message, SerializationErrorCode errorCode, Exception? innerException) : base(message, innerException)
        {
            ErrorCode = errorCode;
        }
    }
}