using System;
using System.Buffers;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Text.Json;
using System.Threading;

namespace USDP2
{
    /// <summary>
    /// Manages pooled resources for serialization operations to reduce GC pressure and improve performance.
    /// 
    /// This class provides:
    /// - Pooled byte arrays for CBOR serialization
    /// - Pooled StringBuilder instances for JSON operations
    /// - Pooled MemoryStream instances for binary operations
    /// - Cached JsonSerializerOptions instances
    /// - Thread-safe resource management
    /// </summary>
    public static class SerializationPoolManager
    {
        #region Pool Configuration

        /// <summary>
        /// Configuration for pool sizes and limits.
        /// </summary>
        public static class PoolConfig
        {
            /// <summary>
            /// Maximum number of byte arrays to keep in the pool.
            /// </summary>
            public const int MaxByteArrayPoolSize = 100;

            /// <summary>
            /// Maximum number of StringBuilder instances to keep in the pool.
            /// </summary>
            public const int MaxStringBuilderPoolSize = 50;

            /// <summary>
            /// Maximum number of MemoryStream instances to keep in the pool.
            /// </summary>
            public const int MaxMemoryStreamPoolSize = 50;

            /// <summary>
            /// Default initial capacity for StringBuilder instances.
            /// </summary>
            public const int DefaultStringBuilderCapacity = 1024;

            /// <summary>
            /// Maximum capacity for StringBuilder instances before they're discarded.
            /// </summary>
            public const int MaxStringBuilderCapacity = 64 * 1024; // 64KB

            /// <summary>
            /// Default initial capacity for MemoryStream instances.
            /// </summary>
            public const int DefaultMemoryStreamCapacity = 2048;

            /// <summary>
            /// Maximum capacity for MemoryStream instances before they're discarded.
            /// </summary>
            public const int MaxMemoryStreamCapacity = 128 * 1024; // 128KB
        }

        #endregion

        #region Pooled Resources

        /// <summary>
        /// Pool for byte arrays used in CBOR serialization.
        /// </summary>
        private static readonly ArrayPool<byte> _byteArrayPool = ArrayPool<byte>.Shared;

        /// <summary>
        /// Pool for StringBuilder instances used in JSON operations.
        /// </summary>
        private static readonly ConcurrentQueue<StringBuilder> _stringBuilderPool = new();

        /// <summary>
        /// Pool for MemoryStream instances used in binary operations.
        /// </summary>
        private static readonly ConcurrentQueue<MemoryStream> _memoryStreamPool = new();

        /// <summary>
        /// Current count of StringBuilder instances in the pool.
        /// </summary>
        private static int _stringBuilderPoolCount = 0;

        /// <summary>
        /// Current count of MemoryStream instances in the pool.
        /// </summary>
        private static int _memoryStreamPoolCount = 0;

        /// <summary>
        /// Cached JsonSerializerOptions for different scenarios.
        /// </summary>
        private static readonly Lazy<JsonSerializerOptions> _defaultJsonOptions = new(() => new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = false,
            DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull
        });

        /// <summary>
        /// Cached JsonSerializerOptions for pretty-printed JSON.
        /// </summary>
        private static readonly Lazy<JsonSerializerOptions> _indentedJsonOptions = new(() => new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = true,
            DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull
        });

        #endregion

        #region Public API

        /// <summary>
        /// Gets the default JsonSerializerOptions instance.
        /// </summary>
        public static JsonSerializerOptions DefaultJsonOptions => _defaultJsonOptions.Value;

        /// <summary>
        /// Gets the indented JsonSerializerOptions instance.
        /// </summary>
        public static JsonSerializerOptions IndentedJsonOptions => _indentedJsonOptions.Value;

        /// <summary>
        /// Rents a byte array from the pool.
        /// </summary>
        /// <param name="minimumLength">The minimum length of the array needed.</param>
        /// <returns>A rented byte array that must be returned to the pool.</returns>
        public static byte[] RentByteArray(int minimumLength)
        {
            return _byteArrayPool.Rent(minimumLength);
        }

        /// <summary>
        /// Returns a byte array to the pool.
        /// </summary>
        /// <param name="array">The array to return to the pool.</param>
        /// <param name="clearArray">Whether to clear the array before returning it.</param>
        public static void ReturnByteArray(byte[] array, bool clearArray = true)
        {
            _byteArrayPool.Return(array, clearArray);
        }

        /// <summary>
        /// Rents a StringBuilder from the pool or creates a new one.
        /// </summary>
        /// <returns>A StringBuilder instance that should be returned to the pool when done.</returns>
        public static StringBuilder RentStringBuilder()
        {
            if (_stringBuilderPool.TryDequeue(out var sb))
            {
                Interlocked.Decrement(ref _stringBuilderPoolCount);
                sb.Clear(); // Ensure it's clean
                return sb;
            }

            return new StringBuilder(PoolConfig.DefaultStringBuilderCapacity);
        }

        /// <summary>
        /// Returns a StringBuilder to the pool if it's within size limits.
        /// </summary>
        /// <param name="stringBuilder">The StringBuilder to return.</param>
        public static void ReturnStringBuilder(StringBuilder stringBuilder)
        {
            if (stringBuilder == null) return;

            // Only return to pool if it's not too large and we haven't exceeded pool size
            if (stringBuilder.Capacity <= PoolConfig.MaxStringBuilderCapacity &&
                _stringBuilderPoolCount < PoolConfig.MaxStringBuilderPoolSize)
            {
                stringBuilder.Clear();
                _stringBuilderPool.Enqueue(stringBuilder);
                Interlocked.Increment(ref _stringBuilderPoolCount);
            }
        }

        /// <summary>
        /// Rents a MemoryStream from the pool or creates a new one.
        /// </summary>
        /// <returns>A MemoryStream instance that should be returned to the pool when done.</returns>
        public static MemoryStream RentMemoryStream()
        {
            if (_memoryStreamPool.TryDequeue(out var stream))
            {
                Interlocked.Decrement(ref _memoryStreamPoolCount);
                stream.SetLength(0); // Reset to empty
                stream.Position = 0;
                return stream;
            }

            return new MemoryStream(PoolConfig.DefaultMemoryStreamCapacity);
        }

        /// <summary>
        /// Returns a MemoryStream to the pool if it's within size limits.
        /// </summary>
        /// <param name="memoryStream">The MemoryStream to return.</param>
        public static void ReturnMemoryStream(MemoryStream memoryStream)
        {
            if (memoryStream == null) return;

            // Only return to pool if it's not too large and we haven't exceeded pool size
            if (memoryStream.Capacity <= PoolConfig.MaxMemoryStreamCapacity &&
                _memoryStreamPoolCount < PoolConfig.MaxMemoryStreamPoolSize)
            {
                memoryStream.SetLength(0);
                memoryStream.Position = 0;
                _memoryStreamPool.Enqueue(memoryStream);
                Interlocked.Increment(ref _memoryStreamPoolCount);
            }
            else
            {
                memoryStream.Dispose();
            }
        }

        /// <summary>
        /// Gets statistics about the current pool usage.
        /// </summary>
        /// <returns>Pool usage statistics.</returns>
        public static PoolStatistics GetStatistics()
        {
            return new PoolStatistics
            {
                StringBuilderPoolCount = _stringBuilderPoolCount,
                MemoryStreamPoolCount = _memoryStreamPoolCount,
                StringBuilderPoolLimit = PoolConfig.MaxStringBuilderPoolSize,
                MemoryStreamPoolLimit = PoolConfig.MaxMemoryStreamPoolSize
            };
        }

        #endregion
    }

    /// <summary>
    /// Statistics about pool usage.
    /// </summary>
    public class PoolStatistics
    {
        /// <summary>
        /// Current number of StringBuilder instances in the pool.
        /// </summary>
        public int StringBuilderPoolCount { get; set; }

        /// <summary>
        /// Current number of MemoryStream instances in the pool.
        /// </summary>
        public int MemoryStreamPoolCount { get; set; }

        /// <summary>
        /// Maximum number of StringBuilder instances allowed in the pool.
        /// </summary>
        public int StringBuilderPoolLimit { get; set; }

        /// <summary>
        /// Maximum number of MemoryStream instances allowed in the pool.
        /// </summary>
        public int MemoryStreamPoolLimit { get; set; }

        /// <summary>
        /// StringBuilder pool utilization percentage.
        /// </summary>
        public double StringBuilderUtilization => StringBuilderPoolLimit > 0 ? (double)StringBuilderPoolCount / StringBuilderPoolLimit * 100 : 0;

        /// <summary>
        /// MemoryStream pool utilization percentage.
        /// </summary>
        public double MemoryStreamUtilization => MemoryStreamPoolLimit > 0 ? (double)MemoryStreamPoolCount / MemoryStreamPoolLimit * 100 : 0;
    }
}
