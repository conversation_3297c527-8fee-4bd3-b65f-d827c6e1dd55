using System;
using System.Net.Http;
using System.Net.Security;
using System.Security.Authentication;
using System.Threading;
using System.Threading.Tasks;

namespace USDP2
{
    /// <summary>
    /// Manages TLS/SSL configuration with OS delegation and fallback mechanisms.
    ///
    /// This class provides the core TLS management functionality for USDP2, implementing
    /// OS-managed cipher suites and TLS versions by default, with configurable fallback
    /// mechanisms for enhanced compatibility. It serves as the primary TLS configuration
    /// provider and is always present in the core functionality.
    /// </summary>
    public class TlsConfigurationManager : IDisposable
    {
        private readonly UsdpConfiguration _config;
        private HttpClient? _primaryHttpClient;

        /// <summary>
        /// Initializes a new instance of the TlsConfigurationManager class.
        /// </summary>
        /// <param name="config">The USDP configuration instance. If null, uses the global instance.</param>
        public TlsConfigurationManager(UsdpConfiguration? config = null)
        {
            _config = config ?? UsdpConfiguration.Instance;
        }

        /// <summary>
        /// Creates an HttpClient configured with OS-managed TLS settings.
        /// 
        /// This method creates an HttpClient that delegates TLS/SSL configuration
        /// to the operating system, providing optimal security and compatibility.
        /// The OS manages cipher suite selection, TLS version negotiation, and
        /// security policy enforcement.
        /// </summary>
        /// <returns>An HttpClient configured for OS-managed TLS.</returns>
        public HttpClient CreateHttpClient()
        {
            if (_primaryHttpClient != null)
            {
                return _primaryHttpClient;
            }

            var handler = CreateHttpClientHandler();
            _primaryHttpClient = new HttpClient(handler);

            // Configure standard HTTP client settings
            _primaryHttpClient.Timeout = _config.NetworkTimeout;

            Diagnostics.Log("TlsConfiguration", new
            {
                Message = "Created HttpClient with OS-managed TLS configuration",
                UseOSManagedTls = _config.UseOSManagedTls,
                EnableTlsFallback = _config.EnableTlsFallback
            });

            return _primaryHttpClient;
        }

        /// <summary>
        /// Creates an HttpClientHandler with appropriate TLS configuration.
        /// 
        /// This method configures the HttpClientHandler to use OS-managed TLS
        /// settings when UseOSManagedTls is enabled, or falls back to .NET
        /// defaults when disabled.
        /// </summary>
        /// <returns>A configured HttpClientHandler.</returns>
        private HttpClientHandler CreateHttpClientHandler()
        {
            var handler = new HttpClientHandler();

            if (_config.UseOSManagedTls)
            {
                // Configure for OS-managed TLS
                // SslProtocols.None tells .NET to use the OS default TLS configuration
                handler.SslProtocols = SslProtocols.None;

                // Let the OS handle certificate validation and cipher suite selection
                handler.ServerCertificateCustomValidationCallback = null;

                Diagnostics.Log("TlsConfiguration", new
                {
                    Message = "Configured HttpClientHandler for OS-managed TLS",
                    SslProtocols = "OS Default",
                    CipherSuites = "OS Managed"
                });
            }
            else
            {
                // Use .NET defaults when OS management is disabled
                // This allows for potential manual override through TlsOverrideProvider
                handler.SslProtocols = SslProtocols.Tls12 | SslProtocols.Tls13;

                Diagnostics.Log("TlsConfiguration", new
                {
                    Message = "Configured HttpClientHandler with .NET defaults",
                    SslProtocols = "TLS 1.2, TLS 1.3",
                    CipherSuites = ".NET Default"
                });
            }

            // Configure additional security settings
            handler.UseCookies = false;
            handler.UseDefaultCredentials = false;
            handler.CheckCertificateRevocationList = true;

            return handler;
        }

        /// <summary>
        /// Attempts to send an HTTP request with fallback mechanisms.
        /// 
        /// This method implements graceful TLS fallback by attempting the request
        /// with the primary configuration first, then falling back to alternative
        /// TLS configurations if the initial attempt fails and fallback is enabled.
        /// </summary>
        /// <param name="request">The HTTP request message to send.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>The HTTP response message.</returns>
        public async Task<HttpResponseMessage> SendWithFallbackAsync(
            HttpRequestMessage request,
            CancellationToken cancellationToken = default)
        {
            var primaryClient = CreateHttpClient();

            try
            {
                // Attempt with primary (OS-managed) configuration
                var response = await primaryClient.SendAsync(request, cancellationToken).ConfigureAwait(false);

                Diagnostics.Log("TlsConnection", new
                {
                    Message = "Successful connection with primary TLS configuration",
                    Url = request.RequestUri?.ToString(),
                    StatusCode = response.StatusCode
                });

                return response;
            }
            catch (HttpRequestException ex) when (_config.EnableTlsFallback && IsTlsRelatedError(ex))
            {
                Diagnostics.Log("TlsFallback", new
                {
                    Message = "Primary TLS connection failed, attempting fallback",
                    Url = request.RequestUri?.ToString(),
                    Error = ex.Message,
                    FallbackEnabled = _config.EnableTlsFallback
                });

                return await AttemptFallbackConnectionAsync(request, cancellationToken).ConfigureAwait(false);
            }
        }

        /// <summary>
        /// Attempts fallback connections with alternative TLS configurations.
        ///
        /// This method tries each TLS version specified in FallbackTlsVersions
        /// in order, respecting the AllowTlsDowngrade setting for security boundaries.
        /// </summary>
        /// <param name="request">The HTTP request message to send.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>The HTTP response message.</returns>
        private async Task<HttpResponseMessage> AttemptFallbackConnectionAsync(
            HttpRequestMessage request,
            CancellationToken cancellationToken)
        {
            foreach (var tlsVersion in _config.FallbackTlsVersions)
            {
                // Check if this TLS version is allowed based on security policy
                if (!IsTlsVersionAllowed(tlsVersion))
                {
                    Diagnostics.Log("TlsFallback", new
                    {
                        Message = "Skipping TLS version due to security policy",
                        TlsVersion = tlsVersion,
                        AllowTlsDowngrade = _config.AllowTlsDowngrade
                    });
                    continue;
                }

                try
                {
                    // Create a new request for each fallback attempt to avoid reuse issues
                    var fallbackRequest = new HttpRequestMessage(request.Method, request.RequestUri);
                    if (request.Content != null)
                    {
                        // Clone the content for the fallback request
                        var contentBytes = await request.Content.ReadAsByteArrayAsync(cancellationToken);
                        fallbackRequest.Content = new ByteArrayContent(contentBytes);

                        // Copy content headers if they exist
                        if (request.Content.Headers != null)
                        {
                            foreach (var header in request.Content.Headers)
                            {
                                fallbackRequest.Content.Headers.TryAddWithoutValidation(header.Key, header.Value);
                            }
                        }
                    }

                    // Copy request headers
                    foreach (var header in request.Headers)
                    {
                        fallbackRequest.Headers.TryAddWithoutValidation(header.Key, header.Value);
                    }

                    var fallbackClient = CreateFallbackHttpClient(tlsVersion);
                    var response = await fallbackClient.SendAsync(fallbackRequest, cancellationToken).ConfigureAwait(false);

                    Diagnostics.Log("TlsFallback", new
                    {
                        Message = "Successful fallback connection",
                        Url = request.RequestUri?.ToString(),
                        TlsVersion = tlsVersion,
                        StatusCode = response.StatusCode
                    });

                    return response;
                }
                catch (HttpRequestException ex)
                {
                    Diagnostics.Log("TlsFallback", new
                    {
                        Message = "Fallback attempt failed",
                        TlsVersion = tlsVersion,
                        Error = ex.Message
                    });

                    // Continue to next TLS version
                }
            }

            // All fallback attempts failed
            throw new InvalidOperationException(
                "All TLS fallback attempts failed. Check network connectivity and TLS configuration.");
        }

        /// <summary>
        /// Creates an HttpClient configured for a specific TLS version fallback.
        /// </summary>
        /// <param name="tlsVersion">The TLS version to use for this client.</param>
        /// <returns>An HttpClient configured for the specified TLS version.</returns>
        private HttpClient CreateFallbackHttpClient(string tlsVersion)
        {
            var handler = new HttpClientHandler();

            // Configure specific TLS version for fallback
            handler.SslProtocols = tlsVersion switch
            {
                "1.3" => SslProtocols.Tls13,
                "1.2" => SslProtocols.Tls12,
#pragma warning disable SYSLIB0039 // TLS 1.0 and 1.1 are deprecated but supported for legacy compatibility when explicitly configured
                "1.1" => SslProtocols.Tls11,
                "1.0" => SslProtocols.Tls,
#pragma warning restore SYSLIB0039
                _ => SslProtocols.Tls12 // Default to TLS 1.2 for unknown versions
            };

            handler.UseCookies = false;
            handler.UseDefaultCredentials = false;
            handler.CheckCertificateRevocationList = true;

            var client = new HttpClient(handler);
            client.Timeout = _config.NetworkTimeout;

            return client;
        }

        /// <summary>
        /// Determines if an exception is related to TLS/SSL configuration issues.
        /// </summary>
        /// <param name="ex">The exception to examine.</param>
        /// <returns>True if the exception appears to be TLS-related.</returns>
        private static bool IsTlsRelatedError(HttpRequestException ex)
        {
            var message = ex.Message.ToLowerInvariant();
            return message.Contains("ssl") ||
                   message.Contains("tls") ||
                   message.Contains("certificate") ||
                   message.Contains("handshake") ||
                   message.Contains("protocol");
        }

        /// <summary>
        /// Checks if a TLS version is allowed based on current security policy.
        /// </summary>
        /// <param name="tlsVersion">The TLS version to check.</param>
        /// <returns>True if the TLS version is allowed.</returns>
        private bool IsTlsVersionAllowed(string tlsVersion)
        {
            // Check for deprecated TLS versions (1.0 and 1.1)
            bool isDeprecatedVersion = tlsVersion == "1.0" || tlsVersion == "1.1";

            if (isDeprecatedVersion && !_config.EnableLowSecurityTlsFallback)
            {
                // Deprecated TLS versions are not allowed unless explicitly enabled
                Diagnostics.Log("TlsSecurityPolicy", new
                {
                    Message = "Deprecated TLS version blocked by security policy",
                    TlsVersion = tlsVersion,
                    EnableLowSecurityTlsFallback = _config.EnableLowSecurityTlsFallback,
                    SecurityRisk = "TLS 1.0/1.1 have known vulnerabilities"
                });
                return false;
            }

            if (isDeprecatedVersion && _config.EnableLowSecurityTlsFallback)
            {
                // Log security warning when deprecated versions are used
                Diagnostics.Log("TlsSecurityWarning", new
                {
                    Message = "Using deprecated TLS version - security risk",
                    TlsVersion = tlsVersion,
                    SecurityRisk = "TLS 1.0/1.1 have known vulnerabilities",
                    Recommendation = "Upgrade to TLS 1.2 or higher as soon as possible"
                });
            }

            if (_config.AllowTlsDowngrade)
            {
                return true; // All versions allowed when downgrade is permitted
            }

            // When downgrade is not allowed, only permit TLS 1.2 and higher
            return tlsVersion == "1.3" || tlsVersion == "1.2";
        }

        /// <summary>
        /// Disposes of managed resources.
        /// </summary>
        public void Dispose()
        {
            _primaryHttpClient?.Dispose();
            GC.SuppressFinalize(this);
        }
    }
}
