using Microsoft.Extensions.Logging;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Linq;

namespace USDP2.Tests
{
    /// <summary>
    /// Tests for the UsdpLogger Microsoft.Extensions.Logging integration.
    /// 
    /// These tests verify that the new logging system works correctly and provides
    /// backward compatibility with the existing Diagnostics.Log system.
    /// </summary>
    [TestClass]
    public class UsdpLoggerTests
    {
        /// <summary>
        /// Test logger provider that captures log entries for verification.
        /// </summary>
        private class TestLoggerProvider : ILoggerProvider
        {
            public List<LogEntry> LogEntries { get; } = new();

            public ILogger CreateLogger(string categoryName)
            {
                return new TestLogger(categoryName, LogEntries);
            }

            public void Dispose()
            {
                LogEntries.Clear();
            }
        }

        /// <summary>
        /// Test logger that captures log entries.
        /// </summary>
        private class TestLogger : ILogger
        {
            private readonly string _categoryName;
            private readonly List<LogEntry> _logEntries;

            public TestLogger(string categoryName, List<LogEntry> logEntries)
            {
                _categoryName = categoryName;
                _logEntries = logEntries;
            }

            public IDisposable BeginScope<TState>(TState state) => null!;

            public bool IsEnabled(LogLevel logLevel) => true;

            public void Log<TState>(LogLevel logLevel, EventId eventId, TState state, Exception? exception, Func<TState, Exception?, string> formatter)
            {
                _logEntries.Add(new LogEntry
                {
                    CategoryName = _categoryName,
                    LogLevel = logLevel,
                    EventId = eventId,
                    State = state,
                    Exception = exception,
                    Message = formatter(state, exception)
                });
            }
        }

        /// <summary>
        /// Represents a captured log entry.
        /// </summary>
        private class LogEntry
        {
            public string CategoryName { get; set; } = string.Empty;
            public LogLevel LogLevel { get; set; }
            public EventId EventId { get; set; }
            public object? State { get; set; }
            public Exception? Exception { get; set; }
            public string Message { get; set; } = string.Empty;
        }

        private TestLoggerProvider? _testProvider;

        [TestInitialize]
        public void TestInitialize()
        {
            // Create a test logger provider
            _testProvider = new TestLoggerProvider();

            // Create a logger factory with our test provider
            var loggerFactory = LoggerFactory.Create(builder =>
            {
                builder.SetMinimumLevel(LogLevel.Trace);
                builder.AddProvider(_testProvider);
            });

            // Initialize UsdpLogger with the test factory
            UsdpLogger.Initialize(loggerFactory);
            UsdpLogger.EnableMetrics = true;
            UsdpLogger.MinimumLogLevel = LogLevel.Trace;
        }

        [TestCleanup]
        public void TestCleanup()
        {
            UsdpLogger.Dispose();
            _testProvider?.Dispose();
        }

        [TestMethod]
        public void UsdpLogger_Log_CreatesLogEntry()
        {
            // Arrange
            var eventType = "TestEvent";
            var data = new { Message = "Test message", Value = 42 };

            // Act
            UsdpLogger.Log(eventType, data);

            // Assert
            Assert.IsNotNull(_testProvider);
            Assert.AreEqual(1, _testProvider.LogEntries.Count);

            var logEntry = _testProvider.LogEntries[0];
            Assert.AreEqual("USDP2.General", logEntry.CategoryName);
            Assert.AreEqual(LogLevel.Information, logEntry.LogLevel);
            Assert.IsTrue(logEntry.Message.Contains(eventType));
        }

        [TestMethod]
        public void UsdpLogger_Log_AutoDetectsErrorLevel()
        {
            // Arrange
            var eventType = "NetworkConnectionError";
            var data = new { Error = "Connection failed" };

            // Act
            UsdpLogger.Log(eventType, data);

            // Assert
            Assert.IsNotNull(_testProvider);
            Assert.AreEqual(1, _testProvider.LogEntries.Count);

            var logEntry = _testProvider.LogEntries[0];
            Assert.AreEqual(LogLevel.Error, logEntry.LogLevel);
        }

        [TestMethod]
        public void UsdpLogger_Log_AutoDetectsWarningLevel()
        {
            // Arrange
            var eventType = "ConnectionTimeout";
            var data = new { Timeout = "30 seconds" };

            // Act
            UsdpLogger.Log(eventType, data);

            // Assert
            Assert.IsNotNull(_testProvider);
            Assert.AreEqual(1, _testProvider.LogEntries.Count);

            var logEntry = _testProvider.LogEntries[0];
            Assert.AreEqual(LogLevel.Warning, logEntry.LogLevel);
        }

        [TestMethod]
        public void UsdpLogger_Log_AutoDetectsDebugLevel()
        {
            // Arrange
            var eventType = "DebugTrace";
            var data = new { Step = "Processing request" };

            // Act
            UsdpLogger.Log(eventType, data);

            // Assert
            Assert.IsNotNull(_testProvider);
            Assert.AreEqual(1, _testProvider.LogEntries.Count);

            var logEntry = _testProvider.LogEntries[0];
            Assert.AreEqual(LogLevel.Debug, logEntry.LogLevel);
        }

        [TestMethod]
        public void UsdpLogger_Log_DeterminesCorrectCategory()
        {
            // Arrange & Act
            UsdpLogger.Log("BloomFilter.ServiceAdded", new { ServiceId = "test" });
            UsdpLogger.Log("TlsConnectionEstablished", new { Host = "example.com" });
            UsdpLogger.Log("NetworkPacketReceived", new { Size = 1024 });

            // Assert
            Assert.IsNotNull(_testProvider);
            Assert.AreEqual(3, _testProvider.LogEntries.Count);

            Assert.AreEqual("USDP2.BloomFilter", _testProvider.LogEntries[0].CategoryName);
            Assert.AreEqual("USDP2.Tls", _testProvider.LogEntries[1].CategoryName);
            Assert.AreEqual("USDP2.Network", _testProvider.LogEntries[2].CategoryName);
        }

        [TestMethod]
        public void UsdpLogger_GetMetrics_TracksEventCounts()
        {
            // Arrange
            UsdpLogger.EnableMetrics = true;
            UsdpLogger.ClearMetrics();

            // Act
            UsdpLogger.Log("TestEvent1", null);
            UsdpLogger.Log("TestEvent1", null);
            UsdpLogger.Log("TestEvent2", null);

            // Assert
            var metrics = UsdpLogger.GetMetrics();
            Assert.AreEqual(2, metrics.Count);
            Assert.AreEqual(2, metrics["TestEvent1"]);
            Assert.AreEqual(1, metrics["TestEvent2"]);
        }

        [TestMethod]
        public void UsdpLogger_GetLogger_ReturnsTypedLogger()
        {
            // Act
            var logger = UsdpLogger.GetLogger<UsdpLoggerTests>();

            // Assert
            Assert.IsNotNull(logger);
            Assert.IsInstanceOfType(logger, typeof(ILogger<UsdpLoggerTests>));
        }

        [TestMethod]
        public void UsdpLogger_GetLogger_ReturnsCategoryLogger()
        {
            // Act
            var logger = UsdpLogger.GetLogger("TestCategory");

            // Assert
            Assert.IsNotNull(logger);
            Assert.IsInstanceOfType(logger, typeof(ILogger));
        }

        [TestMethod]
        public void Diagnostics_Log_DelegatesToUsdpLogger()
        {
            // Arrange
            var eventType = "DiagnosticsTest";
            var data = new { Message = "Testing backward compatibility" };

            // Act
            Diagnostics.Log(eventType, data);

            // Assert
            Assert.IsNotNull(_testProvider);
            Assert.IsTrue(_testProvider.LogEntries.Count >= 1);

            var logEntry = _testProvider.LogEntries.FirstOrDefault(e => e.Message.Contains(eventType));
            Assert.IsNotNull(logEntry);
            Assert.AreEqual("USDP2.General", logEntry.CategoryName);
        }

        [TestMethod]
        public void UsdpLogger_MinimumLogLevel_FiltersEvents()
        {
            // Arrange
            UsdpLogger.MinimumLogLevel = LogLevel.Warning;

            // Act
            UsdpLogger.Log("DebugEvent", null); // Should be filtered
            UsdpLogger.Log("InfoEvent", null);  // Should be filtered
            UsdpLogger.Log("WarningEvent", null); // Should pass
            UsdpLogger.Log("ErrorEvent", null);   // Should pass

            // Assert
            Assert.IsNotNull(_testProvider);
            
            // Note: The test provider captures all events, but UsdpLogger should filter based on MinimumLogLevel
            // In a real scenario, the filtering would happen at the logger factory level
            var warningEvents = _testProvider.LogEntries.Where(e => e.LogLevel >= LogLevel.Warning).ToList();
            Assert.IsTrue(warningEvents.Count >= 2);
        }

        [TestMethod]
        public void UsdpLogger_HandleNullData_DoesNotThrow()
        {
            // Act & Assert - Should not throw
            UsdpLogger.Log("TestEventWithNullData", null);

            // Verify log entry was created
            Assert.IsNotNull(_testProvider);
            Assert.IsTrue(_testProvider.LogEntries.Count >= 1);
        }

        [TestMethod]
        public void UsdpLogger_HandleComplexData_SerializesCorrectly()
        {
            // Arrange
            var complexData = new
            {
                ServiceId = "home/lighting/bulb1",
                Endpoints = new[] { "http://192.168.1.100:8080", "https://192.168.1.100:8443" },
                Metadata = new Dictionary<string, object>
                {
                    ["brightness"] = 75,
                    ["color"] = "warm_white",
                    ["power_consumption"] = 9.5
                }
            };

            // Act
            UsdpLogger.Log("ServiceDiscovered", complexData);

            // Assert
            Assert.IsNotNull(_testProvider);
            Assert.AreEqual(1, _testProvider.LogEntries.Count);

            var logEntry = _testProvider.LogEntries[0];
            Assert.IsTrue(logEntry.Message.Contains("ServiceDiscovered"));
            Assert.IsTrue(logEntry.Message.Contains("home/lighting/bulb1"));
        }
    }
}
