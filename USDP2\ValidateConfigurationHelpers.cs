using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Text.RegularExpressions;

namespace USDP2
{
    /// <summary>
    /// Helper methods for configuration validation.
    /// </summary>
    public static partial class ValidateConfiguration
    {
        /// <summary>
        /// Additional invalid characters for path validation.
        /// </summary>
        private static readonly char[] AdditionalInvalidPathChars = { '?', '#', ' ' };
        /// <summary>
        /// Validates an IP address.
        /// </summary>
        private static ValidationResult ValidateIpAddress(string address, string propertyName)
        {
            if (string.IsNullOrWhiteSpace(address))
            {
                return new ValidationResult
                {
                    Severity = ValidationSeverity.Critical,
                    PropertyName = propertyName,
                    Message = "IP address cannot be null or empty",
                    CurrentValue = address,
                    RecommendedAction = "Set a valid IPv4 or IPv6 address"
                };
            }

            if (!IPAddress.TryParse(address, out var ipAddress))
            {
                return new ValidationResult
                {
                    Severity = ValidationSeverity.Critical,
                    PropertyName = propertyName,
                    Message = "Invalid IP address format",
                    CurrentValue = address,
                    RecommendedAction = "Use a valid IP address format (e.g., 127.0.0.1 or ::1)"
                };
            }

            // Check for localhost
            if (IPAddress.IsLoopback(ipAddress))
            {
                return new ValidationResult
                {
                    Severity = ValidationSeverity.Info,
                    PropertyName = propertyName,
                    Message = "Using localhost address - service will only be accessible locally",
                    CurrentValue = address,
                    RecommendedAction = "Use 0.0.0.0 to bind to all interfaces or a specific IP for network access"
                };
            }

            return new ValidationResult
            {
                Severity = ValidationSeverity.Info,
                PropertyName = propertyName,
                Message = "IP address is valid",
                CurrentValue = address
            };
        }

        /// <summary>
        /// Validates a port number.
        /// </summary>
        private static ValidationResult ValidatePort(int port, string propertyName)
        {
            if (port < 1 || port > 65535)
            {
                return new ValidationResult
                {
                    Severity = ValidationSeverity.Critical,
                    PropertyName = propertyName,
                    Message = "Port number must be between 1 and 65535",
                    CurrentValue = port,
                    RecommendedAction = "Use a valid port number in the range 1-65535"
                };
            }

            // Check for well-known ports
            if (port < 1024)
            {
                return new ValidationResult
                {
                    Severity = ValidationSeverity.Warning,
                    PropertyName = propertyName,
                    Message = "Using well-known port (< 1024) may require elevated privileges",
                    CurrentValue = port,
                    RecommendedAction = "Consider using a port above 1024 or ensure the application runs with appropriate privileges"
                };
            }

            return new ValidationResult
            {
                Severity = ValidationSeverity.Info,
                PropertyName = propertyName,
                Message = "Port number is valid",
                CurrentValue = port
            };
        }

        /// <summary>
        /// Validates port conflicts between different services.
        /// </summary>
        private static ValidationResult[] ValidatePortConflicts(UsdpConfiguration config)
        {
            var results = new List<ValidationResult>();

            // Create a list of port/name pairs to avoid dictionary key conflicts
            var portMappings = new List<(int Port, string Name)>
            {
                (config.DefaultMulticastPort, nameof(config.DefaultMulticastPort)),
                (config.DefaultHttpPort, nameof(config.DefaultHttpPort)),
                (config.DefaultHttpsPort, nameof(config.DefaultHttpsPort)),
                (config.DefaultServicePort, nameof(config.DefaultServicePort))
            };

            var duplicates = portMappings.GroupBy(mapping => mapping.Port)
                .Where(g => g.Count() > 1)
                .ToList();

            foreach (var duplicate in duplicates)
            {
                var conflictingProperties = string.Join(", ", duplicate.Select(d => d.Name));
                results.Add(new ValidationResult
                {
                    Severity = ValidationSeverity.Error,
                    PropertyName = "PortConflict",
                    Message = $"Port {duplicate.Key} is used by multiple services",
                    CurrentValue = $"Port {duplicate.Key}: {conflictingProperties}",
                    RecommendedAction = "Assign unique port numbers to each service"
                });
            }

            return results.ToArray();
        }

        /// <summary>
        /// Validates an endpoint path.
        /// </summary>
        private static ValidationResult ValidateEndpointPath(string path, string propertyName)
        {
            if (string.IsNullOrWhiteSpace(path))
            {
                return new ValidationResult
                {
                    Severity = ValidationSeverity.Critical,
                    PropertyName = propertyName,
                    Message = "Endpoint path cannot be null or empty",
                    CurrentValue = path,
                    RecommendedAction = "Set a valid endpoint path starting with '/'"
                };
            }

            if (!path.StartsWith("/", StringComparison.Ordinal))
            {
                return new ValidationResult
                {
                    Severity = ValidationSeverity.Error,
                    PropertyName = propertyName,
                    Message = "Endpoint path must start with '/'",
                    CurrentValue = path,
                    RecommendedAction = "Prefix the path with '/' (e.g., '/usdp')"
                };
            }

            // Check for invalid characters
            var invalidChars = Path.GetInvalidFileNameChars().Concat(AdditionalInvalidPathChars).ToArray();
            if (path.Skip(1).Any(c => invalidChars.Contains(c)))
            {
                return new ValidationResult
                {
                    Severity = ValidationSeverity.Warning,
                    PropertyName = propertyName,
                    Message = "Endpoint path contains potentially problematic characters",
                    CurrentValue = path,
                    RecommendedAction = "Use only alphanumeric characters, hyphens, and underscores in paths"
                };
            }

            return new ValidationResult
            {
                Severity = ValidationSeverity.Info,
                PropertyName = propertyName,
                Message = "Endpoint path is valid",
                CurrentValue = path
            };
        }

        /// <summary>
        /// Validates response port offset.
        /// </summary>
        private static ValidationResult ValidateResponsePortOffset(int offset)
        {
            if (offset < 1 || offset > 1000)
            {
                return new ValidationResult
                {
                    Severity = ValidationSeverity.Error,
                    PropertyName = nameof(UsdpConfiguration.ResponsePortOffset),
                    Message = "Response port offset must be between 1 and 1000",
                    CurrentValue = offset,
                    RecommendedAction = "Use a small positive offset (typically 1-10)"
                };
            }

            return new ValidationResult
            {
                Severity = ValidationSeverity.Info,
                PropertyName = nameof(UsdpConfiguration.ResponsePortOffset),
                Message = "Response port offset is valid",
                CurrentValue = offset
            };
        }

        /// <summary>
        /// Validates a timeout value.
        /// </summary>
        private static ValidationResult ValidateTimeout(TimeSpan timeout, string propertyName, TimeSpan minValue, TimeSpan maxValue)
        {
            if (timeout < TimeSpan.Zero)
            {
                return new ValidationResult
                {
                    Severity = ValidationSeverity.Critical,
                    PropertyName = propertyName,
                    Message = "Timeout cannot be negative",
                    CurrentValue = timeout,
                    RecommendedAction = $"Set a positive timeout value between {minValue} and {maxValue}"
                };
            }

            if (timeout < minValue)
            {
                return new ValidationResult
                {
                    Severity = ValidationSeverity.Warning,
                    PropertyName = propertyName,
                    Message = $"Timeout is below recommended minimum of {minValue}",
                    CurrentValue = timeout,
                    RecommendedAction = $"Consider increasing to at least {minValue} for reliable operation"
                };
            }

            if (timeout > maxValue)
            {
                return new ValidationResult
                {
                    Severity = ValidationSeverity.Warning,
                    PropertyName = propertyName,
                    Message = $"Timeout exceeds recommended maximum of {maxValue}",
                    CurrentValue = timeout,
                    RecommendedAction = $"Consider reducing to {maxValue} or less for better responsiveness"
                };
            }

            return new ValidationResult
            {
                Severity = ValidationSeverity.Info,
                PropertyName = propertyName,
                Message = "Timeout value is valid",
                CurrentValue = timeout
            };
        }

        /// <summary>
        /// Validates a security protocol string.
        /// </summary>
        private static ValidationResult ValidateSecurityProtocol(string protocol)
        {
            if (string.IsNullOrWhiteSpace(protocol))
            {
                return new ValidationResult
                {
                    Severity = ValidationSeverity.Critical,
                    PropertyName = nameof(UsdpConfiguration.DefaultSecurity),
                    Message = "Security protocol cannot be null or empty",
                    CurrentValue = protocol,
                    RecommendedAction = "Set a valid security protocol (e.g., 'psk-tls1.3', 'cert-tls1.3', or 'none')"
                };
            }

            var validProtocols = new[] { "none", "psk-tls1.3", "cert-tls1.3", "psk-tls1.2", "cert-tls1.2" };
            if (!validProtocols.Contains(protocol.ToLowerInvariant()))
            {
                return new ValidationResult
                {
                    Severity = ValidationSeverity.Warning,
                    PropertyName = nameof(UsdpConfiguration.DefaultSecurity),
                    Message = "Unknown security protocol",
                    CurrentValue = protocol,
                    RecommendedAction = $"Use one of: {string.Join(", ", validProtocols)}"
                };
            }

            if (string.Equals(protocol, "none", StringComparison.OrdinalIgnoreCase))
            {
                return new ValidationResult
                {
                    Severity = ValidationSeverity.Warning,
                    PropertyName = nameof(UsdpConfiguration.DefaultSecurity),
                    Message = "Security is disabled - communications will be unencrypted",
                    CurrentValue = protocol,
                    RecommendedAction = "Consider using 'psk-tls1.3' or 'cert-tls1.3' for secure communications"
                };
            }

            return new ValidationResult
            {
                Severity = ValidationSeverity.Info,
                PropertyName = nameof(UsdpConfiguration.DefaultSecurity),
                Message = "Security protocol is valid",
                CurrentValue = protocol
            };
        }

        /// <summary>
        /// Validates a protocol string.
        /// </summary>
        private static ValidationResult ValidateProtocol(string protocol)
        {
            if (string.IsNullOrWhiteSpace(protocol))
            {
                return new ValidationResult
                {
                    Severity = ValidationSeverity.Critical,
                    PropertyName = nameof(UsdpConfiguration.DefaultProtocol),
                    Message = "Protocol cannot be null or empty",
                    CurrentValue = protocol,
                    RecommendedAction = "Set a valid protocol (e.g., 'coap+udp', 'http', 'https')"
                };
            }

            var validProtocols = new[] { "coap+udp", "http", "https", "tcp", "udp" };
            if (!validProtocols.Contains(protocol.ToLowerInvariant()))
            {
                return new ValidationResult
                {
                    Severity = ValidationSeverity.Warning,
                    PropertyName = nameof(UsdpConfiguration.DefaultProtocol),
                    Message = "Unknown protocol",
                    CurrentValue = protocol,
                    RecommendedAction = $"Use one of: {string.Join(", ", validProtocols)}"
                };
            }

            return new ValidationResult
            {
                Severity = ValidationSeverity.Info,
                PropertyName = nameof(UsdpConfiguration.DefaultProtocol),
                Message = "Protocol is valid",
                CurrentValue = protocol
            };
        }

        /// <summary>
        /// Validates a file name.
        /// </summary>
        private static ValidationResult ValidateFileName(string fileName, string propertyName)
        {
            if (string.IsNullOrWhiteSpace(fileName))
            {
                return new ValidationResult
                {
                    Severity = ValidationSeverity.Critical,
                    PropertyName = propertyName,
                    Message = "File name cannot be null or empty",
                    CurrentValue = fileName,
                    RecommendedAction = "Set a valid file name"
                };
            }

            var invalidChars = Path.GetInvalidFileNameChars();
            if (fileName.Any(c => invalidChars.Contains(c)))
            {
                return new ValidationResult
                {
                    Severity = ValidationSeverity.Error,
                    PropertyName = propertyName,
                    Message = "File name contains invalid characters",
                    CurrentValue = fileName,
                    RecommendedAction = "Remove invalid characters from the file name"
                };
            }

            return new ValidationResult
            {
                Severity = ValidationSeverity.Info,
                PropertyName = propertyName,
                Message = "File name is valid",
                CurrentValue = fileName
            };
        }

        /// <summary>
        /// Validates a log file path.
        /// </summary>
        private static ValidationResult ValidateLogFilePath(string filePath)
        {
            try
            {
                var directory = Path.GetDirectoryName(filePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    return new ValidationResult
                    {
                        Severity = ValidationSeverity.Warning,
                        PropertyName = nameof(UsdpConfiguration.CustomLogFilePath),
                        Message = "Log file directory does not exist",
                        CurrentValue = directory,
                        RecommendedAction = "Ensure the directory exists or will be created at runtime"
                    };
                }

                return new ValidationResult
                {
                    Severity = ValidationSeverity.Info,
                    PropertyName = nameof(UsdpConfiguration.CustomLogFilePath),
                    Message = "Log file path is valid",
                    CurrentValue = filePath
                };
            }
            catch (Exception ex)
            {
                return new ValidationResult
                {
                    Severity = ValidationSeverity.Error,
                    PropertyName = nameof(UsdpConfiguration.CustomLogFilePath),
                    Message = $"Invalid log file path: {ex.Message}",
                    CurrentValue = filePath,
                    RecommendedAction = "Use a valid file path format"
                };
            }
        }

        /// <summary>
        /// Validates a buffer size.
        /// </summary>
        private static ValidationResult ValidateBufferSize(int size, string propertyName, int minSize, int maxSize)
        {
            if (size < minSize)
            {
                return new ValidationResult
                {
                    Severity = ValidationSeverity.Error,
                    PropertyName = propertyName,
                    Message = $"Buffer size is below minimum of {minSize} bytes",
                    CurrentValue = size,
                    RecommendedAction = $"Set buffer size to at least {minSize} bytes"
                };
            }

            if (size > maxSize)
            {
                return new ValidationResult
                {
                    Severity = ValidationSeverity.Warning,
                    PropertyName = propertyName,
                    Message = $"Buffer size exceeds recommended maximum of {maxSize} bytes",
                    CurrentValue = size,
                    RecommendedAction = $"Consider reducing buffer size to {maxSize} bytes or less"
                };
            }

            return new ValidationResult
            {
                Severity = ValidationSeverity.Info,
                PropertyName = propertyName,
                Message = "Buffer size is valid",
                CurrentValue = size
            };
        }

        /// <summary>
        /// Validates a threshold value.
        /// </summary>
        private static ValidationResult ValidateThreshold(int threshold, string propertyName, int minValue, int maxValue)
        {
            if (threshold < minValue)
            {
                return new ValidationResult
                {
                    Severity = ValidationSeverity.Error,
                    PropertyName = propertyName,
                    Message = $"Threshold is below minimum of {minValue}",
                    CurrentValue = threshold,
                    RecommendedAction = $"Set threshold to at least {minValue}"
                };
            }

            if (threshold > maxValue)
            {
                return new ValidationResult
                {
                    Severity = ValidationSeverity.Warning,
                    PropertyName = propertyName,
                    Message = $"Threshold exceeds recommended maximum of {maxValue}",
                    CurrentValue = threshold,
                    RecommendedAction = $"Consider reducing threshold to {maxValue} or less"
                };
            }

            return new ValidationResult
            {
                Severity = ValidationSeverity.Info,
                PropertyName = propertyName,
                Message = "Threshold value is valid",
                CurrentValue = threshold
            };
        }

        /// <summary>
        /// Validates a service identifier string.
        /// </summary>
        private static ValidationResult ValidateServiceIdentifier(string identifier, string propertyName)
        {
            if (string.IsNullOrWhiteSpace(identifier))
            {
                return new ValidationResult
                {
                    Severity = ValidationSeverity.Warning,
                    PropertyName = propertyName,
                    Message = "Service identifier is empty",
                    CurrentValue = identifier,
                    RecommendedAction = "Set a descriptive service identifier"
                };
            }

            // Check for valid DNS-like format
            var validPattern = @"^[a-zA-Z0-9]([a-zA-Z0-9\-\.]*[a-zA-Z0-9])?$";
            if (!Regex.IsMatch(identifier, validPattern))
            {
                return new ValidationResult
                {
                    Severity = ValidationSeverity.Warning,
                    PropertyName = propertyName,
                    Message = "Service identifier should follow DNS naming conventions",
                    CurrentValue = identifier,
                    RecommendedAction = "Use alphanumeric characters, hyphens, and dots only"
                };
            }

            return new ValidationResult
            {
                Severity = ValidationSeverity.Info,
                PropertyName = propertyName,
                Message = "Service identifier is valid",
                CurrentValue = identifier
            };
        }

        /// <summary>
        /// Validates an event log name.
        /// </summary>
        private static ValidationResult ValidateEventLogName(string logName)
        {
            if (string.IsNullOrWhiteSpace(logName))
            {
                return new ValidationResult
                {
                    Severity = ValidationSeverity.Error,
                    PropertyName = nameof(UsdpConfiguration.EventLogName),
                    Message = "Event log name cannot be null or empty",
                    CurrentValue = logName,
                    RecommendedAction = "Set a valid event log name (e.g., 'Application')"
                };
            }

            var validLogNames = new[] { "Application", "System", "Security" };
            if (!validLogNames.Contains(logName))
            {
                return new ValidationResult
                {
                    Severity = ValidationSeverity.Info,
                    PropertyName = nameof(UsdpConfiguration.EventLogName),
                    Message = "Using custom event log name",
                    CurrentValue = logName,
                    RecommendedAction = "Ensure the custom log is registered or use 'Application' for standard logging"
                };
            }

            return new ValidationResult
            {
                Severity = ValidationSeverity.Info,
                PropertyName = nameof(UsdpConfiguration.EventLogName),
                Message = "Event log name is valid",
                CurrentValue = logName
            };
        }
    }
}
