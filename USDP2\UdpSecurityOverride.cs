using System;
using System.Security.Cryptography;
using System.Threading.Tasks;

namespace USDP2
{
    /// <summary>
    /// Provides optional UDP security capabilities including message authentication and integrity verification.
    ///
    /// ⚠️  REMOVABLE CLASS NOTICE ⚠️
    /// This class is designed to be easily removable from the codebase if optional
    /// UDP security functionality is not needed. It is completely isolated from
    /// core USDP2 functionality and only activated when EnableUdpSecurity
    /// is set to true in the configuration.
    ///
    /// To remove this functionality:
    /// 1. Delete this file (UdpSecurityOverride.cs)
    /// 2. Remove references to UdpSecurityOverride in other classes
    /// 3. Set EnableUdpSecurity to false in UsdpConfiguration.cs
    ///
    /// This class provides UDP security features:
    /// - Message authentication using HMAC-SHA256
    /// - Message integrity verification
    /// - Protection against UDP packet spoofing
    /// - Defense against replay attacks (with timestamp validation)
    /// - Integration with existing USDP2 key management infrastructure
    ///
    /// Security implementation:
    /// - Uses HMAC-SHA256 for message authentication codes (MAC)
    /// - Integrates with KeyManagement<PERSON><PERSON>ider for secure key storage
    /// - Includes timestamp-based replay attack protection
    /// - Provides comprehensive security logging
    /// - Graceful fallback to unsecured operation on failure
    ///
    /// Performance considerations:
    /// - Adds cryptographic overhead to UDP operations
    /// - Increases message size by ~40 bytes (MAC + timestamp + metadata)
    /// - Key derivation performed once per instance for efficiency
    /// - Minimal impact on throughput for typical USDP2 message sizes
    /// </summary>
    public class UdpSecurityOverride
    {
        private readonly byte[] _authenticationKey;
        private readonly TimeSpan _replayWindow;
        private readonly bool _enableTimestampValidation;

        /// <summary>
        /// Private constructor for UdpSecurityOverride. Use CreateAsync factory method instead.
        /// </summary>
        /// <param name="authenticationKey">The pre-loaded authentication key.</param>
        /// <param name="replayWindowMinutes">The replay protection window in minutes.</param>
        /// <param name="enableTimestampValidation">Whether to enable timestamp-based replay protection.</param>
        private UdpSecurityOverride(byte[] authenticationKey, int replayWindowMinutes, bool enableTimestampValidation)
        {
            _authenticationKey = authenticationKey;
            _replayWindow = TimeSpan.FromMinutes(replayWindowMinutes);
            _enableTimestampValidation = enableTimestampValidation;
        }

        /// <summary>
        /// Creates a new instance of the UdpSecurityOverride class asynchronously.
        /// </summary>
        /// <param name="keyBackend">The key management backend to use for key retrieval.</param>
        /// <param name="keyNameOrPath">The name or path of the authentication key.</param>
        /// <param name="vaultUri">The vault URI (for Azure Key Vault backend).</param>
        /// <param name="hashicorpToken">The HashiCorp Vault token (for HashiCorp backend).</param>
        /// <param name="gcpProjectId">The GCP project ID (for Google Cloud backend).</param>
        /// <param name="replayWindowMinutes">The replay protection window in minutes (default: 5).</param>
        /// <param name="enableTimestampValidation">Whether to enable timestamp-based replay protection (default: true).</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>A task that represents the asynchronous creation operation.</returns>
        /// <exception cref="ArgumentException">Thrown when key parameters are invalid.</exception>
        /// <exception cref="CryptographicException">Thrown when key retrieval or validation fails.</exception>
        public static async Task<UdpSecurityOverride> CreateAsync(
            KeyManagementBackend keyBackend = KeyManagementBackend.WindowsDPAPI,
            string keyNameOrPath = "UdpAuthKey",
            string? vaultUri = null,
            string? hashicorpToken = null,
            string? gcpProjectId = null,
            int replayWindowMinutes = 5,
            bool enableTimestampValidation = true,
            CancellationToken cancellationToken = default)
        {
            ArgumentException.ThrowIfNullOrEmpty(keyNameOrPath);

            if (replayWindowMinutes < 1 || replayWindowMinutes > 60)
            {
                throw new ArgumentException("Replay window must be between 1 and 60 minutes", nameof(replayWindowMinutes));
            }

            try
            {
                // Load or create the authentication key
                var authenticationKey = await LoadOrCreateAuthenticationKeyAsync(
                    keyBackend, keyNameOrPath, vaultUri, hashicorpToken, gcpProjectId, cancellationToken);

                var instance = new UdpSecurityOverride(authenticationKey, replayWindowMinutes, enableTimestampValidation);

                Diagnostics.Log("UdpSecurityOverride.Initialized", new
                {
                    KeyBackend = keyBackend.ToString(),
                    KeyName = keyNameOrPath,
                    ReplayWindowMinutes = replayWindowMinutes,
                    TimestampValidationEnabled = enableTimestampValidation,
                    Success = true
                });

                return instance;
            }
            catch (Exception ex)
            {
                Diagnostics.Log("UdpSecurityOverride.InitializationFailed", new
                {
                    KeyBackend = keyBackend.ToString(),
                    KeyName = keyNameOrPath,
                    Error = ex.Message,
                    Success = false
                });
                throw;
            }
        }

        /// <summary>
        /// Secures UDP data by adding message authentication and integrity protection.
        /// </summary>
        /// <param name="data">The data to secure.</param>
        /// <returns>The secured data with authentication information.</returns>
        /// <exception cref="ArgumentNullException">Thrown when data is null.</exception>
        /// <exception cref="CryptographicException">Thrown when security operations fail.</exception>
        public byte[] SecureData(byte[] data)
        {
            ArgumentNullException.ThrowIfNull(data);

            try
            {
                // Create timestamp for replay protection
                var timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                var timestampBytes = BitConverter.GetBytes(timestamp);

                // Create message to authenticate (original data + timestamp)
                var messageToAuth = new byte[data.Length + timestampBytes.Length];
                Array.Copy(data, 0, messageToAuth, 0, data.Length);
                Array.Copy(timestampBytes, 0, messageToAuth, data.Length, timestampBytes.Length);

                // Validate authentication key before use
                if (_authenticationKey == null || _authenticationKey.Length == 0)
                {
                    throw new CryptographicException("Authentication key is null or empty. Cannot secure message.");
                }

                // Generate HMAC-SHA256 authentication code
                using var hmac = new HMACSHA256(_authenticationKey);
                var authCode = hmac.ComputeHash(messageToAuth);

                // Create secured message: [original data][timestamp][auth code]
                var securedData = new byte[data.Length + timestampBytes.Length + authCode.Length];
                Array.Copy(data, 0, securedData, 0, data.Length);
                Array.Copy(timestampBytes, 0, securedData, data.Length, timestampBytes.Length);
                Array.Copy(authCode, 0, securedData, data.Length + timestampBytes.Length, authCode.Length);

                Diagnostics.Log("UdpSecurityOverride.DataSecured", new
                {
                    OriginalSize = data.Length,
                    SecuredSize = securedData.Length,
                    Timestamp = timestamp,
                    Success = true
                });

                return securedData;
            }
            catch (Exception ex)
            {
                Diagnostics.Log("UdpSecurityOverride.SecureDataFailed", new
                {
                    DataSize = data.Length,
                    Error = ex.Message,
                    Success = false
                });
                throw new CryptographicException("Failed to secure UDP data", ex);
            }
        }

        /// <summary>
        /// Verifies and extracts the original data from secured UDP data.
        /// </summary>
        /// <param name="securedData">The secured data to verify.</param>
        /// <returns>The original data if verification succeeds.</returns>
        /// <exception cref="ArgumentNullException">Thrown when securedData is null.</exception>
        /// <exception cref="CryptographicException">Thrown when verification fails.</exception>
        public byte[] VerifyAndExtractData(byte[] securedData)
        {
            ArgumentNullException.ThrowIfNull(securedData);

            try
            {
                // Minimum size check: timestamp (8 bytes) + HMAC-SHA256 (32 bytes) = 40 bytes
                const int minSecurityOverhead = 40;
                if (securedData.Length < minSecurityOverhead)
                {
                    throw new CryptographicException($"Secured data is too small to contain required security information. Expected at least {minSecurityOverhead} bytes, got {securedData.Length} bytes");
                }

                // Extract components with proper bounds checking
                const int authCodeSize = 32; // HMAC-SHA256 size
                const int timestampSize = 8; // Int64 timestamp size
                var originalDataSize = securedData.Length - timestampSize - authCodeSize;

                // Validate that we have a positive message length
                if (originalDataSize < 0)
                {
                    throw new CryptographicException($"Invalid secured data structure. Calculated original data size is negative: {originalDataSize}");
                }

                var originalData = new byte[originalDataSize];
                var timestampBytes = new byte[timestampSize];
                var receivedAuthCode = new byte[authCodeSize];

                // Perform bounds-checked array copies
                try
                {
                    Array.Copy(securedData, 0, originalData, 0, originalDataSize);
                    Array.Copy(securedData, originalDataSize, timestampBytes, 0, timestampSize);
                    Array.Copy(securedData, originalDataSize + timestampSize, receivedAuthCode, 0, authCodeSize);
                }
                catch (ArgumentException ex)
                {
                    throw new CryptographicException("Failed to extract security components from secured data. Data may be corrupted or have invalid structure.", ex);
                }

                // Extract timestamp for verification and logging
                var timestamp = BitConverter.ToInt64(timestampBytes, 0);

                // Verify timestamp if enabled
                if (_enableTimestampValidation)
                {
                    var messageTime = DateTimeOffset.FromUnixTimeSeconds(timestamp);
                    var currentTime = DateTimeOffset.UtcNow;

                    if (Math.Abs((currentTime - messageTime).TotalMinutes) > _replayWindow.TotalMinutes)
                    {
                        throw new CryptographicException($"Message timestamp outside replay window. Message time: {messageTime}, Current time: {currentTime}");
                    }
                }

                // Verify authentication code
                var messageToAuth = new byte[originalDataSize + timestampSize];
                Array.Copy(originalData, 0, messageToAuth, 0, originalDataSize);
                Array.Copy(timestampBytes, 0, messageToAuth, originalDataSize, timestampSize);

                // Validate authentication key before use
                if (_authenticationKey == null || _authenticationKey.Length == 0)
                {
                    throw new CryptographicException("Authentication key is null or empty. Cannot verify message authenticity.");
                }

                using var hmac = new HMACSHA256(_authenticationKey);
                var computedAuthCode = hmac.ComputeHash(messageToAuth);

                if (!CryptographicOperations.FixedTimeEquals(receivedAuthCode, computedAuthCode))
                {
                    throw new CryptographicException("Message authentication failed - data may have been tampered with");
                }
                Diagnostics.Log("UdpSecurityOverride.DataVerified", new
                {
                    SecuredSize = securedData.Length,
                    OriginalSize = originalData.Length,
                    Timestamp = timestamp,
                    Success = true
                });

                return originalData;
            }
            catch (Exception ex)
            {
                Diagnostics.Log("UdpSecurityOverride.VerificationFailed", new
                {
                    SecuredDataSize = securedData.Length,
                    Error = ex.Message,
                    Success = false
                });
                throw;
            }
        }

        /// <summary>
        /// Loads or creates an authentication key for UDP security operations.
        /// </summary>
        private static async Task<byte[]> LoadOrCreateAuthenticationKeyAsync(
            KeyManagementBackend keyBackend,
            string keyNameOrPath,
            string? vaultUri,
            string? hashicorpToken,
            string? gcpProjectId,
#pragma warning disable IDE0060 // Remove unused parameter - cancellationToken reserved for future use
            CancellationToken cancellationToken = default)
#pragma warning restore IDE0060 // Remove unused parameter
        {
            try
            {
                // Try to load existing key
                return await KeyManagementProvider.LoadKeyAsync(
                    keyBackend, keyNameOrPath, vaultUri, hashicorpToken, gcpProjectId);
            }
            catch (Exception)
            {
                // Key doesn't exist, create a new one (only supported for DPAPI currently)
                if (keyBackend == KeyManagementBackend.WindowsDPAPI)
                {
                    var newKey = new byte[32]; // 256-bit key for HMAC-SHA256
                    using (var rng = RandomNumberGenerator.Create())
                    {
                        rng.GetBytes(newKey);
                    }

                    KeyManagementProvider.SaveKeyToDPAPI(keyNameOrPath, newKey);

                    Diagnostics.Log("UdpSecurityOverride.NewKeyCreated", new
                    {
                        KeyBackend = keyBackend.ToString(),
                        KeyName = keyNameOrPath,
                        KeySize = newKey.Length,
                        Success = true
                    });

                    return newKey;
                }
                else
                {
                    throw new NotSupportedException($"Creating new UDP authentication keys is not currently supported for backend: {keyBackend}");
                }
            }
        }
    }
}
