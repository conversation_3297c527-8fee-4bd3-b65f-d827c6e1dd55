using System;
using System.Threading;
using System.Threading.Tasks;

namespace USDP2.HealthCheck
{
    /// <summary>
    /// Defines a health check for a component or service.
    /// </summary>
    public interface IHealthCheck
    {
        /// <summary>
        /// Gets the name of the health check.
        /// </summary>
        string Name { get; }

        /// <summary>
        /// Gets the description of what this health check verifies.
        /// </summary>
        string Description { get; }

        /// <summary>
        /// Gets the timeout for the health check operation.
        /// </summary>
        TimeSpan Timeout { get; }

        /// <summary>
        /// Performs the health check asynchronously.
        /// </summary>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>A <see cref="Task{HealthCheckResult}"/> representing the health check result.</returns>
        Task<HealthCheckResult> CheckHealthAsync(CancellationToken cancellationToken = default);
    }

    /// <summary>
    /// Base class for health checks that provides common functionality.
    /// </summary>
    public abstract class HealthCheckBase : IHealthCheck
    {
        /// <summary>
        /// Gets the name of the health check.
        /// </summary>
        public string Name { get; }

        /// <summary>
        /// Gets the description of what this health check verifies.
        /// </summary>
        public string Description { get; }

        /// <summary>
        /// Gets the timeout for the health check operation.
        /// </summary>
        public TimeSpan Timeout { get; }

        /// <summary>
        /// Initializes a new instance of the <see cref="HealthCheckBase"/> class.
        /// </summary>
        /// <param name="name">The name of the health check.</param>
        /// <param name="description">The description of what this health check verifies.</param>
        /// <param name="timeout">The timeout for the health check operation.</param>
        protected HealthCheckBase(string name, string description, TimeSpan? timeout = null)
        {
            Name = name ?? throw new ArgumentNullException(nameof(name));
            Description = description ?? throw new ArgumentNullException(nameof(description));
            Timeout = timeout ?? TimeSpan.FromSeconds(30);
        }

        /// <summary>
        /// Performs the health check asynchronously.
        /// </summary>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>A <see cref="Task{HealthCheckResult}"/> representing the health check result.</returns>
        public async Task<HealthCheckResult> CheckHealthAsync(CancellationToken cancellationToken = default)
        {
            var startTime = DateTimeOffset.UtcNow;
            
            try
            {
                using var timeoutCts = new CancellationTokenSource(Timeout);
                using var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, timeoutCts.Token);

                var result = await PerformHealthCheckAsync(combinedCts.Token).ConfigureAwait(false);
                var duration = DateTimeOffset.UtcNow - startTime;

                // Update the result with the actual duration
                return new HealthCheckResult(result.Status, result.Description, duration, result.Exception, result.Data);
            }
            catch (OperationCanceledException) when (cancellationToken.IsCancellationRequested)
            {
                var duration = DateTimeOffset.UtcNow - startTime;
                return HealthCheckResult.Unknown("Health check was cancelled", duration);
            }
            catch (OperationCanceledException)
            {
                var duration = DateTimeOffset.UtcNow - startTime;
                return HealthCheckResult.Unhealthy($"Health check timed out after {Timeout.TotalSeconds:F1} seconds", duration);
            }
            catch (Exception ex)
            {
                var duration = DateTimeOffset.UtcNow - startTime;
                return HealthCheckResult.Unhealthy($"Health check failed with exception: {ex.Message}", duration, ex);
            }
        }

        /// <summary>
        /// Performs the actual health check logic.
        /// </summary>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>A <see cref="Task{HealthCheckResult}"/> representing the health check result.</returns>
        protected abstract Task<HealthCheckResult> PerformHealthCheckAsync(CancellationToken cancellationToken);

        /// <summary>
        /// Creates a data dictionary for health check results.
        /// </summary>
        /// <param name="values">The key-value pairs to include in the data.</param>
        /// <returns>A read-only dictionary containing the data.</returns>
        protected static System.Collections.Generic.IReadOnlyDictionary<string, object> CreateData(params (string key, object value)[] values)
        {
            var data = new System.Collections.Generic.Dictionary<string, object>();
            foreach (var (key, value) in values)
            {
                data[key] = value;
            }
            return data;
        }

        /// <summary>
        /// Logs the health check result.
        /// </summary>
        /// <param name="result">The health check result to log.</param>
        protected void LogResult(HealthCheckResult result)
        {
            UsdpLogger.Log($"HealthCheck.{Name}", new
            {
                Status = result.Status.ToString(),
                Description = result.Description,
                Duration = result.Duration.TotalMilliseconds,
                Timestamp = result.Timestamp,
                Exception = result.Exception?.Message,
                Data = result.Data
            });
        }
    }

    /// <summary>
    /// Represents the configuration for a health check.
    /// </summary>
    public class HealthCheckOptions
    {
        /// <summary>
        /// Gets or sets the timeout for health check operations.
        /// </summary>
        public TimeSpan Timeout { get; set; } = TimeSpan.FromSeconds(30);

        /// <summary>
        /// Gets or sets the interval between health checks.
        /// </summary>
        public TimeSpan Interval { get; set; } = TimeSpan.FromMinutes(1);

        /// <summary>
        /// Gets or sets a value indicating whether the health check is enabled.
        /// </summary>
        public bool Enabled { get; set; } = true;

        /// <summary>
        /// Gets or sets the failure threshold before marking a component as unhealthy.
        /// </summary>
        public int FailureThreshold { get; set; } = 3;

        /// <summary>
        /// Gets or sets the success threshold before marking a component as healthy again.
        /// </summary>
        public int SuccessThreshold { get; set; } = 1;

        /// <summary>
        /// Gets or sets a value indicating whether to log health check results.
        /// </summary>
        public bool LogResults { get; set; } = true;

        /// <summary>
        /// Gets or sets a value indicating whether to include detailed data in health check results.
        /// </summary>
        public bool IncludeDetailedData { get; set; } = true;
    }

    /// <summary>
    /// Represents a health check registration.
    /// </summary>
    public class HealthCheckRegistration
    {
        /// <summary>
        /// Gets the name of the health check.
        /// </summary>
        public string Name { get; }

        /// <summary>
        /// Gets the health check instance.
        /// </summary>
        public IHealthCheck HealthCheck { get; }

        /// <summary>
        /// Gets the health check options.
        /// </summary>
        public HealthCheckOptions Options { get; }

        /// <summary>
        /// Gets the tags associated with the health check.
        /// </summary>
        public System.Collections.Generic.IReadOnlyList<string> Tags { get; }

        /// <summary>
        /// Initializes a new instance of the <see cref="HealthCheckRegistration"/> class.
        /// </summary>
        /// <param name="name">The name of the health check.</param>
        /// <param name="healthCheck">The health check instance.</param>
        /// <param name="options">The health check options.</param>
        /// <param name="tags">The tags associated with the health check.</param>
        public HealthCheckRegistration(
            string name,
            IHealthCheck healthCheck,
            HealthCheckOptions? options = null,
            System.Collections.Generic.IEnumerable<string>? tags = null)
        {
            Name = name ?? throw new ArgumentNullException(nameof(name));
            HealthCheck = healthCheck ?? throw new ArgumentNullException(nameof(healthCheck));
            Options = options ?? new HealthCheckOptions();
            Tags = tags?.ToList() ?? new System.Collections.Generic.List<string>();
        }
    }
}
