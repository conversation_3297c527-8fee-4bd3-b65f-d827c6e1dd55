using System;
using System.Threading;
using System.Threading.Tasks;

namespace USDP2
{
    /// <summary>
    /// 
    /// </summary>
    /// <seealso cref="System.IAsyncDisposable" />
    public interface INetworkReceiver : IAsyncDisposable
    {
        /// <summary>
        /// Starts the receiving asynchronous.
        /// </summary>
        /// <param name="onMessageReceived">The on message received.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns></returns>
        Task StartReceivingAsync(Func<byte[], string, int, Task> onMessageReceived, CancellationToken cancellationToken = default);
    }
}