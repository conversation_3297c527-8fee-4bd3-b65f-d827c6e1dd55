using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using USDP2;

namespace USDP2.Tests
{
    [TestClass]
    public class BloomFilterTests
    {
        private const int TestFilterSize = 10000;
        private const int TestHashFunctions = 5;

        [TestMethod]
        public void BloomFilter_Constructor_ValidParameters_Success()
        {
            // Act & Assert - Should not throw
            var filter = new BloomFilter(TestFilterSize, TestHashFunctions);
            Assert.IsNotNull(filter);
        }

        [TestMethod]
        public void BloomFilter_Constructor_InvalidSize_ThrowsArgumentOutOfRangeException()
        {
            // Act & Assert
            Assert.ThrowsException<ArgumentOutOfRangeException>(() => new BloomFilter(0, TestHashFunctions));
            Assert.ThrowsException<ArgumentOutOfRangeException>(() => new BloomFilter(-1, TestHashFunctions));
            Assert.ThrowsException<ArgumentOutOfRangeException>(() => new BloomFilter(100_000_001, TestHashFunctions));
        }

        [TestMethod]
        public void BloomFilter_Constructor_InvalidHashFunctions_ThrowsArgumentOutOfRangeException()
        {
            // Act & Assert
            Assert.ThrowsException<ArgumentOutOfRangeException>(() => new BloomFilter(TestFilterSize, 0));
            Assert.ThrowsException<ArgumentOutOfRangeException>(() => new BloomFilter(TestFilterSize, -1));
        }

        [TestMethod]
        public void BloomFilter_Add_ValidString_Success()
        {
            // Arrange
            var filter = new BloomFilter(TestFilterSize, TestHashFunctions);
            const string testItem = "test-service";

            // Act & Assert - Should not throw
            filter.Add(testItem);
        }

        [TestMethod]
        public void BloomFilter_MightContain_AddedItem_ReturnsTrue()
        {
            // Arrange
            var filter = new BloomFilter(TestFilterSize, TestHashFunctions);
            const string testItem = "test-service";

            // Act
            filter.Add(testItem);
            bool result = filter.MightContain(testItem);

            // Assert
            Assert.IsTrue(result, "Bloom filter should return true for items that were added");
        }

        [TestMethod]
        public void BloomFilter_MightContain_NotAddedItem_MayReturnFalse()
        {
            // Arrange
            var filter = new BloomFilter(TestFilterSize, TestHashFunctions);
            const string addedItem = "added-service";
            const string notAddedItem = "not-added-service";

            // Act
            filter.Add(addedItem);
            bool resultAdded = filter.MightContain(addedItem);
            bool resultNotAdded = filter.MightContain(notAddedItem);

            // Assert
            Assert.IsTrue(resultAdded, "Should return true for added item");
            // Note: We can't assert false for not added item due to possible false positives
            // But we can verify the filter is working by checking the added item
        }

        [TestMethod]
        public void BloomFilter_MultipleItems_AllReturnTrue()
        {
            // Arrange
            var filter = new BloomFilter(TestFilterSize, TestHashFunctions);
            var testItems = new[] { "service1", "service2", "service3", "service4", "service5" };

            // Act
            foreach (var item in testItems)
            {
                filter.Add(item);
            }

            // Assert
            foreach (var item in testItems)
            {
                Assert.IsTrue(filter.MightContain(item), $"Filter should contain {item}");
            }
        }

        [TestMethod]
        public void BloomFilter_FalsePositiveRate_WithinExpectedRange()
        {
            // Arrange
            var filter = new BloomFilter(10000, 7); // Optimized for ~1% false positive rate
            var addedItems = new HashSet<string>();
            var testItems = new List<string>();

            // Add 1000 items to the filter
            for (int i = 0; i < 1000; i++)
            {
                string item = $"service-{i}";
                filter.Add(item);
                addedItems.Add(item);
            }

            // Test 1000 items that were NOT added
            for (int i = 1000; i < 2000; i++)
            {
                testItems.Add($"service-{i}");
            }

            // Act
            int falsePositives = 0;
            foreach (var item in testItems)
            {
                if (filter.MightContain(item))
                {
                    falsePositives++;
                }
            }

            double falsePositiveRate = falsePositives / (double)testItems.Count;

            // Assert
            // With optimal parameters, false positive rate should be around 1%
            // We'll allow up to 5% to account for randomness in testing
            Assert.IsTrue(falsePositiveRate <= 0.05, 
                $"False positive rate {falsePositiveRate:P2} should be <= 5%");
            
            Console.WriteLine($"False positive rate: {falsePositiveRate:P2} ({falsePositives}/{testItems.Count})");
        }

        [TestMethod]
        public void BloomFilter_EmptyString_HandledGracefully()
        {
            // Arrange
            var filter = new BloomFilter(TestFilterSize, TestHashFunctions);

            // Act & Assert - Should not throw
            filter.Add("");
            bool result = filter.MightContain("");
            Assert.IsTrue(result, "Empty string should be handled correctly");
        }

        [TestMethod]
        public void BloomFilter_UnicodeStrings_HandledCorrectly()
        {
            // Arrange
            var filter = new BloomFilter(TestFilterSize, TestHashFunctions);
            var unicodeItems = new[] { "服务", "サービス", "сервис", "🚀service", "café" };

            // Act
            foreach (var item in unicodeItems)
            {
                filter.Add(item);
            }

            // Assert
            foreach (var item in unicodeItems)
            {
                Assert.IsTrue(filter.MightContain(item), $"Unicode item {item} should be found");
            }
        }

        [TestMethod]
        public void BloomFilter_LargeStrings_HandledCorrectly()
        {
            // Arrange
            var filter = new BloomFilter(TestFilterSize, TestHashFunctions);
            string largeString = new string('a', 10000); // 10KB string

            // Act & Assert - Should not throw
            filter.Add(largeString);
            bool result = filter.MightContain(largeString);
            Assert.IsTrue(result, "Large string should be handled correctly");
        }

        [TestMethod]
        public void BloomFilter_PrimeSize_UsesNextPrime()
        {
            // Arrange & Act
            var filter1 = new BloomFilter(100, TestHashFunctions); // 100 is not prime
            var filter2 = new BloomFilter(101, TestHashFunctions); // 101 is prime

            // Assert - Both should work without throwing
            Assert.IsNotNull(filter1);
            Assert.IsNotNull(filter2);
            
            // Test that they work correctly
            filter1.Add("test");
            filter2.Add("test");
            Assert.IsTrue(filter1.MightContain("test"));
            Assert.IsTrue(filter2.MightContain("test"));
        }

        [TestMethod]
        public void BloomFilter_DifferentHashFunctionCounts_AffectAccuracy()
        {
            // Arrange
            var filter1 = new BloomFilter(10000, 1);  // Few hash functions
            var filter5 = new BloomFilter(10000, 5);  // More hash functions
            var filter10 = new BloomFilter(10000, 10); // Many hash functions

            var addedItems = new List<string>();
            var testItems = new List<string>();

            // Add same items to all filters
            for (int i = 0; i < 500; i++)
            {
                string item = $"service-{i}";
                filter1.Add(item);
                filter5.Add(item);
                filter10.Add(item);
                addedItems.Add(item);
            }

            // Test items not added
            for (int i = 500; i < 1000; i++)
            {
                testItems.Add($"service-{i}");
            }

            // Act
            int falsePositives1 = testItems.Count(item => filter1.MightContain(item));
            int falsePositives5 = testItems.Count(item => filter5.MightContain(item));
            int falsePositives10 = testItems.Count(item => filter10.MightContain(item));

            // Assert
            // More hash functions should generally result in fewer false positives
            Console.WriteLine($"False positives - 1 hash: {falsePositives1}, 5 hash: {falsePositives5}, 10 hash: {falsePositives10}");
            
            // At minimum, all added items should still be found
            foreach (var item in addedItems)
            {
                Assert.IsTrue(filter1.MightContain(item), $"Filter1 should contain {item}");
                Assert.IsTrue(filter5.MightContain(item), $"Filter5 should contain {item}");
                Assert.IsTrue(filter10.MightContain(item), $"Filter10 should contain {item}");
            }
        }

        [TestMethod]
        public void BloomFilter_ServiceIdentifierPatterns_WorkCorrectly()
        {
            // Arrange
            var filter = new BloomFilter(TestFilterSize, TestHashFunctions);
            var serviceIds = new[]
            {
                "home/lighting",
                "home/security", 
                "office/hvac",
                "office/lighting",
                "factory/sensors"
            };

            // Act
            foreach (var serviceId in serviceIds)
            {
                filter.Add(serviceId);
            }

            // Assert
            foreach (var serviceId in serviceIds)
            {
                Assert.IsTrue(filter.MightContain(serviceId), $"Should contain service ID: {serviceId}");
            }

            // Test some non-existent service IDs
            var nonExistentIds = new[] { "home/hvac", "office/security", "factory/lighting" };
            foreach (var serviceId in nonExistentIds)
            {
                // Note: We can't assert false due to possible false positives
                // But we can verify the test is meaningful by checking that added items work
                bool result = filter.MightContain(serviceId);
                Console.WriteLine($"Non-existent service {serviceId}: {result}");
            }
        }
    }
}
