using System.Threading;
using System.Threading.Tasks;

// Example usage
// For console applications
//await ExampleIntegration.RunAsync(scope, port, multicastAddress, ReceiverStartMode.Await, cancellationToken);
// For background services
//await ExampleIntegration.RunAsync(scope, port, multicastAddress, ReceiverStartMode.FireAndForget, cancellationToken);
// For GUI or large services
//await ExampleIntegration.RunAsync(scope, port, multicastAddress, ReceiverStartMode.ThreadPool, cancellationToken);

namespace USDP2
{
    /// <summary>
    /// The receiver start modes.
    /// </summary>
    public enum ReceiverStartMode
    {
        /// <summary>
        /// Waits for receiver to start completely.
        /// </summary>
        Await,
        /// <summary>
        /// Starts receiver without waiting for completion.
        /// </summary>
        FireAndForget,
        /// <summary>
        /// Starts receiver on thread pool.
        /// </summary>
        ThreadPool
    }
    public static class ExampleIntegration
    {
        /// <summary>
        /// Runs an example integration that demonstrates USDP service discovery functionality.
        /// Creates network sender/receiver components and starts a directory node with configurable receiver modes.
        /// </summary>
        /// <param name="scope">The network scope (Local or Global) that determines the type of network components to create.</param>
        /// <param name="port">The port number to listen on for incoming network traffic.</param>
        /// <param name="multicastAddress">The multicast address to use for local network scope. Can be null for global scope.</param>
        /// <param name="cancellationToken">The cancellation token to stop the operation gracefully.</param>
        /// <param name="receiverMode">The mode for starting the receiver (Await, FireAndForget, or ThreadPool).</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public static async Task RunAsync(
            NetworkScope scope,
            int port,
            string? multicastAddress = null,
            ReceiverStartMode receiverMode = ReceiverStartMode.Await,
            CancellationToken cancellationToken = default)
        {
            var sender = NetworkSenderFactory.CreateSender(scope);
            var receiver = NetworkReceiverFactory.CreateReceiver(scope, port, multicastAddress);

            var directoryNode = new DirectoryNode(sender, receiver);

            switch (receiverMode)
            {
                case ReceiverStartMode.Await:
                    // Await the receive loop so RunAsync doesn't exit immediately
                    await directoryNode.StartAsync(cancellationToken);
                    break;

                case ReceiverStartMode.FireAndForget:
                    // Start receiving in the background (fire-and-forget)
                    _ = directoryNode.StartAsync(cancellationToken); // intentionally not awaited
                    break;

                case ReceiverStartMode.ThreadPool:
                    // Explicitly run on the thread pool (fire-and-forget)
                    _ = Task.Run(() => directoryNode.StartAsync(cancellationToken), cancellationToken);
                    break;
            }
        }
    }
}
