# USDP2 Test Suite Documentation

## 📋 **Overview**

The USDP2 test suite provides comprehensive validation of the Universal Service Discovery Protocol version 2 implementation. This document explains the functionality, purpose, and usage of all test components in the system.

## 🎯 **Test Suite Objectives**

### **Primary Goals:**
- **Functional Validation**: Ensure all USDP2 components work as designed
- **Integration Testing**: Verify components work together correctly
- **Error Handling**: Validate proper exception handling and edge cases
- **Configuration Testing**: Ensure centralized configuration system works properly
- **Network Resilience**: Test behavior under various network conditions

### **Quality Assurance:**
- **Code Coverage**: Comprehensive testing of all public APIs
- **Performance Validation**: Ensure components meet performance requirements
- **Security Testing**: Validate authentication and security mechanisms
- **Compatibility Testing**: Ensure cross-platform functionality

## 📁 **Test Structure**

### **Test Project Organization:**
```
USDP2.Tests/
├── ConfigurationTests.cs          # Configuration system validation
├── HttpNetworkingTests.cs         # HTTP networking component tests
├── LocalDirectoryIntegrationTests.cs # Integration and directory tests
├── NetworkFactoryTests.cs         # Network component factory tests
├── SerializationTests.cs          # JSON/CBOR serialization tests
├── TcpNetworkingTests.cs          # TCP networking component tests
└── UdpNetworkingTests.cs          # UDP networking component tests
```

## 🔧 **Test Classes Detailed**

### **1. ConfigurationTests.cs**
**Purpose**: Validates the centralized configuration system (`UsdpConfiguration`)

**Key Test Categories:**
- **Singleton Pattern**: Ensures single instance behavior
- **Default Values**: Validates all configuration defaults
- **Value Constraints**: Tests port ranges, timeouts, buffer sizes
- **Runtime Modification**: Tests configuration changes during execution
- **Custom Settings**: Validates extensibility features

**Example Tests:**
```csharp
UsdpConfiguration_Singleton_ReturnsSameInstance()
UsdpConfiguration_DefaultValues_AreCorrect()
UsdpConfiguration_NetworkPorts_AreInValidRange()
UsdpConfiguration_TimeoutValues_ArePositive()
```

### **2. HttpNetworkingTests.cs**
**Purpose**: Tests HTTP-based networking components for service discovery

**Key Test Categories:**
- **Message Transmission**: HTTP POST message sending
- **Error Handling**: Network failures, timeouts, cancellation
- **Resource Management**: Proper disposal of HTTP clients
- **Configuration Integration**: Uses centralized port configuration

**Example Tests:**
```csharp
HttpNetworkSender_SendAsync_ValidData_Success()
HttpNetworkSender_SendAsync_CancellationRequested_ThrowsTaskCanceledException()
HttpNetworkSender_Dispose_ProperlyDisposesResources()
```

### **3. LocalDirectoryIntegrationTests.cs**
**Purpose**: Integration testing of the LocalDirectory component with networking

**Key Test Categories:**
- **Service Announcement**: Multicast service advertisements
- **Service Discovery**: Query and response mechanisms
- **Cache Management**: In-memory service cache operations
- **Constructor Validation**: Parameter validation and error handling
- **Resource Cleanup**: Proper disposal patterns

**Example Tests:**
```csharp
LocalDirectory_AnnounceService_SendsMulticastMessage()
LocalDirectory_GetCachedAdvertisements_ReturnsEmptyInitially()
LocalDirectory_Constructor_NullSender_ThrowsArgumentNullException()
```

### **4. NetworkFactoryTests.cs**
**Purpose**: Tests the factory pattern for creating network components

**Key Test Categories:**
- **Component Creation**: Factory methods for senders/receivers
- **Configuration Integration**: Uses centralized configuration
- **Type Validation**: Ensures correct component types returned
- **Parameter Handling**: Validates factory method parameters

### **5. SerializationTests.cs**
**Purpose**: Validates JSON and CBOR serialization/deserialization

**Key Test Categories:**
- **Round-trip Testing**: Serialize → Deserialize → Validate
- **Error Handling**: Invalid data, null inputs, malformed content
- **Digital Signatures**: Cryptographic signing and verification
- **Configuration Integration**: Uses centralized test data

**Example Tests:**
```csharp
ServiceAdvertisement_JsonSerialization_RoundTrip()
ServiceAdvertisement_CborSerialization_RoundTrip()
ServiceAdvertisement_SignAndVerify_Success()
DefaultUSDPSerializer_HandlesNullValues()
```

### **6. TcpNetworkingTests.cs**
**Purpose**: Tests TCP-based networking components

**Key Test Categories:**
- **Connection Management**: TCP client connections
- **Data Transmission**: Reliable message delivery
- **Error Scenarios**: Connection failures, invalid addresses
- **Cancellation Handling**: Proper cancellation token support

**Example Tests:**
```csharp
TcpNetworkSender_SendAsync_ValidData_Success()
TcpNetworkSender_SendAsync_InvalidAddress_ThrowsArgumentOutOfRangeException()
TcpNetworkReceiver_StartReceiving_ReceivesMessages()
```

### **7. UdpNetworkingTests.cs**
**Purpose**: Tests UDP-based networking components including multicast

**Key Test Categories:**
- **Unicast Communication**: Point-to-point UDP messaging
- **Multicast Support**: Group communication testing
- **Message Broadcasting**: Multicast group join/leave operations
- **Network Resilience**: Handles firewall and network restrictions

**Example Tests:**
```csharp
UdpNetworkSender_SendAsync_ValidData_Success()
UdpNetworkReceiver_Multicast_JoinsAndLeavesGroup()
UdpNetworkReceiver_StartReceiving_ReceivesMessages()
```

## 🚀 **Running the Tests**

### **Prerequisites:**
- .NET 8.0 SDK or later
- Visual Studio 2022 or VS Code with C# extension
- Network access (some tests require network operations)
- Administrator privileges (for multicast tests on some systems)

### **Command Line Execution:**

#### **Run All Tests:**
```bash
dotnet test USDP2.Tests --verbosity normal
```

#### **Run Specific Test Class:**
```bash
dotnet test USDP2.Tests --filter "ConfigurationTests" --verbosity normal
```

#### **Run Tests by Category:**
```bash
# Network tests only
dotnet test USDP2.Tests --filter "NetworkingTests" --verbosity normal

# Serialization tests only
dotnet test USDP2.Tests --filter "SerializationTests" --verbosity normal
```

#### **Run with Coverage:**
```bash
dotnet test USDP2.Tests --collect:"XPlat Code Coverage" --verbosity normal
```

### **Visual Studio Execution:**
1. Open the solution in Visual Studio
2. Build the solution (Ctrl+Shift+B)
3. Open Test Explorer (Test → Test Explorer)
4. Click "Run All Tests" or select specific tests

### **VS Code Execution:**
1. Install the C# extension
2. Open the integrated terminal
3. Navigate to the solution directory
4. Run `dotnet test USDP2.Tests`

## ⚙️ **Configuration and Customization**

### **Test Configuration:**
Tests use the centralized `UsdpConfiguration` system with the following approach:

```csharp
// Tests use configuration values with offsets to avoid conflicts
private static int TestPort => UsdpConfiguration.Instance.DefaultMulticastPort + 1000;
private static string TestMulticastAddress => UsdpConfiguration.Instance.DefaultMulticastAddress;
```

### **Port Management:**
- **Base Ports**: Defined in `UsdpConfiguration`
- **Test Offsets**: Each test class uses different port offsets
- **Conflict Avoidance**: Systematic port allocation prevents conflicts

### **Environment Considerations:**
- **Firewall**: Some tests may require firewall permissions
- **Network**: Multicast tests need network interface support
- **Permissions**: Administrator rights may be needed for some network operations

## 🔍 **Test Data and Scenarios**

### **Test Data Sources:**
All test data comes from centralized configuration:

```csharp
var config = UsdpConfiguration.Instance;
var serviceId = new ServiceIdentifier(config.DefaultServiceType, config.DefaultServiceInstance);
var endpoint = new TransportEndpoint
{
    Protocol = config.DefaultProtocol,
    Address = config.DefaultServiceAddress,
    Port = config.DefaultServicePort,
    Security = config.DefaultSecurity
};
```

### **Common Test Scenarios:**
- **Happy Path**: Normal operation with valid inputs
- **Error Cases**: Invalid inputs, network failures, timeouts
- **Edge Cases**: Boundary conditions, null values, empty data
- **Integration**: Multiple components working together
- **Performance**: Response times and resource usage

## 📊 **Test Metrics and Coverage**

### **Current Test Statistics:**
- **Total Test Classes**: 6
- **Total Test Methods**: 70+
- **Code Coverage**: Comprehensive coverage of public APIs
- **Test Categories**: Unit, Integration, Error Handling, Configuration

### **Coverage Areas:**
- ✅ **Networking Components**: UDP, TCP, HTTP senders/receivers
- ✅ **Serialization**: JSON, CBOR, digital signatures
- ✅ **Configuration System**: All properties and constraints
- ✅ **Integration**: LocalDirectory with networking
- ✅ **Error Handling**: Exceptions, timeouts, cancellation
- ✅ **Resource Management**: Disposal patterns, memory cleanup

## 🛠️ **Troubleshooting**

### **Common Issues:**

#### **Network Permission Errors:**
```
Solution: Run tests with administrator privileges
Command: Run Visual Studio or terminal as administrator
```

#### **Port Conflicts:**
```
Solution: Modify port offsets in test configuration
Location: Each test class has configurable port offsets
```

#### **Multicast Not Supported:**
```
Solution: Tests handle this gracefully with Assert.Inconclusive()
Note: Some network environments don't support multicast
```

#### **Firewall Blocking:**
```
Solution: Allow .NET test runner through firewall
Ports: Check UsdpConfiguration for port ranges used
```

### **Debug Information:**
Tests include comprehensive logging through the `Diagnostics` system:
- Serialization errors with detailed messages
- Network operation results
- Configuration validation results

## 📈 **Extending the Tests**

### **Adding New Tests:**
1. Follow existing naming conventions
2. Use centralized configuration values
3. Include proper cleanup in finally blocks
4. Add comprehensive error handling
5. Document test purpose and expected behavior

### **Test Template:**
```csharp
[TestMethod]
public async Task ComponentName_Operation_ExpectedResult()
{
    // Arrange - Use centralized configuration
    var config = UsdpConfiguration.Instance;
    
    try
    {
        // Act - Perform the operation
        
        // Assert - Validate results
    }
    finally
    {
        // Cleanup - Dispose resources
    }
}
```

## 🎯 **Best Practices**

### **Test Development Guidelines:**

#### **1. Configuration-Driven Testing:**
- Always use `UsdpConfiguration.Instance` for test values
- Apply systematic port offsets to prevent conflicts
- Use meaningful configuration property names

#### **2. Resource Management:**
- Implement proper disposal patterns in tests
- Use `try-finally` blocks for cleanup
- Dispose network components after each test

#### **3. Error Handling:**
- Test both success and failure scenarios
- Validate specific exception types
- Include meaningful error messages in assertions

#### **4. Network Testing:**
- Handle environment-specific limitations gracefully
- Use `Assert.Inconclusive()` for unsupported scenarios
- Test with realistic network conditions

### **Code Quality Standards:**
```csharp
[TestMethod]
public async Task ExampleTest_WithBestPractices()
{
    // Arrange - Use configuration and clear variable names
    var config = UsdpConfiguration.Instance;
    var testPort = config.DefaultMulticastPort + 1000;
    INetworkSender sender = null;

    try
    {
        sender = new UdpNetworkSender();
        var testData = Encoding.UTF8.GetBytes("test message");

        // Act - Single responsibility per test
        await sender.SendAsync(testData, "127.0.0.1", testPort);

        // Assert - Specific and meaningful assertions
        Assert.IsTrue(true, "Message sent successfully");
    }
    catch (NetworkException ex) when (ex.InnerException is SocketException)
    {
        // Handle expected network issues gracefully
        Assert.Inconclusive($"Network not available: {ex.Message}");
    }
    finally
    {
        // Cleanup - Always dispose resources
        sender?.Dispose();
    }
}
```

## 🔬 **Advanced Testing Scenarios**

### **Performance Testing:**
```csharp
[TestMethod]
public async Task NetworkComponent_PerformanceTest()
{
    var stopwatch = Stopwatch.StartNew();

    // Perform operation
    await component.OperationAsync();

    stopwatch.Stop();
    Assert.IsTrue(stopwatch.ElapsedMilliseconds < 1000,
        $"Operation took {stopwatch.ElapsedMilliseconds}ms, expected < 1000ms");
}
```

### **Stress Testing:**
```csharp
[TestMethod]
public async Task NetworkComponent_StressTest()
{
    var tasks = new List<Task>();
    for (int i = 0; i < 100; i++)
    {
        tasks.Add(component.SendAsync(testData, address, port));
    }

    await Task.WhenAll(tasks);
    Assert.IsTrue(true, "All concurrent operations completed");
}
```

### **Integration Testing Patterns:**
```csharp
[TestMethod]
public async Task FullWorkflow_ServiceDiscovery_EndToEnd()
{
    // Arrange - Set up complete environment
    var sender = new UdpNetworkSender();
    var receiver = new UdpNetworkReceiver(testPort, true, multicastAddress);
    var directory = new LocalDirectory(sender, receiver, multicastAddress, testPort);

    try
    {
        // Act - Perform complete workflow
        await directory.StartAsync();
        await directory.AnnounceServiceAsync(testAdvertisement);
        var services = directory.GetCachedAdvertisements();

        // Assert - Validate end-to-end functionality
        Assert.IsTrue(services.Any(), "Service discovery workflow completed");
    }
    finally
    {
        await directory.DisposeAsync();
    }
}
```

## 📋 **Test Maintenance**

### **Regular Maintenance Tasks:**

#### **1. Configuration Updates:**
- Review test configuration values quarterly
- Update port ranges if conflicts occur
- Validate timeout values for different environments

#### **2. Test Review:**
- Remove obsolete tests when features change
- Update test data to reflect current usage patterns
- Ensure test names accurately describe functionality

#### **3. Performance Monitoring:**
- Track test execution times
- Identify and optimize slow tests
- Monitor resource usage during test runs

### **Continuous Integration:**
```yaml
# Example CI configuration
test:
  script:
    - dotnet restore USDP2.Tests
    - dotnet build USDP2.Tests --no-restore
    - dotnet test USDP2.Tests --no-build --verbosity normal --collect:"XPlat Code Coverage"
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: coverage.xml
```

## 🔧 **Customization Guide**

### **Environment-Specific Configuration:**
Create environment-specific test configurations:

```csharp
public static class TestEnvironment
{
    public static bool IsCI => Environment.GetEnvironmentVariable("CI") == "true";
    public static bool HasNetworkAccess => CheckNetworkAccess();
    public static bool SupportsMulticast => CheckMulticastSupport();

    private static bool CheckNetworkAccess()
    {
        try
        {
            using var client = new UdpClient();
            return true;
        }
        catch
        {
            return false;
        }
    }
}
```

### **Custom Test Categories:**
```csharp
[TestCategory("Network")]
[TestCategory("Integration")]
[TestMethod]
public async Task CustomCategorizedTest()
{
    // Test implementation
}
```

### **Conditional Test Execution:**
```csharp
[TestMethod]
public async Task NetworkTest_OnlyWhenNetworkAvailable()
{
    if (!TestEnvironment.HasNetworkAccess)
    {
        Assert.Inconclusive("Network access not available");
        return;
    }

    // Proceed with network test
}
```

## 📚 **Additional Resources**

### **Related Documentation:**
- `UsdpConfiguration.cs` - Comprehensive configuration documentation
- `CodeReviewReport.md` - Implementation details and architecture
- `README.md` - Project overview and setup instructions

### **External References:**
- [MSTest Documentation](https://docs.microsoft.com/en-us/dotnet/core/testing/unit-testing-with-mstest)
- [.NET Testing Best Practices](https://docs.microsoft.com/en-us/dotnet/core/testing/best-practices)
- [Network Programming in .NET](https://docs.microsoft.com/en-us/dotnet/fundamentals/networking/)

### **Support and Troubleshooting:**
- Check firewall settings for network tests
- Verify administrator privileges for multicast operations
- Review test output logs for detailed error information
- Consult configuration documentation for parameter meanings

This comprehensive test suite provides robust validation of the USDP2 system while maintaining flexibility for different deployment environments and configurations.
