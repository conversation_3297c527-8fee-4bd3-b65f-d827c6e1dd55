using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Linq;
using System.Reflection;

namespace USDP2.Tests
{
    /// <summary>
    /// Tests for the ValidateConfiguration class.
    /// 
    /// These tests verify that configuration validation correctly identifies:
    /// - Critical errors that prevent startup
    /// - Warnings for suboptimal configurations
    /// - Information messages for valid configurations
    /// - Cross-property dependencies and conflicts
    /// </summary>
    [TestClass]
    public class ValidateConfigurationTests
    {
        /// <summary>
        /// Creates a test configuration by copying the singleton instance.
        /// Since UsdpConfiguration is a singleton, we use reflection to create test instances.
        /// </summary>
        private static UsdpConfiguration CreateTestConfiguration()
        {
            // Use reflection to create a new instance for testing
            var constructor = typeof(UsdpConfiguration).GetConstructor(
                BindingFlags.NonPublic | BindingFlags.Instance,
                null,
                Type.EmptyTypes,
                null);

            if (constructor == null)
                throw new InvalidOperationException("Cannot create test configuration instance");

            return (UsdpConfiguration)constructor.Invoke(null);
        }
        [TestMethod]
        public void ValidateConfiguration_DefaultConfiguration_ShouldBeValid()
        {
            // Arrange
            var config = UsdpConfiguration.Instance;

            // Act
            var results = ValidateConfiguration.ValidateAll(config);

            // Assert
            var criticalErrors = results.Where(r => r.Severity == ValidationSeverity.Critical).ToList();
            var errors = results.Where(r => r.Severity == ValidationSeverity.Error).ToList();

            Assert.AreEqual(0, criticalErrors.Count,
                $"Default configuration should not have critical errors. Found: {string.Join(", ", criticalErrors.Select(e => e.Message))}");

            Assert.AreEqual(0, errors.Count,
                $"Default configuration should not have errors. Found: {string.Join(", ", errors.Select(e => e.Message))}");
        }

        [TestMethod]
        public void ValidateConfiguration_InvalidMulticastAddress_ShouldReturnCriticalError()
        {
            // Arrange
            var config = CreateTestConfiguration();
            config.DefaultMulticastAddress = "invalid-address";

            // Act
            var results = ValidateConfiguration.ValidateAll(config);

            // Assert
            var criticalErrors = results.Where(r => r.Severity == ValidationSeverity.Critical).ToList();
            Assert.IsTrue(criticalErrors.Any(e => e.PropertyName == nameof(config.DefaultMulticastAddress)));
            Assert.IsTrue(criticalErrors.Any(e => e.Message.Contains("Invalid IP address format")));
        }

        [TestMethod]
        public void ValidateConfiguration_NonMulticastAddress_ShouldReturnError()
        {
            // Arrange
            var config = CreateTestConfiguration();
            config.DefaultMulticastAddress = "***********"; // Valid IP but not multicast

            // Act
            var results = ValidateConfiguration.ValidateAll(config);

            // Assert
            var errors = results.Where(r => r.Severity == ValidationSeverity.Error).ToList();
            Assert.IsTrue(errors.Any(e => e.PropertyName == nameof(config.DefaultMulticastAddress)));
            Assert.IsTrue(errors.Any(e => e.Message.Contains("not in the IPv4 multicast range")));
        }

        [TestMethod]
        public void ValidateConfiguration_InvalidPortRange_ShouldReturnCriticalError()
        {
            // Arrange
            var config = new UsdpConfiguration(forTesting: true)
            {
                DefaultHttpPort = 70000 // Invalid port > 65535
            };

            // Act
            var results = ValidateConfiguration.ValidateAll(config);

            // Assert
            var criticalErrors = results.Where(r => r.Severity == ValidationSeverity.Critical).ToList();
            Assert.IsTrue(criticalErrors.Any(e => e.PropertyName == nameof(config.DefaultHttpPort)));
            Assert.IsTrue(criticalErrors.Any(e => e.Message.Contains("Port number must be between 1 and 65535")));
        }

        [TestMethod]
        public void ValidateConfiguration_WellKnownPort_ShouldReturnWarning()
        {
            // Arrange
            var config = new UsdpConfiguration(forTesting: true)
            {
                DefaultHttpPort = 80 // Well-known port
            };

            // Act
            var results = ValidateConfiguration.ValidateAll(config);

            // Assert
            var warnings = results.Where(r => r.Severity == ValidationSeverity.Warning).ToList();
            Assert.IsTrue(warnings.Any(e => e.PropertyName == nameof(config.DefaultHttpPort)));
            Assert.IsTrue(warnings.Any(e => e.Message.Contains("well-known port")));
        }

        [TestMethod]
        public void ValidateConfiguration_PortConflicts_ShouldReturnError()
        {
            // Arrange
            var config = new UsdpConfiguration(forTesting: true)
            {
                DefaultHttpPort = 8080,
                DefaultHttpsPort = 8080 // Same port as HTTP
            };

            // Act
            var results = ValidateConfiguration.ValidateAll(config);

            // Assert
            var errors = results.Where(r => r.Severity == ValidationSeverity.Error).ToList();
            Assert.IsTrue(errors.Any(e => e.PropertyName == "PortConflict"));
            Assert.IsTrue(errors.Any(e => e.Message.Contains("used by multiple services")));
        }

        [TestMethod]
        public void ValidateConfiguration_NegativeTimeout_ShouldReturnCriticalError()
        {
            // Arrange
            var config = new UsdpConfiguration(forTesting: true)
            {
                NetworkTimeout = TimeSpan.FromSeconds(-5) // Negative timeout
            };

            // Act
            var results = ValidateConfiguration.ValidateAll(config);

            // Assert
            var criticalErrors = results.Where(r => r.Severity == ValidationSeverity.Critical).ToList();
            Assert.IsTrue(criticalErrors.Any(e => e.PropertyName == nameof(config.NetworkTimeout)));
            Assert.IsTrue(criticalErrors.Any(e => e.Message.Contains("Timeout cannot be negative")));
        }

        [TestMethod]
        public void ValidateConfiguration_DefaultTtlGreaterThanMaxTtl_ShouldReturnError()
        {
            // Arrange
            var config = new UsdpConfiguration(forTesting: true)
            {
                DefaultTtl = TimeSpan.FromDays(10),
                MaxTtl = TimeSpan.FromDays(5) // Max is less than default
            };

            // Act
            var results = ValidateConfiguration.ValidateAll(config);

            // Assert
            var errors = results.Where(r => r.Severity == ValidationSeverity.Error).ToList();
            Assert.IsTrue(errors.Any(e => e.PropertyName == nameof(config.DefaultTtl)));
            Assert.IsTrue(errors.Any(e => e.Message.Contains("DefaultTtl cannot be greater than MaxTtl")));
        }

        [TestMethod]
        public void ValidateConfiguration_AuthenticationWithoutSecurity_ShouldReturnError()
        {
            // Arrange
            var config = new UsdpConfiguration(forTesting: true)
            {
                RequireAuthentication = true,
                DefaultSecurity = "none" // Authentication required but no security
            };

            // Act
            var results = ValidateConfiguration.ValidateAll(config);

            // Assert
            var errors = results.Where(r => r.Severity == ValidationSeverity.Error).ToList();
            Assert.IsTrue(errors.Any(e => e.PropertyName == nameof(config.RequireAuthentication)));
            Assert.IsTrue(errors.Any(e => e.Message.Contains("Authentication is required but security is set to 'none'")));
        }

        [TestMethod]
        public void ValidateConfiguration_InvalidEndpointPath_ShouldReturnError()
        {
            // Arrange
            var config = new UsdpConfiguration(forTesting: true)
            {
                HttpEndpointPath = "invalid-path" // Missing leading slash
            };

            // Act
            var results = ValidateConfiguration.ValidateAll(config);

            // Assert
            var errors = results.Where(r => r.Severity == ValidationSeverity.Error).ToList();
            Assert.IsTrue(errors.Any(e => e.PropertyName == nameof(config.HttpEndpointPath)));
            Assert.IsTrue(errors.Any(e => e.Message.Contains("must start with '/'")));
        }

        [TestMethod]
        public void ValidateConfiguration_BufferSizeConflict_ShouldReturnError()
        {
            // Arrange
            var config = new UsdpConfiguration(forTesting: true)
            {
                DefaultBufferSize = 64 * 1024,
                MaxBufferSize = 32 * 1024 // Max is less than default
            };

            // Act
            var results = ValidateConfiguration.ValidateAll(config);

            // Assert
            var errors = results.Where(r => r.Severity == ValidationSeverity.Error).ToList();
            Assert.IsTrue(errors.Any(e => e.PropertyName == nameof(config.DefaultBufferSize)));
            Assert.IsTrue(errors.Any(e => e.Message.Contains("DefaultBufferSize cannot be greater than MaxBufferSize")));
        }

        [TestMethod]
        public void ValidateConfiguration_CircuitBreakersDisabled_ShouldReturnInfo()
        {
            // Arrange
            var config = new UsdpConfiguration(forTesting: true)
            {
                EnableCircuitBreakers = false
            };

            // Act
            var results = ValidateConfiguration.ValidateAll(config);

            // Assert
            var infoMessages = results.Where(r => r.Severity == ValidationSeverity.Info).ToList();
            Assert.IsTrue(infoMessages.Any(e => e.PropertyName == nameof(config.EnableCircuitBreakers)));
            Assert.IsTrue(infoMessages.Any(e => e.Message.Contains("Circuit breakers are disabled")));
        }

        [TestMethod]
        public void ValidateConfiguration_SecurityNone_ShouldReturnWarning()
        {
            // Arrange
            var config = new UsdpConfiguration(forTesting: true)
            {
                DefaultSecurity = "none"
            };

            // Act
            var results = ValidateConfiguration.ValidateAll(config);

            // Assert
            var warnings = results.Where(r => r.Severity == ValidationSeverity.Warning).ToList();
            Assert.IsTrue(warnings.Any(e => e.PropertyName == nameof(config.DefaultSecurity)));
            Assert.IsTrue(warnings.Any(e => e.Message.Contains("Security is disabled")));
        }

        [TestMethod]
        public void ValidateAndThrow_WithCriticalErrors_ShouldThrowException()
        {
            // Arrange
            var config = new UsdpConfiguration(forTesting: true)
            {
                DefaultMulticastAddress = "invalid",
                DefaultHttpPort = -1
            };

            // Act & Assert
            Assert.ThrowsException<InvalidOperationException>(() =>
            {
                ValidateConfiguration.ValidateAndThrow(config);
            });
        }

        [TestMethod]
        public void ValidateAndThrow_WithValidConfiguration_ShouldNotThrow()
        {
            // Arrange
            var config = new UsdpConfiguration(forTesting: true); // Default valid configuration

            // Act & Assert - Should not throw
            ValidateConfiguration.ValidateAndThrow(config);
        }

        [TestMethod]
        public void ValidateConfiguration_LogValidationResults_ShouldLogAllSeverities()
        {
            // Arrange
            var config = new UsdpConfiguration(forTesting: true)
            {
                DefaultSecurity = "none", // Warning
                DefaultHttpPort = 80      // Warning for well-known port
            };

            // Act
            var results = ValidateConfiguration.ValidateAll(config);
            ValidateConfiguration.LogValidationResults(results);

            // Assert
            Assert.IsTrue(results.Any(r => r.Severity == ValidationSeverity.Warning));
            Assert.IsTrue(results.Any(r => r.Severity == ValidationSeverity.Info));
            // Should complete without throwing
        }

        [TestMethod]
        public void ValidateConfiguration_QueryTimeoutGreaterThanNetworkTimeout_ShouldReturnWarning()
        {
            // Arrange
            var config = new UsdpConfiguration(forTesting: true)
            {
                NetworkTimeout = TimeSpan.FromSeconds(5),
                QueryResponseTimeout = TimeSpan.FromSeconds(10) // Greater than network timeout
            };

            // Act
            var results = ValidateConfiguration.ValidateAll(config);

            // Assert
            var warnings = results.Where(r => r.Severity == ValidationSeverity.Warning).ToList();
            Assert.IsTrue(warnings.Any(e => e.PropertyName == nameof(config.QueryResponseTimeout)));
            Assert.IsTrue(warnings.Any(e => e.Message.Contains("QueryResponseTimeout is greater than NetworkTimeout")));
        }

        [TestMethod]
        public void ValidateConfiguration_HttpsWithNoSecurity_ShouldReturnWarning()
        {
            // Arrange
            var config = new UsdpConfiguration(forTesting: true)
            {
                UseHttps = true,
                DefaultSecurity = "none"
            };

            // Act
            var results = ValidateConfiguration.ValidateAll(config);

            // Assert
            var warnings = results.Where(r => r.Severity == ValidationSeverity.Warning).ToList();
            Assert.IsTrue(warnings.Any(e => e.PropertyName == nameof(config.UseHttps)));
            Assert.IsTrue(warnings.Any(e => e.Message.Contains("HTTPS is enabled but security is set to 'none'")));
        }
    }
}
