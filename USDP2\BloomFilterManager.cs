using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace USDP2
{
    /// <summary>
    /// High-level manager for Bloom filters used in USDP2 service discovery optimization.
    ///
    /// <para>
    /// The BloomFilterManager provides a comprehensive solution for managing Bloom filters in service discovery scenarios.
    /// It handles two separate filters: one for service identifiers and another for metadata, enabling fast elimination
    /// of non-matching services before performing expensive detailed queries.
    /// </para>
    ///
    /// <para><strong>Key Features:</strong></para>
    /// <list type="bullet">
    /// <item><description><strong>Dual Filtering:</strong> Separate filters for service IDs and metadata for optimal performance</description></item>
    /// <item><description><strong>Auto-tuning:</strong> Automatically calculates optimal filter parameters based on expected load</description></item>
    /// <item><description><strong>Performance Monitoring:</strong> Tracks false positive rates and provides detailed statistics</description></item>
    /// <item><description><strong>Automatic Refresh:</strong> Configurable periodic refresh to maintain accuracy</description></item>
    /// <item><description><strong>Thread Safety:</strong> Safe for concurrent access with internal synchronization</description></item>
    /// <item><description><strong>Resource Management:</strong> Proper disposal and cleanup of resources</description></item>
    /// </list>
    ///
    /// <para><strong>Performance Benefits:</strong></para>
    /// <list type="bullet">
    /// <item><description><strong>Fast Elimination:</strong> O(k) membership testing vs O(n) linear search</description></item>
    /// <item><description><strong>Network Efficiency:</strong> Reduces unnecessary service queries in large networks</description></item>
    /// <item><description><strong>Memory Efficient:</strong> Constant memory usage regardless of service count</description></item>
    /// <item><description><strong>Scalable:</strong> Performance remains constant as network size grows</description></item>
    /// </list>
    ///
    /// <para><strong>Use Cases:</strong></para>
    /// <list type="bullet">
    /// <item><description><strong>Service Discovery:</strong> Quickly eliminate services that don't match query criteria</description></item>
    /// <item><description><strong>Metadata Filtering:</strong> Fast checks for specific metadata key-value pairs</description></item>
    /// <item><description><strong>Large Networks:</strong> Essential for networks with thousands of services</description></item>
    /// <item><description><strong>Real-time Systems:</strong> Minimize latency in time-critical service lookups</description></item>
    /// </list>
    ///
    /// <para><strong>Configuration Integration:</strong></para>
    /// <para>
    /// The manager integrates with UsdpConfiguration to provide comprehensive configuration options including
    /// filter sizing, hash function counts, false positive rate targets, and refresh intervals.
    /// </para>
    ///
    /// <example>
    /// <code>
    /// // Create manager with default configuration
    /// using var manager = new BloomFilterManager();
    ///
    /// // Add services to the filters
    /// manager.AddService(serviceAdvertisement);
    ///
    /// // Quick membership tests
    /// bool mightHaveService = manager.MightContainService("home", "lighting");
    /// bool mightHaveMetadata = manager.MightContainMetadata("location", "room1");
    ///
    /// // Get performance statistics
    /// var stats = manager.GetStatistics();
    /// Console.WriteLine($"False positive rate: {stats["EstimatedFalsePositiveRate"]}");
    /// </code>
    /// </example>
    /// </summary>
    public class BloomFilterManager : IDisposable
    {
        private readonly UsdpConfiguration _config;
        private BloomFilter? _serviceFilter;
        private BloomFilter? _metadataFilter;
        private Timer? _refreshTimer;
        private readonly object _lock = new object();
        private bool _disposed;

        /// <summary>
        /// Gets the current false positive rate estimate based on filter parameters and service count.
        /// </summary>
        public double EstimatedFalsePositiveRate { get; private set; }

        /// <summary>
        /// Gets the number of services currently in the filter.
        /// </summary>
        public int ServiceCount { get; private set; }

        /// <summary>
        /// Gets whether the Bloom filters are currently enabled and initialized.
        /// </summary>
        public bool IsEnabled => _config.EnableBloomFilters && _serviceFilter != null;

        /// <summary>
        /// Initializes a new instance of the BloomFilterManager.
        /// </summary>
        /// <param name="config">The USDP configuration to use. If null, uses UsdpConfiguration.Instance.</param>
        public BloomFilterManager(UsdpConfiguration? config = null)
        {
            _config = config ?? UsdpConfiguration.Instance;

            if (_config.EnableBloomFilters)
            {
                InitializeFilters();
                StartRefreshTimer();
            }

            Diagnostics.Log("BloomFilterManager.Initialized", new
            {
                Enabled = _config.EnableBloomFilters,
                FilterSize = _config.BloomFilterSize,
                HashFunctions = _config.BloomFilterHashFunctions,
                ExpectedServices = _config.ExpectedServiceCount,
                MaxFalsePositiveRate = _config.MaxFalsePositiveRate,
                AutoTune = _config.AutoTuneBloomFilter
            });
        }

        /// <summary>
        /// Initializes the Bloom filters with optimal parameters.
        /// </summary>
        private void InitializeFilters()
        {
            lock (_lock)
            {
                var (size, hashFunctions) = _config.AutoTuneBloomFilter
                    ? CalculateOptimalParameters(_config.ExpectedServiceCount, _config.MaxFalsePositiveRate)
                    : (_config.BloomFilterSize, _config.BloomFilterHashFunctions);

                _serviceFilter = new BloomFilter(size, hashFunctions);
                _metadataFilter = new BloomFilter(size, hashFunctions);

                // Calculate estimated false positive rate
                EstimatedFalsePositiveRate = CalculateFalsePositiveRate(size, hashFunctions, _config.ExpectedServiceCount);

                Diagnostics.Log("BloomFilterManager.FiltersInitialized", new
                {
                    ActualSize = size,
                    ActualHashFunctions = hashFunctions,
                    EstimatedFPR = EstimatedFalsePositiveRate,
                    AutoTuned = _config.AutoTuneBloomFilter
                });
            }
        }

        /// <summary>
        /// Calculates optimal Bloom filter parameters for given constraints.
        /// </summary>
        /// <param name="expectedElements">Expected number of elements to store.</param>
        /// <param name="targetFalsePositiveRate">Target false positive rate.</param>
        /// <returns>Tuple of (optimal size, optimal hash functions).</returns>
        private static (int size, int hashFunctions) CalculateOptimalParameters(int expectedElements, double targetFalsePositiveRate)
        {
            // Calculate optimal filter size: m = -n * ln(p) / (ln(2)^2)
            double optimalSize = -expectedElements * Math.Log(targetFalsePositiveRate) / (Math.Log(2) * Math.Log(2));
            int size = Math.Max(1000, Math.Min(100_000_000, (int)Math.Ceiling(optimalSize)));

            // Calculate optimal number of hash functions: k = (m/n) * ln(2)
            double optimalHashFunctions = (size / (double)expectedElements) * Math.Log(2);
            int hashFunctions = Math.Max(1, Math.Min(20, (int)Math.Round(optimalHashFunctions)));

            return (size, hashFunctions);
        }

        /// <summary>
        /// Calculates the false positive rate for given parameters.
        /// </summary>
        /// <param name="filterSize">Size of the filter.</param>
        /// <param name="hashFunctions">Number of hash functions.</param>
        /// <param name="elementCount">Number of elements in the filter.</param>
        /// <returns>Estimated false positive rate.</returns>
        private static double CalculateFalsePositiveRate(int filterSize, int hashFunctions, int elementCount)
        {
            // FPR = (1 - e^(-k*n/m))^k
            double exponent = -hashFunctions * elementCount / (double)filterSize;
            double probability = 1.0 - Math.Exp(exponent);
            return Math.Pow(probability, hashFunctions);
        }

        /// <summary>
        /// Adds a service advertisement to the Bloom filters.
        /// </summary>
        /// <param name="advertisement">The service advertisement to add.</param>
        public void AddService(ServiceAdvertisement advertisement)
        {
            if (!IsEnabled || advertisement == null) return;

            lock (_lock)
            {
                // Add service identifier to service filter
                string serviceKey = $"{advertisement.ServiceId.Namespace}/{advertisement.ServiceId.Name}";
                _serviceFilter!.Add(serviceKey);

                // Add metadata keys to metadata filter
                if (advertisement.Metadata != null)
                {
                    foreach (var kvp in advertisement.Metadata)
                    {
                        string metadataKey = $"{kvp.Key}:{kvp.Value}";
                        _metadataFilter!.Add(metadataKey);
                    }
                }

                ServiceCount++;

                // Recalculate false positive rate
                EstimatedFalsePositiveRate = CalculateFalsePositiveRate(
                    _config.BloomFilterSize,
                    _config.BloomFilterHashFunctions,
                    ServiceCount);
            }

            Diagnostics.Log("BloomFilterManager.ServiceAdded", new
            {
                ServiceId = advertisement.ServiceId.FullName,
                ServiceCount,
                EstimatedFPR = EstimatedFalsePositiveRate
            });
        }

        /// <summary>
        /// Checks if a service might be present in the filter.
        /// </summary>
        /// <param name="serviceNamespace">The service namespace to check.</param>
        /// <param name="serviceName">The service name to check.</param>
        /// <returns>True if the service might be present (subject to false positives), false if definitely not present.</returns>
        public bool MightContainService(string serviceNamespace, string serviceName)
        {
            if (!IsEnabled) return true; // If disabled, assume all services might be present

            lock (_lock)
            {
                string serviceKey = $"{serviceNamespace}/{serviceName}";
                return _serviceFilter!.MightContain(serviceKey);
            }
        }

        /// <summary>
        /// Checks if a service with specific metadata might be present in the filter.
        /// </summary>
        /// <param name="metadataKey">The metadata key to check.</param>
        /// <param name="metadataValue">The metadata value to check.</param>
        /// <returns>True if the metadata might be present (subject to false positives), false if definitely not present.</returns>
        public bool MightContainMetadata(string metadataKey, object metadataValue)
        {
            if (!IsEnabled) return true; // If disabled, assume all metadata might be present

            lock (_lock)
            {
                string metadataKeyValue = $"{metadataKey}:{metadataValue}";
                return _metadataFilter!.MightContain(metadataKeyValue);
            }
        }

        /// <summary>
        /// Refreshes the Bloom filters with current service data.
        /// </summary>
        /// <param name="services">Current list of service advertisements.</param>
        public void RefreshFilters(IEnumerable<ServiceAdvertisement> services)
        {
            if (!IsEnabled) return;

            lock (_lock)
            {
                // Reinitialize filters
                InitializeFilters();
                ServiceCount = 0;

                // Add all current services
                foreach (var service in services)
                {
                    AddService(service);
                }
            }

            Diagnostics.Log("BloomFilterManager.FiltersRefreshed", new
            {
                ServiceCount,
                EstimatedFPR = EstimatedFalsePositiveRate
            });
        }

        /// <summary>
        /// Starts the automatic refresh timer if configured.
        /// </summary>
        private void StartRefreshTimer()
        {
            if (_config.BloomFilterRefreshInterval > TimeSpan.Zero)
            {
                _refreshTimer = new Timer(OnRefreshTimer, null, _config.BloomFilterRefreshInterval, _config.BloomFilterRefreshInterval);

                Diagnostics.Log("BloomFilterManager.RefreshTimerStarted", new
                {
                    Interval = _config.BloomFilterRefreshInterval
                });
            }
        }

        /// <summary>
        /// Timer callback for automatic filter refresh.
        /// </summary>
        private void OnRefreshTimer(object? state)
        {
            try
            {
                // This would typically get services from a service registry
                // For now, we'll just log that a refresh is needed
                Diagnostics.Log("BloomFilterManager.AutoRefreshTriggered", new
                {
                    ServiceCount,
                    EstimatedFPR = EstimatedFalsePositiveRate
                });
            }
            catch (Exception ex)
            {
                Diagnostics.Log("BloomFilterManager.RefreshError", new
                {
                    Error = ex.Message
                });
            }
        }

        /// <summary>
        /// Gets performance statistics for the Bloom filters.
        /// </summary>
        /// <returns>Dictionary containing performance metrics.</returns>
        public Dictionary<string, object> GetStatistics()
        {
            lock (_lock)
            {
                return new Dictionary<string, object>
                {
                    ["Enabled"] = IsEnabled,
                    ["ServiceCount"] = ServiceCount,
                    ["FilterSize"] = _config.BloomFilterSize,
                    ["HashFunctions"] = _config.BloomFilterHashFunctions,
                    ["EstimatedFalsePositiveRate"] = EstimatedFalsePositiveRate,
                    ["MemoryUsageBytes"] = _config.BloomFilterSize / 8, // Approximate memory usage
                    ["AutoTuned"] = _config.AutoTuneBloomFilter,
                    ["RefreshInterval"] = _config.BloomFilterRefreshInterval
                };
            }
        }

        /// <summary>
        /// Disposes the BloomFilterManager and releases resources.
        /// </summary>
        public void Dispose()
        {
            if (!_disposed)
            {
                _refreshTimer?.Dispose();
                _disposed = true;

                Diagnostics.Log("BloomFilterManager.Disposed", new
                {
                    ServiceCount,
                    EstimatedFPR = EstimatedFalsePositiveRate
                });
            }
            GC.SuppressFinalize(this);
        }
    }
}
