using System;
using System.Threading;
using System.Threading.Tasks;

namespace USDP2
{
    /// <summary>
    /// The directory node.
    /// </summary>
    public class DirectoryNode : IAsyncDisposable
    {
        /// <summary>
        /// The sender.
        /// </summary>
        private readonly INetworkSender _sender;
        /// <summary>
        /// The receiver.
        /// </summary>
        private readonly INetworkReceiver _receiver;

        /// <summary>
        /// Initializes a new instance of the <see cref="DirectoryNode"/> class.
        /// </summary>
        /// <param name="sender">The sender.</param>
        /// <param name="receiver">The receiver.</param>
        public DirectoryNode(INetworkSender sender, INetworkReceiver receiver)
        {
            _sender = sender;
            _receiver = receiver;
        }

        /// <summary>
        /// TODO: Add Summary.
        /// </summary>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>A <see cref="Task"/></returns>
        public async Task StartAsync(CancellationToken cancellationToken = default)
        {
            await _receiver.StartReceivingAsync(OnMessageReceivedAsync, cancellationToken);
        }

        /// <summary>
        /// On message received asynchronously.
        /// </summary>
        /// <param name="data">The data.</param>
        /// <param name="remoteAddress">The remote address.</param>
        /// <param name="remotePort">The remote port.</param>
        /// <returns>A <see cref="Task"/></returns>
        private async Task OnMessageReceivedAsync(byte[] data, string remoteAddress, int remotePort)
        {
            // Try to deserialize as ServiceAdvertisement with validation
            if (TryDeserializeServiceAdvertisement(data, out var advertisement, remoteAddress, remotePort))
            {
                await HandleServiceAdvertisementAsync(advertisement!, remoteAddress, remotePort);
                return;
            }

            // Try to deserialize as ServiceQuery with validation
            if (TryDeserializeServiceQuery(data, out var query, remoteAddress, remotePort))
            {
                await HandleServiceQueryAsync(query!, remoteAddress, remotePort);
                return;
            }

            // Unknown message type - log for security monitoring
            UsdpLogger.Log("DirectoryNode.UnknownMessageType", new
            {
                RemoteAddress = remoteAddress,
                RemotePort = remotePort,
                DataSize = data?.Length ?? 0,
                Severity = "Info"
            });
        }

        /// <summary>
        /// Try deserialize service advertisement with comprehensive validation and sanitization.
        /// </summary>
        /// <param name="data">The data.</param>
        /// <param name="advertisement">The advertisement.</param>
        /// <param name="remoteAddress">The remote address for validation logging.</param>
        /// <param name="remotePort">The remote port for validation logging.</param>
        /// <returns>A <see cref="bool"/></returns>
        private static bool TryDeserializeServiceAdvertisement(
            byte[] data,
            out ServiceAdvertisement? advertisement,
            string remoteAddress = "unknown",
            int remotePort = 0)
        {
            advertisement = null;

            // First validate the incoming network data
            var dataValidation = NetworkDataValidator.ValidateIncomingData(data, remoteAddress, remotePort);
            if (!dataValidation.IsValid)
            {
                UsdpLogger.Log("DirectoryNode.InvalidNetworkData", new
                {
                    RemoteAddress = remoteAddress,
                    RemotePort = remotePort,
                    ErrorType = dataValidation.ErrorType,
                    ErrorMessage = dataValidation.ErrorMessage,
                    DataSize = data?.Length ?? 0,
                    Severity = "Warning"
                });
                return false;
            }

            // Try CBOR deserialization first
            try
            {
                var cborResult = ServiceAdvertisement.FromCborWithResult(data);
                if (cborResult.IsSuccess && cborResult.Value != null)
                {
                    // Validate and sanitize the service advertisement
                    var advValidation = NetworkDataValidator.ValidateServiceAdvertisement(
                        cborResult.Value, remoteAddress, remotePort);

                    if (advValidation.IsValid && advValidation.SanitizedAdvertisement != null)
                    {
                        advertisement = advValidation.SanitizedAdvertisement;
                        return true;
                    }
                    else
                    {
                        UsdpLogger.Log("DirectoryNode.ServiceAdvertisementValidationFailed", new
                        {
                            RemoteAddress = remoteAddress,
                            RemotePort = remotePort,
                            ErrorMessage = advValidation.ErrorMessage,
                            Severity = "Warning"
                        });
                        return false;
                    }
                }
            }
            catch (Exception ex)
            {
                UsdpLogger.Log("DirectoryNode.CborDeserializationError", new
                {
                    RemoteAddress = remoteAddress,
                    RemotePort = remotePort,
                    Error = ex.Message,
                    Severity = "Debug"
                });
            }

            // Try JSON deserialization as fallback
            try
            {
                var json = System.Text.Encoding.UTF8.GetString(data);
                var jsonResult = ServiceAdvertisement.FromJsonWithResult(json);

                if (jsonResult.IsSuccess && jsonResult.Value != null)
                {
                    // Validate and sanitize the service advertisement
                    var advValidation = NetworkDataValidator.ValidateServiceAdvertisement(
                        jsonResult.Value, remoteAddress, remotePort);

                    if (advValidation.IsValid && advValidation.SanitizedAdvertisement != null)
                    {
                        advertisement = advValidation.SanitizedAdvertisement;
                        return true;
                    }
                    else
                    {
                        UsdpLogger.Log("DirectoryNode.ServiceAdvertisementValidationFailed", new
                        {
                            RemoteAddress = remoteAddress,
                            RemotePort = remotePort,
                            ErrorMessage = advValidation.ErrorMessage,
                            Severity = "Warning"
                        });
                        return false;
                    }
                }
            }
            catch (Exception ex)
            {
                UsdpLogger.Log("DirectoryNode.JsonDeserializationError", new
                {
                    RemoteAddress = remoteAddress,
                    RemotePort = remotePort,
                    Error = ex.Message,
                    Severity = "Debug"
                });
            }

            // Log failed deserialization attempt
            UsdpLogger.Log("DirectoryNode.DeserializationFailed", new
            {
                RemoteAddress = remoteAddress,
                RemotePort = remotePort,
                DataSize = data?.Length ?? 0,
                Severity = "Info"
            });

            return false;
        }

        /// <summary>
        /// Try deserialize service query with comprehensive validation and sanitization.
        /// </summary>
        /// <param name="data">The data.</param>
        /// <param name="query">The query.</param>
        /// <param name="remoteAddress">The remote address for validation logging.</param>
        /// <param name="remotePort">The remote port for validation logging.</param>
        /// <returns>A <see cref="bool"/></returns>
        private static bool TryDeserializeServiceQuery(
            byte[] data,
            out ServiceQuery? query,
            string remoteAddress = "unknown",
            int remotePort = 0)
        {
            query = null;

            // First validate the incoming network data
            var dataValidation = NetworkDataValidator.ValidateIncomingData(data, remoteAddress, remotePort);
            if (!dataValidation.IsValid)
            {
                UsdpLogger.Log("DirectoryNode.InvalidNetworkDataForQuery", new
                {
                    RemoteAddress = remoteAddress,
                    RemotePort = remotePort,
                    ErrorType = dataValidation.ErrorType,
                    ErrorMessage = dataValidation.ErrorMessage,
                    DataSize = data?.Length ?? 0,
                    Severity = "Warning"
                });
                return false;
            }

            // Try CBOR deserialization first
            try
            {
                var cborResult = ServiceQuery.FromCborWithResult(data);
                if (cborResult.IsSuccess && cborResult.Value != null)
                {
                    // Basic validation for service query
                    if (ValidateServiceQuery(cborResult.Value, remoteAddress, remotePort))
                    {
                        query = cborResult.Value;
                        return true;
                    }
                    return false;
                }
            }
            catch (Exception ex)
            {
                UsdpLogger.Log("DirectoryNode.QueryCborDeserializationError", new
                {
                    RemoteAddress = remoteAddress,
                    RemotePort = remotePort,
                    Error = ex.Message,
                    Severity = "Debug"
                });
            }

            // Try JSON deserialization as fallback
            try
            {
                var json = System.Text.Encoding.UTF8.GetString(data);
                var jsonResult = ServiceQuery.FromJsonWithResult(json);

                if (jsonResult.IsSuccess && jsonResult.Value != null)
                {
                    // Basic validation for service query
                    if (ValidateServiceQuery(jsonResult.Value, remoteAddress, remotePort))
                    {
                        query = jsonResult.Value;
                        return true;
                    }
                    return false;
                }
            }
            catch (Exception ex)
            {
                UsdpLogger.Log("DirectoryNode.QueryJsonDeserializationError", new
                {
                    RemoteAddress = remoteAddress,
                    RemotePort = remotePort,
                    Error = ex.Message,
                    Severity = "Debug"
                });
            }

            return false;
        }

        /// <summary>
        /// Validates a service query for security and data integrity.
        /// </summary>
        /// <param name="query">The service query to validate.</param>
        /// <param name="remoteAddress">The remote address for logging.</param>
        /// <param name="remotePort">The remote port for logging.</param>
        /// <returns>True if the query is valid, false otherwise.</returns>
        private static bool ValidateServiceQuery(ServiceQuery query, string remoteAddress, int remotePort)
        {
            try
            {
                // Validate query is not null
                if (query == null)
                {
                    UsdpLogger.Log("DirectoryNode.NullServiceQuery", new
                    {
                        RemoteAddress = remoteAddress,
                        RemotePort = remotePort,
                        Severity = "Warning"
                    });
                    return false;
                }

                // Validate SID filter length
                if (!string.IsNullOrEmpty(query.SidFilter) &&
                    query.SidFilter.Length > NetworkDataValidator.MaxStringLength)
                {
                    UsdpLogger.Log("DirectoryNode.OversizedSidFilter", new
                    {
                        RemoteAddress = remoteAddress,
                        RemotePort = remotePort,
                        SidFilterLength = query.SidFilter.Length,
                        MaxAllowed = NetworkDataValidator.MaxStringLength,
                        Severity = "Warning"
                    });
                    return false;
                }

                // Validate metadata filter entries
                if (query.MetadataFilter != null &&
                    query.MetadataFilter.Count > NetworkDataValidator.MaxMetadataEntries)
                {
                    UsdpLogger.Log("DirectoryNode.TooManyMetadataFilters", new
                    {
                        RemoteAddress = remoteAddress,
                        RemotePort = remotePort,
                        FilterCount = query.MetadataFilter.Count,
                        MaxAllowed = NetworkDataValidator.MaxMetadataEntries,
                        Severity = "Warning"
                    });
                    return false;
                }

                // Validate individual metadata filter values
                if (query.MetadataFilter != null)
                {
                    foreach (var kvp in query.MetadataFilter)
                    {
                        if (kvp.Key.Length > NetworkDataValidator.MaxStringLength ||
                            kvp.Value.Length > NetworkDataValidator.MaxStringLength)
                        {
                            UsdpLogger.Log("DirectoryNode.OversizedMetadataFilter", new
                            {
                                RemoteAddress = remoteAddress,
                                RemotePort = remotePort,
                                Key = kvp.Key,
                                KeyLength = kvp.Key.Length,
                                ValueLength = kvp.Value.Length,
                                MaxAllowed = NetworkDataValidator.MaxStringLength,
                                Severity = "Warning"
                            });
                            return false;
                        }
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                UsdpLogger.Log("DirectoryNode.ServiceQueryValidationError", new
                {
                    RemoteAddress = remoteAddress,
                    RemotePort = remotePort,
                    Error = ex.Message,
                    Severity = "Error"
                });
                return false;
            }
        }

        // Mark as static and discard unused parameters
        /// <summary>
        /// Handle service advertisement asynchronously.
        /// </summary>
        /// <param name="_advertisement">The advertisement.</param>
        /// <param name="_remoteAddress">The remote address.</param>
        /// <param name="_remotePort">The remote port.</param>
        /// <returns>A <see cref="Task"/></returns>
        private static Task HandleServiceAdvertisementAsync(ServiceAdvertisement _advertisement, string _remoteAddress, int _remotePort)
        {
            // TODO: Add logic to cache, forward, or respond to advertisements
            return Task.CompletedTask;
        }

        // Mark as static and discard unused parameters
        /// <summary>
        /// Handle service query asynchronously.
        /// </summary>
        /// <param name="_query">The query.</param>
        /// <param name="_remoteAddress">The remote address.</param>
        /// <param name="_remotePort">The remote port.</param>
        /// <returns>A <see cref="Task"/></returns>
        private static Task HandleServiceQueryAsync(ServiceQuery _query, string _remoteAddress, int _remotePort)
        {
            // TODO: Add logic to match services and respond
            // Example: Respond with a ServiceAdvertisement if a match is found
            // var response = ...;
            // await AdvertiseServiceAsync(response, remoteAddress, remotePort);
            return Task.CompletedTask;
        }

        /// <summary>
        /// Advertises the service asynchronously.
        /// </summary>
        /// <param name="advertisement">The advertisement.</param>
        /// <param name="address">The address.</param>
        /// <param name="port">The port.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>A <see cref="Task"/></returns>
        public async Task AdvertiseServiceAsync(ServiceAdvertisement advertisement, string address, int port, CancellationToken cancellationToken = default)
        {
            var data = advertisement.ToCbor();
            await _sender.SendAsync(data, address, port, cancellationToken);
        }

        /// <summary>
        /// TODO: Add Summary.
        /// </summary>
        /// <returns>A <see cref="ValueTask"/></returns>
        public ValueTask DisposeAsync()
        {
            GC.SuppressFinalize(this);
            return _receiver.DisposeAsync();
        }
    }
}