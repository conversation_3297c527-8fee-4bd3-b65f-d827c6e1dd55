using System;
using System.Collections.Generic;
using System.Linq;
using System.Numerics;
using System.Threading.Tasks;
/* Example of registering and using the singleton configuration
   // At startup:
   services.AddSingleton<UsdpConfiguration>();
 */

namespace USDP2
{
    /// <summary>
    /// The chord node interface.
    /// </summary>
    public interface IChordNode
    {
        /// <summary>
        /// TODO: Add Summary.
        /// </summary>
        /// <param name="knownNodeAddress">The known node address.</param>
        /// <param name="knownNodePort">The known node port.</param>
        /// <returns>A <see cref="Task"/></returns>
        Task JoinAsync(string knownNodeAddress, int knownNodePort);
        /// <summary>
        /// Lookups and return a Task string asynchronously.
        /// </summary>
        /// <param name="key">The key.</param>
        /// <returns>A Task string</returns>
        Task<string?> LookupAsync(BigInteger key);
        /// <summary>
        /// TODO: Add Summary.
        /// </summary>
        /// <param name="key">The key.</param>
        /// <param name="value">The value.</param>
        /// <returns>A <see cref="Task"/></returns>
        Task StoreAsync(BigInteger key, string value);
    }

    /// <summary>
    /// The chord node.
    /// </summary>
    /// <param name="address">The address.</param>
    /// <param name="port">The port.</param>
    /// <param name="m">The M.</param>
    public class ChordNode(string address, int port, int m = 160)
    : IChordNode {
        /// <summary>
        /// The M.
        /// </summary>
        private readonly int _m = m; // Number of bits in the keyspace
        /// <summary>
        /// The node id.
        /// </summary>
        private readonly BigInteger _nodeId = Hash(address + ":" + port);
        /// <summary>
        /// The address.
        /// </summary>
        private readonly string _address = address;
        /// <summary>
        /// The port.
        /// </summary>
        private readonly int _port = port;

        // Finger table and data store
        /// <summary>
        /// The finger table.
        /// </summary>
        private readonly List<(BigInteger start, string? address, int? port)> _fingerTable = new(new (BigInteger, string?, int?)[m]);
        /// <summary>
        /// The data.
        /// </summary>
        private readonly Dictionary<BigInteger, string> _data = new();

        /// <summary>
        /// Asynchronously joins the Chord network by contacting a known node.
        /// </summary>
        /// <param name="knownNodeAddress">The known node address.</param>
        /// <param name="knownNodePort">The known node port.</param>
        /// <returns>A <see cref="Task"/></returns>
        public Task JoinAsync(string knownNodeAddress, int knownNodePort)
        {
            try
            {
                // TODO: Implement Chord join protocol
                Diagnostics.Log("ChordNode.JoinAttempt", new { KnownNode = $"{knownNodeAddress}:{knownNodePort}", LocalNode = $"{_address}:{_port}" });
                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                Diagnostics.Log("ChordNode.JoinError", new { KnownNode = $"{knownNodeAddress}:{knownNodePort}", LocalNode = $"{_address}:{_port}", Error = ex.Message });
                throw;
            }
        }

        /// <summary>
        /// Lookups and return a Task string asynchronously.
        /// </summary>
        /// <param name="key">The key.</param>
        /// <returns>A Task string asynchronously</returns>
        public Task<string?> LookupAsync(BigInteger key)
        {
            try
            {
                // TODO: Implement Chord lookup protocol
                _data.TryGetValue(key, out var value);
                Diagnostics.Log("ChordNode.Lookup", new { Key = key, LocalNode = $"{_address}:{_port}" });
                return Task.FromResult(value);
            } catch (Exception ex) {
                Diagnostics.Log("ChordNode.LookupError", new { Key = key, LocalNode = $"{_address}:{_port}" , Error = ex.Message });
                throw;
            }
        }

        /// <summary>
        /// TODO: Add Summary.
        /// </summary>
        /// <param name="key">The key.</param>
        /// <param name="value">The value.</param>
        /// <returns>A <see cref="Task"/></returns>
        public Task StoreAsync(BigInteger key, string value)
        {
            try
            {
                // TODO: Implement Chord store protocol
                _data[key] = value;
                Diagnostics.Log("ChordNode.Store", new { Key = key, Value = value, LocalNode = $"{_address}:{_port}" });
                return Task.CompletedTask;
            } catch (Exception ex) {
                Diagnostics.Log("ChordNode.StoreError", new { Key = key, Value = value, LocalNode = $"{_address}:{_port}" , Error = ex.Message });
                throw;
            }
        }

        /// <summary>
        /// Generates a hash for the given input string.
        /// </summary>
        /// <param name="input">The input.</param>
        /// <returns>A <see cref="BigInteger"/></returns>
        // Use SHA-256 for a more robust and secure hash
        private static BigInteger Hash(string input)
        {
            // Simple hash SHA-256 for production)
            return new BigInteger(System.Security.Cryptography.SHA256.HashData(System.Text.Encoding.UTF8.GetBytes(input)));
        }
    }
};
