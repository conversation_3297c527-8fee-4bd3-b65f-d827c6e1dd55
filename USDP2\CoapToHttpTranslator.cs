using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using System;

namespace USDP2
{
	/// <summary>
	/// The coap converts to http translator.
	/// </summary>
	public class CoapToHttpTranslator(HttpClient httpClient) : IProtocolTranslator
	{
		private readonly HttpClient _httpClient = httpClient;
		public CoapToHttpTranslator() : this(new HttpClient()) { }
		public async Task TranslateAsync(byte[] request, string fromProtocol, string toProtocol, string targetAddress, int targetPort, CancellationToken cancellationToken = default)
		{
			if (fromProtocol != "coap+udp" || toProtocol != "http")
			{
				Diagnostics.Log("UnsupportedTranslation", new { From = fromProtocol, To = toProtocol });
				throw new NotSupportedException($"Unsupported translation from {fromProtocol} to {toProtocol}");
			}

			// Convert CoAP message to HTTP
			var url = $"http://{targetAddress}:{targetPort}/usdp";

			var content = new ByteArrayContent(request);

			try
            {
                var response = await _httpClient.PostAsync(url, content, cancellationToken);

                if (response.IsSuccessStatusCode)
                {
                    Diagnostics.Log("CoapToHttpSuccess", new { Url = url, StatusCode = response.StatusCode });
                }
                else
                {
                    Diagnostics.Log("CoapToHttpError", new { Url = url, StatusCode = response.StatusCode });
                    throw new HttpRequestException($"HTTP request failed with status code {response.StatusCode}");
                }
            }
            catch (HttpRequestException ex)
            {
                Diagnostics.Log("CoapToHttpException", new { Url = url, Error = ex.Message });
                throw; // Re-throw the exception to be handled by the caller
            }
    }
}
};