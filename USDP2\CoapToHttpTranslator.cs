using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using System;
using System.Collections.Concurrent;

namespace USDP2
{
    /// <summary>
    /// CoAP to HTTP protocol translator with circuit breaker protection.
    ///
    /// This translator converts CoAP messages to HTTP requests while providing:
    /// - Circuit breaker protection against failing endpoints
    /// - Per-endpoint circuit breaker isolation
    /// - Automatic failure detection and recovery
    /// - Fast failure response during outages
    /// - Comprehensive logging and monitoring
    /// </summary>
    public class CoapToHttpTranslator : IProtocolTranslator, IDisposable
    {
        private readonly HttpClient _httpClient;
        private readonly ConcurrentDictionary<string, CircuitBreaker> _circuitBreakers = new();
        private readonly bool _ownsHttpClient;
        private bool _disposed;

        /// <summary>
        /// Initializes a new instance of the <see cref="CoapToHttpTranslator"/> class with a provided HttpClient.
        /// </summary>
        /// <param name="httpClient">The HttpClient to use for HTTP requests.</param>
        public CoapToHttpTranslator(HttpClient httpClient)
        {
            _httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));
            _ownsHttpClient = false;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="CoapToHttpTranslator"/> class with a default HttpClient.
        /// </summary>
        public CoapToHttpTranslator() : this(new HttpClient())
        {
            _ownsHttpClient = true;
        }
        /// <summary>
        /// Translates a CoAP message to HTTP with circuit breaker protection.
        /// </summary>
        /// <param name="request">The CoAP request data to translate.</param>
        /// <param name="fromProtocol">The source protocol (must be "coap+udp").</param>
        /// <param name="toProtocol">The target protocol (must be "http").</param>
        /// <param name="targetAddress">The target HTTP server address.</param>
        /// <param name="targetPort">The target HTTP server port.</param>
        /// <param name="cancellationToken">Cancellation token for the operation.</param>
        /// <returns>A task representing the translation operation.</returns>
        /// <exception cref="NotSupportedException">Thrown when the protocol translation is not supported.</exception>
        /// <exception cref="CircuitBreakerOpenException">Thrown when the circuit breaker is open for the target endpoint.</exception>
        /// <exception cref="HttpRequestException">Thrown when the HTTP request fails.</exception>
        public async Task TranslateAsync(byte[] request, string fromProtocol, string toProtocol, string targetAddress, int targetPort, CancellationToken cancellationToken = default)
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(CoapToHttpTranslator));

            if (fromProtocol != "coap+udp" || toProtocol != "http")
            {
                UsdpLogger.Log("CoapToHttpTranslator.UnsupportedTranslation", new
                {
                    From = fromProtocol,
                    To = toProtocol,
                    TargetAddress = targetAddress,
                    TargetPort = targetPort
                });
                throw new NotSupportedException($"Unsupported translation from {fromProtocol} to {toProtocol}");
            }

            // Get or create circuit breaker for this endpoint
            var endpointKey = $"{targetAddress}:{targetPort}";
            var circuitBreaker = GetOrCreateCircuitBreaker(endpointKey);

            // Execute the HTTP request through the circuit breaker
            await circuitBreaker.ExecuteAsync(async ct =>
            {
                var url = $"http://{targetAddress}:{targetPort}/usdp";
                using var content = new ByteArrayContent(request);

                UsdpLogger.Log("CoapToHttpTranslator.RequestStarted", new
                {
                    Url = url,
                    RequestSize = request?.Length ?? 0,
                    CircuitBreakerState = circuitBreaker.State
                });

                var response = await _httpClient.PostAsync(url, content, ct).ConfigureAwait(false);

                if (response.IsSuccessStatusCode)
                {
                    UsdpLogger.Log("CoapToHttpTranslator.Success", new
                    {
                        Url = url,
                        StatusCode = (int)response.StatusCode,
                        ResponseHeaders = response.Headers.ToString()
                    });
                }
                else
                {
                    UsdpLogger.Log("CoapToHttpTranslator.HttpError", new
                    {
                        Url = url,
                        StatusCode = (int)response.StatusCode,
                        ReasonPhrase = response.ReasonPhrase
                    });
                    throw new HttpRequestException($"HTTP request failed with status code {response.StatusCode}: {response.ReasonPhrase}");
                }
            }, cancellationToken).ConfigureAwait(false);
        }

        /// <summary>
        /// Gets or creates a circuit breaker for the specified endpoint.
        /// </summary>
        /// <param name="endpointKey">The endpoint key (address:port).</param>
        /// <returns>A circuit breaker instance for the endpoint.</returns>
        private CircuitBreaker GetOrCreateCircuitBreaker(string endpointKey)
        {
            return _circuitBreakers.GetOrAdd(endpointKey, key =>
            {
                var options = new CircuitBreakerOptions
                {
                    FailureThreshold = UsdpConfiguration.Instance.CircuitBreakerFailureThreshold,
                    OpenTimeout = UsdpConfiguration.Instance.CircuitBreakerOpenTimeout,
                    OperationTimeout = UsdpConfiguration.Instance.NetworkTimeout,
                    SuccessThreshold = UsdpConfiguration.Instance.CircuitBreakerSuccessThreshold,
                    ShouldHandleException = ex => ex switch
                    {
                        TaskCanceledException when ex.InnerException is TimeoutException => true, // Count timeout cancellations as failures
                        OperationCanceledException => false, // Don't count cancellations as failures
                        TimeoutException => true,            // Count timeouts as failures
                        HttpRequestException => true,        // Count HTTP errors as failures
                        _ => true // Count other exceptions as failures by default
                    }
                };

                var circuitBreaker = new CircuitBreaker($"CoapToHttpTranslator.{key}", options);

                UsdpLogger.Log("CoapToHttpTranslator.CircuitBreakerCreated", new
                {
                    EndpointKey = key,
                    FailureThreshold = options.FailureThreshold,
                    OpenTimeout = options.OpenTimeout.TotalSeconds,
                    OperationTimeout = options.OperationTimeout.TotalSeconds
                });

                return circuitBreaker;
            });
        }

        /// <summary>
        /// Gets statistics for all circuit breakers managed by this translator.
        /// </summary>
        /// <returns>A dictionary of endpoint keys and their circuit breaker statistics.</returns>
        public Dictionary<string, CircuitBreakerStatistics> GetCircuitBreakerStatistics()
        {
            var statistics = new Dictionary<string, CircuitBreakerStatistics>();

            foreach (var kvp in _circuitBreakers)
            {
                try
                {
                    statistics[kvp.Key] = kvp.Value.GetStatistics();
                }
                catch (Exception ex)
                {
                    UsdpLogger.Log("CoapToHttpTranslator.StatisticsError", new
                    {
                        EndpointKey = kvp.Key,
                        Error = ex.Message
                    });
                }
            }

            return statistics;
        }

        /// <summary>
        /// Gets the circuit breaker for a specific endpoint, if it exists.
        /// </summary>
        /// <param name="targetAddress">The target address.</param>
        /// <param name="targetPort">The target port.</param>
        /// <returns>The circuit breaker for the endpoint, or null if it doesn't exist.</returns>
        public CircuitBreaker? GetCircuitBreaker(string targetAddress, int targetPort)
        {
            var endpointKey = $"{targetAddress}:{targetPort}";
            return _circuitBreakers.TryGetValue(endpointKey, out var circuitBreaker) ? circuitBreaker : null;
        }

        /// <summary>
        /// Disposes the translator and its resources.
        /// </summary>
        public void Dispose()
        {
            if (!_disposed)
            {
                if (_ownsHttpClient)
                {
                    _httpClient?.Dispose();
                }

                UsdpLogger.Log("CoapToHttpTranslator.Disposed", new
                {
                    CircuitBreakersCount = _circuitBreakers.Count,
                    OwnedHttpClient = _ownsHttpClient
                });

                _disposed = true;
            }
            GC.SuppressFinalize(this);
        }
    }
}