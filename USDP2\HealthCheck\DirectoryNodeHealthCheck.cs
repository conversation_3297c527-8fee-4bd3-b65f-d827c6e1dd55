using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;

namespace USDP2.HealthCheck
{
    /// <summary>
    /// Health check for DirectoryNode that verifies message processing capability,
    /// network connectivity, and overall service discovery functionality.
    /// </summary>
    public class DirectoryNodeHealthCheck : HealthCheckBase
    {
        private readonly DirectoryNode _directoryNode;
        private readonly HealthCheckOptions _options;

        /// <summary>
        /// Initializes a new instance of the <see cref="DirectoryNodeHealthCheck"/> class.
        /// </summary>
        /// <param name="directoryNode">The DirectoryNode to monitor.</param>
        /// <param name="options">The health check options.</param>
        public DirectoryNodeHealthCheck(DirectoryNode directoryNode, HealthCheckOptions? options = null)
            : base("DirectoryNode", "Monitors DirectoryNode message processing and service discovery capabilities", options?.Timeout)
        {
            _directoryNode = directoryNode ?? throw new ArgumentNullException(nameof(directoryNode));
            _options = options ?? new HealthCheckOptions();
        }

        /// <summary>
        /// Performs the DirectoryNode health check.
        /// </summary>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>A <see cref="Task{HealthCheckResult}"/> representing the health check result.</returns>
        protected override async Task<HealthCheckResult> PerformHealthCheckAsync(CancellationToken cancellationToken)
        {
            var stopwatch = Stopwatch.StartNew();
            var healthData = new Dictionary<string, object>();
            var issues = new List<string>();

            try
            {
                // Check 1: Verify DirectoryNode is properly initialized
                await CheckNodeInitializationAsync(healthData, issues, cancellationToken).ConfigureAwait(false);

                // Check 2: Test message processing capability
                await CheckMessageProcessingAsync(healthData, issues, cancellationToken).ConfigureAwait(false);

                // Check 3: Verify service advertisement functionality
                await CheckServiceAdvertisementAsync(healthData, issues, cancellationToken).ConfigureAwait(false);

                // Check 4: Test serialization/deserialization
                await CheckSerializationAsync(healthData, issues, cancellationToken).ConfigureAwait(false);

                // Check 5: Monitor performance metrics
                await CheckPerformanceMetricsAsync(healthData, issues, cancellationToken).ConfigureAwait(false);

                stopwatch.Stop();

                // Determine overall health status
                var status = DetermineHealthStatus(issues, healthData);
                var description = CreateHealthDescription(status, issues);

                var result = new HealthCheckResult(status, description, stopwatch.Elapsed, null, healthData);

                if (_options.LogResults)
                {
                    LogResult(result);
                }

                return result;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                healthData["exception"] = ex.Message;
                healthData["stackTrace"] = ex.StackTrace ?? string.Empty;

                var result = HealthCheckResult.Unhealthy($"DirectoryNode health check failed: {ex.Message}", stopwatch.Elapsed, ex, healthData);

                if (_options.LogResults)
                {
                    LogResult(result);
                }

                return result;
            }
        }

        /// <summary>
        /// Checks if the DirectoryNode is properly initialized.
        /// </summary>
        private async Task CheckNodeInitializationAsync(Dictionary<string, object> healthData, List<string> issues, CancellationToken cancellationToken)
        {
            try
            {
                // Test that the DirectoryNode can handle basic operations
                // Since most properties are private, we'll test through public interface

                // Create a test service advertisement
                var testServiceId = new ServiceIdentifier("health-check", "test-service");
                var testEndpoint = new TransportEndpoint
                {
                    Address = "127.0.0.1",
                    Port = 8080,
                    Protocol = "http"
                };
                var testAdvertisement = new ServiceAdvertisement(testServiceId, testEndpoint);

                // Test advertisement functionality
                await _directoryNode.AdvertiseServiceAsync(testAdvertisement, "127.0.0.1", 8080, cancellationToken).ConfigureAwait(false);

                healthData["nodeInitialized"] = true;
                healthData["advertisementTestSuccessful"] = true;
            }
            catch (Exception ex)
            {
                healthData["nodeInitialized"] = false;
                healthData["initializationError"] = ex.Message;
                issues.Add($"DirectoryNode initialization check failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Checks message processing capability.
        /// </summary>
        private async Task CheckMessageProcessingAsync(Dictionary<string, object> healthData, List<string> issues, CancellationToken cancellationToken)
        {
            var stopwatch = Stopwatch.StartNew();
            var successfulProcessing = 0;
            var failedProcessing = 0;
            const int testMessages = 5;

            try
            {
                for (int i = 0; i < testMessages; i++)
                {
                    try
                    {
                        // Create test service advertisement data
                        var testServiceId = new ServiceIdentifier("health-check", $"test-service-{i}");
                        var testEndpoint = new TransportEndpoint
                        {
                            Address = "*************",
                            Port = 8080 + i,
                            Protocol = "http"
                        };
                        var testAdvertisement = new ServiceAdvertisement(testServiceId, testEndpoint);
                        var testData = testAdvertisement.ToCbor();

                        // Test service advertisement functionality instead
                        await _directoryNode.AdvertiseServiceAsync(testAdvertisement, "127.0.0.1", 8080, cancellationToken).ConfigureAwait(false);
                        successfulProcessing++;
                    }
                    catch
                    {
                        failedProcessing++;
                    }

                    if (cancellationToken.IsCancellationRequested)
                        break;
                }

                stopwatch.Stop();

                healthData["messageProcessingTotal"] = testMessages;
                healthData["messageProcessingSuccessful"] = successfulProcessing;
                healthData["messageProcessingFailed"] = failedProcessing;
                healthData["messageProcessingDuration"] = stopwatch.ElapsedMilliseconds;
                healthData["messageProcessingSuccessRate"] = testMessages > 0 ? (double)successfulProcessing / testMessages * 100 : 0;

                if (failedProcessing > testMessages * 0.2) // More than 20% failure rate
                {
                    issues.Add($"High message processing failure rate: {failedProcessing}/{testMessages} failed");
                }

                if (stopwatch.ElapsedMilliseconds > 2000) // More than 2 seconds for 5 messages
                {
                    issues.Add($"Message processing is slow: {stopwatch.ElapsedMilliseconds}ms for {testMessages} messages");
                }
            }
            catch (Exception ex)
            {
                healthData["messageProcessingError"] = ex.Message;
                issues.Add($"Message processing check failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Checks service advertisement functionality.
        /// </summary>
        private async Task CheckServiceAdvertisementAsync(Dictionary<string, object> healthData, List<string> issues, CancellationToken cancellationToken)
        {
            var stopwatch = Stopwatch.StartNew();
            var successfulAdvertisements = 0;
            var failedAdvertisements = 0;
            const int testAdvertisements = 3;

            try
            {
                for (int i = 0; i < testAdvertisements; i++)
                {
                    try
                    {
                        var serviceId = new ServiceIdentifier("health-check", $"advertisement-test-{i}");
                        var endpoint = new TransportEndpoint
                        {
                            Address = $"192.168.1.{100 + i}",
                            Port = 9000 + i,
                            Protocol = "tcp"
                        };
                        var advertisement = new ServiceAdvertisement(serviceId, endpoint);

                        await _directoryNode.AdvertiseServiceAsync(advertisement, "127.0.0.1", 8080, cancellationToken).ConfigureAwait(false);
                        successfulAdvertisements++;
                    }
                    catch
                    {
                        failedAdvertisements++;
                    }

                    if (cancellationToken.IsCancellationRequested)
                        break;
                }

                stopwatch.Stop();

                healthData["serviceAdvertisementTotal"] = testAdvertisements;
                healthData["serviceAdvertisementSuccessful"] = successfulAdvertisements;
                healthData["serviceAdvertisementFailed"] = failedAdvertisements;
                healthData["serviceAdvertisementDuration"] = stopwatch.ElapsedMilliseconds;
                healthData["serviceAdvertisementSuccessRate"] = testAdvertisements > 0 ? (double)successfulAdvertisements / testAdvertisements * 100 : 0;

                if (failedAdvertisements > 0)
                {
                    issues.Add($"Service advertisement failures detected: {failedAdvertisements}/{testAdvertisements} failed");
                }

                if (stopwatch.ElapsedMilliseconds > 1500) // More than 1.5 seconds for 3 advertisements
                {
                    issues.Add($"Service advertisement is slow: {stopwatch.ElapsedMilliseconds}ms for {testAdvertisements} advertisements");
                }
            }
            catch (Exception ex)
            {
                healthData["serviceAdvertisementError"] = ex.Message;
                issues.Add($"Service advertisement check failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Checks serialization and deserialization functionality.
        /// </summary>
        private async Task CheckSerializationAsync(Dictionary<string, object> healthData, List<string> issues, CancellationToken cancellationToken)
        {
            try
            {
                var serviceId = new ServiceIdentifier("health-check", "serialization-test");
                var endpoint = new TransportEndpoint
                {
                    Address = "********",
                    Port = 7777,
                    Protocol = "udp"
                };
                var advertisement = new ServiceAdvertisement(serviceId, endpoint);

                // Test JSON serialization
                var jsonResult = advertisement.ToJsonWithResult();
                healthData["jsonSerializationSuccessful"] = jsonResult.IsSuccess;

                if (!jsonResult.IsSuccess)
                {
                    issues.Add($"JSON serialization failed: {jsonResult.ErrorMessage}");
                }

                // Test CBOR serialization
                var cborResult = advertisement.ToCborWithResult();
                healthData["cborSerializationSuccessful"] = cborResult.IsSuccess;

                if (!cborResult.IsSuccess)
                {
                    issues.Add($"CBOR serialization failed: {cborResult.ErrorMessage}");
                }

                // Test deserialization if serialization succeeded
                if (jsonResult.IsSuccess)
                {
                    var deserializedResult = ServiceAdvertisement.FromJsonWithResult(jsonResult.Value);
                    healthData["jsonDeserializationSuccessful"] = deserializedResult.IsSuccess;

                    if (!deserializedResult.IsSuccess)
                    {
                        issues.Add($"JSON deserialization failed: {deserializedResult.ErrorMessage}");
                    }
                }

                if (cborResult.IsSuccess)
                {
                    var deserializedResult = ServiceAdvertisement.FromCborWithResult(cborResult.Value);
                    healthData["cborDeserializationSuccessful"] = deserializedResult.IsSuccess;

                    if (!deserializedResult.IsSuccess)
                    {
                        issues.Add($"CBOR deserialization failed: {deserializedResult.ErrorMessage}");
                    }
                }

                await Task.Delay(1, cancellationToken).ConfigureAwait(false); // Minimal async operation
            }
            catch (Exception ex)
            {
                healthData["serializationError"] = ex.Message;
                issues.Add($"Serialization check failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Checks performance metrics and resource usage.
        /// </summary>
        private async Task CheckPerformanceMetricsAsync(Dictionary<string, object> healthData, List<string> issues, CancellationToken cancellationToken)
        {
            try
            {
                var process = Process.GetCurrentProcess();

                healthData["memoryUsageMB"] = process.WorkingSet64 / (1024 * 1024);
                healthData["cpuTimeMs"] = process.TotalProcessorTime.TotalMilliseconds;
                healthData["threadCount"] = process.Threads.Count;

                // Check if memory usage is excessive (more than 300MB for this component)
                var memoryUsageMB = process.WorkingSet64 / (1024 * 1024);
                if (memoryUsageMB > 300)
                {
                    issues.Add($"High memory usage detected: {memoryUsageMB}MB");
                }

                // Check thread count (more than 50 threads might indicate issues)
                if (process.Threads.Count > 50)
                {
                    issues.Add($"High thread count detected: {process.Threads.Count} threads");
                }

                await Task.Delay(1, cancellationToken).ConfigureAwait(false); // Minimal async operation
            }
            catch (Exception ex)
            {
                healthData["performanceMetricsError"] = ex.Message;
                issues.Add($"Performance metrics check failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Determines the overall health status based on issues found.
        /// </summary>
        private static HealthStatus DetermineHealthStatus(List<string> issues, Dictionary<string, object> healthData)
        {
            if (issues.Count == 0)
                return HealthStatus.Healthy;

            // Check for critical issues
            var criticalIssues = issues.FindAll(issue =>
                issue.Contains("initialization") ||
                issue.Contains("serialization failed") ||
                issue.Contains("High") && issue.Contains("failure rate"));

            if (criticalIssues.Count > 0)
                return HealthStatus.Unhealthy;

            // Check for performance issues
            var performanceIssues = issues.FindAll(issue =>
                issue.Contains("slow") ||
                issue.Contains("High memory") ||
                issue.Contains("High thread"));

            if (performanceIssues.Count > 0)
                return HealthStatus.Degraded;

            return issues.Count > 2 ? HealthStatus.Degraded : HealthStatus.Healthy;
        }

        /// <summary>
        /// Creates a health description based on the status and issues.
        /// </summary>
        private static string CreateHealthDescription(HealthStatus status, List<string> issues)
        {
            return status switch
            {
                HealthStatus.Healthy => "DirectoryNode is healthy and functioning normally",
                HealthStatus.Degraded => $"DirectoryNode is degraded with {issues.Count} issue(s): {string.Join("; ", issues)}",
                HealthStatus.Unhealthy => $"DirectoryNode is unhealthy with {issues.Count} issue(s): {string.Join("; ", issues)}",
                _ => $"DirectoryNode health status is unknown with {issues.Count} issue(s): {string.Join("; ", issues)}"
            };
        }
    }
}
