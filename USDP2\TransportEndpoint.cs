using System.Text.Json.Serialization;

namespace USDP2
{
    /// <summary>
    /// Describes a network-agnostic service endpoint.
    /// </summary>
    public sealed class TransportEndpoint
    {
        /// <summary>
        /// Gets or sets the protocol.
        /// </summary>
        /// <value>A <see cref="string"/></value>
        [JsonPropertyName("protocol")]
        public string Protocol { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the address.
        /// </summary>
        /// <value>A <see cref="string"/></value>
        [JsonPropertyName("address")]
        public string Address { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the port.
        /// </summary>
        /// <value>An <see cref="int"/></value>
        [JsonPropertyName("port")]
        public int Port { get; set; }

        /// <summary>
        /// Gets or sets the security.
        /// </summary>
        /// <value>A <see cref="string"/></value>
        [JsonPropertyName("security")]
        public string Security { get; set; } = string.Empty;

        // Delegated serialization/deserialization
        /// <summary>
        /// Converts to the json.
        /// </summary>
        /// <returns>A <see cref="string"/></returns>
        public string ToJson() => USDPSerializer.ToJson(this);
        /// <summary>
        /// Converts to the cbor.
        /// </summary>
        /// <returns>An array of bytes</returns>
        public byte[] ToCbor() => USDPSerializer.ToCbor(this);

        /// <summary>
        /// Froms the json.
        /// </summary>
        /// <param name="json">The json.</param>
        /// <returns>A <see cref="TransportEndpoint"/></returns>
        public static TransportEndpoint? FromJson(string json) => USDPSerializer.FromJson<TransportEndpoint>(json);
        /// <summary>
        /// Froms the cbor.
        /// </summary>
        /// <param name="cbor">The cbor.</param>
        /// <returns>A <see cref="TransportEndpoint"/></returns>
        public static TransportEndpoint? FromCbor(byte[] cbor) => USDPSerializer.FromCbor<TransportEndpoint>(cbor);
    }
}