using System;
using System.Threading.Tasks;
using USDP2.HealthCheck;

namespace USDP2.Examples
{
    /// <summary>
    /// Simple test to verify the health check system is working correctly.
    /// </summary>
    public static class SimpleHealthCheckTest
    {
        /// <summary>
        /// Runs a basic test of the health check system.
        /// </summary>
        public static async Task RunAsync()
        {
            Console.WriteLine("=== Simple Health Check System Test ===");
            Console.WriteLine();

            try
            {
                // Test 1: Create a basic health check manager
                Console.WriteLine("1. Creating Health Check Manager...");
                using var healthManager = new HealthCheckManager();
                Console.WriteLine("✓ Health Check Manager created successfully");

                // Test 2: Create and register a simple health check
                Console.WriteLine("\n2. Creating and registering health checks...");
                
                // Create test components
                var chordNode = new ChordNode("127.0.0.1", 8080);
                var udpSender = new UdpNetworkSender();
                var udpReceiver = new UdpNetworkReceiver(8081, false);
                var directoryNode = new DirectoryNode(udpSender, udpReceiver);
                var serviceCache = new ServiceAdvertisementCache();

                // Register health checks
                var chordHealthCheck = HealthCheckFactory.CreateChordNodeHealthCheck(chordNode);
                healthManager.RegisterHealthCheck("TestChordNode", chordHealthCheck);

                var directoryHealthCheck = HealthCheckFactory.CreateDirectoryNodeHealthCheck(directoryNode);
                healthManager.RegisterHealthCheck("TestDirectoryNode", directoryHealthCheck);

                var networkHealthCheck = HealthCheckFactory.CreateNetworkComponentsHealthCheck(udpSender, udpReceiver);
                healthManager.RegisterHealthCheck("TestNetworkComponents", networkHealthCheck);

                var cacheHealthCheck = HealthCheckFactory.CreateServiceCacheHealthCheck(serviceCache);
                healthManager.RegisterHealthCheck("TestServiceCache", cacheHealthCheck);

                Console.WriteLine($"✓ Registered {healthManager.GetHealthCheckNames().Count()} health checks");

                // Test 3: Run individual health checks
                Console.WriteLine("\n3. Running individual health checks...");
                foreach (var checkName in healthManager.GetHealthCheckNames())
                {
                    Console.WriteLine($"  Running {checkName}...");
                    var result = await healthManager.CheckHealthAsync(checkName);
                    
                    if (result != null)
                    {
                        var statusIcon = result.Status switch
                        {
                            HealthStatus.Healthy => "✅",
                            HealthStatus.Degraded => "⚠️",
                            HealthStatus.Unhealthy => "❌",
                            _ => "❓"
                        };
                        
                        Console.WriteLine($"    {statusIcon} {result.Status} ({result.Duration.TotalMilliseconds:F0}ms)");
                        
                        if (!result.IsHealthy)
                        {
                            Console.WriteLine($"    Issue: {result.Description}");
                        }
                    }
                    else
                    {
                        Console.WriteLine("    ❌ Health check not found");
                    }
                }

                // Test 4: Run comprehensive health check
                Console.WriteLine("\n4. Running comprehensive health check...");
                var report = await healthManager.CheckHealthAsync();
                
                Console.WriteLine($"Overall Status: {report.Status}");
                Console.WriteLine($"Total Checks: {report.Entries.Count}");
                Console.WriteLine($"Total Duration: {report.TotalDuration.TotalMilliseconds:F0}ms");
                Console.WriteLine($"Healthy: {report.Entries.Values.Count(r => r.IsHealthy)}");
                Console.WriteLine($"Degraded: {report.Entries.Values.Count(r => r.IsDegraded)}");
                Console.WriteLine($"Unhealthy: {report.Entries.Values.Count(r => r.IsUnhealthy)}");
                Console.WriteLine($"Unknown: {report.Entries.Values.Count(r => r.IsUnknown)}");

                // Test 5: Test health check events
                Console.WriteLine("\n5. Testing health check events...");
                var eventReceived = false;
                
                healthManager.HealthCheckCompleted += (sender, args) =>
                {
                    eventReceived = true;
                    Console.WriteLine($"  Event: {args.Name} completed with status {args.Result.Status}");
                };

                healthManager.HealthReportGenerated += (sender, args) =>
                {
                    Console.WriteLine($"  Event: Health report generated with overall status {args.Report.Status}");
                };

                // Trigger events by running another health check
                await healthManager.CheckHealthAsync();
                
                if (eventReceived)
                {
                    Console.WriteLine("✓ Health check events working correctly");
                }
                else
                {
                    Console.WriteLine("⚠️ Health check events may not be working");
                }

                // Test 6: Test configuration integration
                Console.WriteLine("\n6. Testing configuration integration...");
                Console.WriteLine($"Health checks enabled: {UsdpConfiguration.Instance.HealthChecksEnabled}");
                Console.WriteLine($"Health check interval: {UsdpConfiguration.Instance.HealthCheckInterval.TotalSeconds}s");
                Console.WriteLine($"Health check timeout: {UsdpConfiguration.Instance.HealthCheckTimeout.TotalSeconds}s");
                Console.WriteLine($"ChordNode health check enabled: {UsdpConfiguration.Instance.EnableChordNodeHealthCheck}");
                Console.WriteLine($"DirectoryNode health check enabled: {UsdpConfiguration.Instance.EnableDirectoryNodeHealthCheck}");
                Console.WriteLine($"Network health check enabled: {UsdpConfiguration.Instance.EnableNetworkComponentsHealthCheck}");
                Console.WriteLine($"Service cache health check enabled: {UsdpConfiguration.Instance.EnableServiceCacheHealthCheck}");
                Console.WriteLine("✓ Configuration integration working");

                // Test 7: Test factory method
                Console.WriteLine("\n7. Testing factory method...");
                using var factoryManager = HealthCheckFactory.CreateComprehensiveHealthCheckManager(
                    chordNode: chordNode,
                    directoryNode: directoryNode,
                    networkSender: udpSender,
                    networkReceiver: udpReceiver,
                    serviceCache: serviceCache
                );
                
                var factoryReport = await factoryManager.CheckHealthAsync();
                Console.WriteLine($"✓ Factory created manager with {factoryReport.Entries.Count} health checks");
                Console.WriteLine($"  Overall status: {factoryReport.Status}");

                // Cleanup
                udpSender.Dispose();
                await udpReceiver.DisposeAsync();

                Console.WriteLine("\n=== All Tests Completed Successfully ===");
                Console.WriteLine("✅ Health Check System is working correctly!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"\n❌ Test failed: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                throw;
            }
        }
    }
}
