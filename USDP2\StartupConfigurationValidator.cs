using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace USDP2
{
    /// <summary>
    /// Provides startup configuration validation for USDP2 applications.
    /// 
    /// This class integrates configuration validation into the application startup process,
    /// ensuring that invalid configurations are caught early and provide clear feedback
    /// to developers and system administrators.
    /// 
    /// Features:
    /// - Automatic validation during application startup
    /// - Configurable validation behavior (strict vs. lenient)
    /// - Comprehensive logging of validation results
    /// - Integration with dependency injection containers
    /// - Support for custom validation rules
    /// </summary>
    public static class StartupConfigurationValidator
    {
        /// <summary>
        /// Validation behavior options for startup.
        /// </summary>
        public enum ValidationBehavior
        {
            /// <summary>Strict - Fail startup on any errors or critical issues.</summary>
            Strict,
            /// <summary>Lenient - Only fail startup on critical issues, log warnings and errors.</summary>
            Lenient,
            /// <summary>LogOnly - Log all validation results but never fail startup.</summary>
            LogOnly,
            /// <summary>Disabled - Skip validation entirely.</summary>
            Disabled
        }

        /// <summary>
        /// Validates configuration at application startup with the specified behavior.
        /// </summary>
        /// <param name="config">Configuration to validate. If null, uses UsdpConfiguration.Instance.</param>
        /// <param name="behavior">Validation behavior to apply.</param>
        /// <param name="customValidators">Optional custom validation functions.</param>
        /// <returns>True if validation passed or was skipped, false if validation failed.</returns>
        /// <exception cref="InvalidOperationException">Thrown when validation fails in strict mode.</exception>
        public static bool ValidateAtStartup(
            UsdpConfiguration? config = null,
            ValidationBehavior behavior = ValidationBehavior.Strict,
            params Func<UsdpConfiguration, List<ValidationResult>>[] customValidators)
        {
            if (behavior == ValidationBehavior.Disabled)
            {
                UsdpLogger.Log("Configuration.ValidationSkipped", new
                {
                    Reason = "Validation is disabled",
                    Behavior = behavior.ToString()
                });
                return true;
            }

            config ??= UsdpConfiguration.Instance;

            try
            {
                UsdpLogger.Log("Configuration.ValidationStarted", new
                {
                    Behavior = behavior.ToString(),
                    HasCustomValidators = customValidators?.Length > 0
                });

                // Run standard validation
                var results = ValidateConfiguration.ValidateAll(config);

                // Run custom validators if provided
                if (customValidators != null)
                {
                    foreach (var validator in customValidators)
                    {
                        try
                        {
                            var customResults = validator(config);
                            results.AddRange(customResults);
                        }
                        catch (Exception ex)
                        {
                            UsdpLogger.Log("Configuration.CustomValidatorError", new
                            {
                                ValidatorType = validator.Method.DeclaringType?.Name,
                                Error = ex.Message
                            });

                            results.Add(new ValidationResult
                            {
                                Severity = ValidationSeverity.Error,
                                PropertyName = "CustomValidator",
                                Message = $"Custom validator failed: {ex.Message}",
                                CurrentValue = validator.Method.Name,
                                RecommendedAction = "Check custom validator implementation"
                            });
                        }
                    }
                }

                // Sort results by severity
                results = results.OrderBy(r => r.Severity).ToList();

                // Log validation results
                ValidateConfiguration.LogValidationResults(results);

                // Analyze results based on behavior
                var criticalCount = results.Count(r => r.Severity == ValidationSeverity.Critical);
                var errorCount = results.Count(r => r.Severity == ValidationSeverity.Error);
                var warningCount = results.Count(r => r.Severity == ValidationSeverity.Warning);

                var validationSummary = new
                {
                    TotalIssues = results.Count,
                    Critical = criticalCount,
                    Errors = errorCount,
                    Warnings = warningCount,
                    Behavior = behavior.ToString(),
                    ValidationPassed = DetermineValidationResult(behavior, criticalCount, errorCount)
                };

                UsdpLogger.Log("Configuration.ValidationCompleted", validationSummary);

                // Apply validation behavior
                switch (behavior)
                {
                    case ValidationBehavior.Strict:
                        if (criticalCount > 0 || errorCount > 0)
                        {
                            var errorMessages = results
                                .Where(r => r.Severity == ValidationSeverity.Critical || r.Severity == ValidationSeverity.Error)
                                .Select(r => $"{r.Severity}: {r.PropertyName} - {r.Message}")
                                .ToList();

                            throw new InvalidOperationException(
                                $"Configuration validation failed in strict mode with {errorMessages.Count} error(s):\n" +
                                string.Join("\n", errorMessages) + "\n\n" +
                                "To resolve these issues:\n" +
                                string.Join("\n", results
                                    .Where(r => r.Severity == ValidationSeverity.Critical || r.Severity == ValidationSeverity.Error)
                                    .Where(r => !string.IsNullOrEmpty(r.RecommendedAction))
                                    .Select(r => $"- {r.PropertyName}: {r.RecommendedAction}")));
                        }
                        break;

                    case ValidationBehavior.Lenient:
                        if (criticalCount > 0)
                        {
                            var criticalMessages = results
                                .Where(r => r.Severity == ValidationSeverity.Critical)
                                .Select(r => $"CRITICAL: {r.PropertyName} - {r.Message}")
                                .ToList();

                            throw new InvalidOperationException(
                                $"Configuration validation failed with {criticalCount} critical error(s):\n" +
                                string.Join("\n", criticalMessages));
                        }
                        break;

                    case ValidationBehavior.LogOnly:
                        // Just log, never fail
                        break;
                }

                return true;
            }
            catch (Exception ex) when (behavior != ValidationBehavior.Strict && behavior != ValidationBehavior.Lenient)
            {
                UsdpLogger.Log("Configuration.ValidationException", new
                {
                    Error = ex.Message,
                    Behavior = behavior.ToString(),
                    ExceptionType = ex.GetType().Name
                });

                // In LogOnly mode, don't fail even on exceptions
                return behavior == ValidationBehavior.LogOnly;
            }
        }

        /// <summary>
        /// Validates configuration asynchronously at application startup.
        /// </summary>
        /// <param name="config">Configuration to validate.</param>
        /// <param name="behavior">Validation behavior to apply.</param>
        /// <param name="customValidators">Optional custom validation functions.</param>
        /// <returns>Task that completes when validation is finished.</returns>
        public static async Task<bool> ValidateAtStartupAsync(
            UsdpConfiguration? config = null,
            ValidationBehavior behavior = ValidationBehavior.Strict,
            params Func<UsdpConfiguration, List<ValidationResult>>[] customValidators)
        {
            return await Task.Run(() => ValidateAtStartup(config, behavior, customValidators));
        }

        /// <summary>
        /// Creates a validation summary report for monitoring and diagnostics.
        /// </summary>
        /// <param name="config">Configuration to validate.</param>
        /// <returns>Validation summary report.</returns>
        public static ValidationSummaryReport CreateValidationReport(UsdpConfiguration? config = null)
        {
            config ??= UsdpConfiguration.Instance;
            var results = ValidateConfiguration.ValidateAll(config);

            return new ValidationSummaryReport
            {
                ValidationTimestamp = DateTime.UtcNow,
                TotalIssues = results.Count,
                CriticalIssues = results.Count(r => r.Severity == ValidationSeverity.Critical),
                ErrorIssues = results.Count(r => r.Severity == ValidationSeverity.Error),
                WarningIssues = results.Count(r => r.Severity == ValidationSeverity.Warning),
                InfoIssues = results.Count(r => r.Severity == ValidationSeverity.Info),
                IsValid = results.Count(r => r.Severity == ValidationSeverity.Critical || r.Severity == ValidationSeverity.Error) == 0,
                ValidationResults = results,
                ConfigurationSnapshot = CreateConfigurationSnapshot(config)
            };
        }

        /// <summary>
        /// Validates configuration and provides startup recommendations.
        /// </summary>
        /// <param name="config">Configuration to analyze.</param>
        /// <returns>Startup recommendations based on configuration analysis.</returns>
        public static StartupRecommendations GetStartupRecommendations(UsdpConfiguration? config = null)
        {
            config ??= UsdpConfiguration.Instance;
            var results = ValidateConfiguration.ValidateAll(config);

            var recommendations = new StartupRecommendations();

            // Analyze security recommendations
            if (config.DefaultSecurity == "none")
            {
                recommendations.SecurityRecommendations.Add(
                    "Consider enabling security with 'psk-tls1.3' or 'cert-tls1.3' for production environments");
            }

            // Analyze performance recommendations
            if (config.DefaultBufferSize < 4096)
            {
                recommendations.PerformanceRecommendations.Add(
                    "Consider increasing DefaultBufferSize to 4096 or higher for better performance");
            }

            // Analyze reliability recommendations
            if (!config.EnableCircuitBreakers)
            {
                recommendations.ReliabilityRecommendations.Add(
                    "Enable circuit breakers for improved network resilience");
            }

            // Analyze monitoring recommendations
            if (config.LogMode == UsdpConfiguration.LoggingMode.None)
            {
                recommendations.MonitoringRecommendations.Add(
                    "Enable logging for better observability and troubleshooting");
            }

            // Add recommendations from validation results
            foreach (var result in results.Where(r => !string.IsNullOrEmpty(r.RecommendedAction)))
            {
                recommendations.GeneralRecommendations.Add(
                    $"{result.PropertyName}: {result.RecommendedAction}");
            }

            return recommendations;
        }

        /// <summary>
        /// Determines if validation passed based on behavior and error counts.
        /// </summary>
        private static bool DetermineValidationResult(ValidationBehavior behavior, int criticalCount, int errorCount)
        {
            return behavior switch
            {
                ValidationBehavior.Strict => criticalCount == 0 && errorCount == 0,
                ValidationBehavior.Lenient => criticalCount == 0,
                ValidationBehavior.LogOnly => true,
                ValidationBehavior.Disabled => true,
                _ => false
            };
        }

        /// <summary>
        /// Creates a snapshot of current configuration for reporting.
        /// </summary>
        private static object CreateConfigurationSnapshot(UsdpConfiguration config)
        {
            return new
            {
                // Network Configuration
                DefaultMulticastAddress = config.DefaultMulticastAddress,
                DefaultMulticastPort = config.DefaultMulticastPort,
                DefaultServiceAddress = config.DefaultServiceAddress,
                DefaultServicePort = config.DefaultServicePort,
                DefaultHttpPort = config.DefaultHttpPort,
                DefaultHttpsPort = config.DefaultHttpsPort,

                // Security Configuration
                DefaultSecurity = config.DefaultSecurity,
                RequireAuthentication = config.RequireAuthentication,
                UseHttps = config.UseHttps,

                // Timeout Configuration
                NetworkTimeout = config.NetworkTimeout,
                QueryResponseTimeout = config.QueryResponseTimeout,
                DefaultTtl = config.DefaultTtl,
                MaxTtl = config.MaxTtl,

                // Circuit Breaker Configuration
                EnableCircuitBreakers = config.EnableCircuitBreakers,
                EnableCircuitBreakerMonitoring = config.EnableCircuitBreakerMonitoring,

                // Logging Configuration
                LogMode = config.LogMode,
                EventLogName = config.EventLogName
            };
        }
    }

    /// <summary>
    /// Validation summary report for monitoring and diagnostics.
    /// </summary>
    public class ValidationSummaryReport
    {
        public DateTime ValidationTimestamp { get; set; }
        public int TotalIssues { get; set; }
        public int CriticalIssues { get; set; }
        public int ErrorIssues { get; set; }
        public int WarningIssues { get; set; }
        public int InfoIssues { get; set; }
        public bool IsValid { get; set; }
        public List<ValidationResult> ValidationResults { get; set; } = new();
        public object? ConfigurationSnapshot { get; set; }
    }

    /// <summary>
    /// Startup recommendations based on configuration analysis.
    /// </summary>
    public class StartupRecommendations
    {
        public List<string> SecurityRecommendations { get; set; } = new();
        public List<string> PerformanceRecommendations { get; set; } = new();
        public List<string> ReliabilityRecommendations { get; set; } = new();
        public List<string> MonitoringRecommendations { get; set; } = new();
        public List<string> GeneralRecommendations { get; set; } = new();
    }
}
