using System;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.VisualStudio.TestTools.UnitTesting;

namespace USDP2.Tests
{
    /// <summary>
    /// Comprehensive tests for HttpNetworkSender focusing on TLS configuration and safeguards.
    /// Tests the enhanced TLS override functionality and fallback mechanisms.
    /// </summary>
    [TestClass]
    public class HttpNetworkSenderTlsTests
    {
        private const string TestMessage = "TLS Test Message";
        private static readonly byte[] TestData = Encoding.UTF8.GetBytes(TestMessage);

        [TestMethod]
        public void HttpNetworkSender_Constructor_DefaultConfiguration_Success()
        {
            // Act & Assert - Should not throw
            using var sender = new HttpNetworkSender();
            Assert.IsNotNull(sender);
        }

        [TestMethod]
        public void HttpNetworkSender_Constructor_CustomConfiguration_Success()
        {
            // Arrange - Use the singleton instance and modify properties
            var config = UsdpConfiguration.Instance;
            var originalUseHttps = config.UseHttps;
            var originalEnableManualTlsOverride = config.EnableManualTlsOverride;
            var originalUseOSManagedTls = config.UseOSManagedTls;

            try
            {
                config.UseHttps = true;
                config.EnableManualTlsOverride = false;
                config.UseOSManagedTls = true;

                // Act & Assert - Should not throw
                using var sender = new HttpNetworkSender(config);
                Assert.IsNotNull(sender);
            }
            finally
            {
                // Restore original values
                config.UseHttps = originalUseHttps;
                config.EnableManualTlsOverride = originalEnableManualTlsOverride;
                config.UseOSManagedTls = originalUseOSManagedTls;
            }
        }

        [TestMethod]
        public void HttpNetworkSender_Constructor_ManualTlsOverrideEnabled_WithValidConfig_Success()
        {
            // Arrange
            var config = UsdpConfiguration.Instance;
            var originalEnableManualTlsOverride = config.EnableManualTlsOverride;
            var originalUseHttps = config.UseHttps;

            try
            {
                config.EnableManualTlsOverride = true;
                config.UseHttps = true;

                var tlsConfig = new TlsOverrideConfiguration
                {
                    AllowedTlsVersions = new() { "1.3", "1.2" },
                    CustomCipherSuites = new() { "TLS_AES_256_GCM_SHA384" },
                    StrictCertificateValidation = true
                };

                // Act & Assert - Should not throw
                using var sender = new HttpNetworkSender(config, tlsConfig);
                Assert.IsNotNull(sender);
            }
            finally
            {
                // Restore original values
                config.EnableManualTlsOverride = originalEnableManualTlsOverride;
                config.UseHttps = originalUseHttps;
            }
        }

        [TestMethod]
        public void HttpNetworkSender_Constructor_ManualTlsOverrideEnabled_WithNullConfig_FallsBackToOSManaged()
        {
            // Arrange
            var config = UsdpConfiguration.Instance;
            var originalEnableManualTlsOverride = config.EnableManualTlsOverride;
            var originalUseHttps = config.UseHttps;

            try
            {
                config.EnableManualTlsOverride = true;
                config.UseHttps = true;

                // Act & Assert - Should not throw and should fall back to OS-managed TLS
                using var sender = new HttpNetworkSender(config, tlsOverrideConfig: null);
                Assert.IsNotNull(sender);
            }
            finally
            {
                // Restore original values
                config.EnableManualTlsOverride = originalEnableManualTlsOverride;
                config.UseHttps = originalUseHttps;
            }
        }

        [TestMethod]
        public void HttpNetworkSender_Constructor_ManualTlsOverrideDisabled_IgnoresTlsConfig()
        {
            // Arrange
            var config = UsdpConfiguration.Instance;
            var originalEnableManualTlsOverride = config.EnableManualTlsOverride;
            var originalUseHttps = config.UseHttps;

            try
            {
                config.EnableManualTlsOverride = false; // Disabled
                config.UseHttps = true;

                var tlsConfig = new TlsOverrideConfiguration
                {
                    AllowedTlsVersions = new() { "1.3" }
                };

                // Act & Assert - Should not throw and should ignore TLS override config
                using var sender = new HttpNetworkSender(config, tlsConfig);
                Assert.IsNotNull(sender);
            }
            finally
            {
                // Restore original values
                config.EnableManualTlsOverride = originalEnableManualTlsOverride;
                config.UseHttps = originalUseHttps;
            }
        }

        [TestMethod]
        public async Task HttpNetworkSender_SendAsync_OSManagedTls_HandlesHttpsGracefully()
        {
            // Arrange - Use default configuration which should have secure defaults
            using var sender = new HttpNetworkSender();

            // Act & Assert - Should handle HTTPS gracefully (may fail due to test environment)
            try
            {
                await sender.SendAsync(TestData, "httpbin.org", 443);
                Assert.IsTrue(true, "HTTPS request succeeded");
            }
            catch (HttpRequestException)
            {
                Assert.IsTrue(true, "HTTPS request failed as expected in test environment");
            }
            catch (InvalidOperationException ex) when (ex.Message.Contains("TLS fallback attempts failed"))
            {
                Assert.IsTrue(true, "TLS fallback failed as expected in test environment");
            }
        }

        [TestMethod]
        public async Task HttpNetworkSender_SendAsync_HttpFallback_Success()
        {
            // Arrange - Configure for HTTP (not HTTPS) to test HTTP functionality
            var config = UsdpConfiguration.Instance;
            var originalUseHttps = config.UseHttps;

            try
            {
                config.UseHttps = false; // Use HTTP for this test
                using var sender = new HttpNetworkSender(config);

                // Act & Assert - Should handle HTTP gracefully
                try
                {
                    await sender.SendAsync(TestData, "httpbin.org", 80);
                    Assert.IsTrue(true, "HTTP request succeeded");
                }
                catch (HttpRequestException)
                {
                    Assert.IsTrue(true, "HTTP request failed - external dependency issue");
                }
            }
            finally
            {
                // Restore original value
                config.UseHttps = originalUseHttps;
            }
        }

        [TestMethod]
        public async Task HttpNetworkSender_SendAsync_CancellationToken_RespectsCancellation()
        {
            // Arrange
            using var sender = new HttpNetworkSender();
            var cts = new CancellationTokenSource();
            cts.Cancel(); // Cancel immediately

            // Act & Assert
            await Assert.ThrowsExceptionAsync<TaskCanceledException>(
                () => sender.SendAsync(TestData, "httpbin.org", 80, cts.Token));
        }

        [TestMethod]
        public async Task HttpNetworkSender_SendAsync_InvalidAddress_ThrowsException()
        {
            // Arrange
            using var sender = new HttpNetworkSender();

            // Act & Assert
            await Assert.ThrowsExceptionAsync<HttpRequestException>(
                () => sender.SendAsync(TestData, "invalid-domain-that-does-not-exist.invalid", 80));
        }

        [TestMethod]
        public void HttpNetworkSender_Dispose_MultipleCallsSafe()
        {
            // Arrange
            var sender = new HttpNetworkSender();

            // Act & Assert - Multiple dispose calls should be safe
            sender.Dispose();
            sender.Dispose(); // Should not throw
            Assert.IsTrue(true);
        }

        [TestMethod]
        public async Task HttpNetworkSender_SendAsync_AfterDispose_ThrowsObjectDisposedException()
        {
            // Arrange
            var sender = new HttpNetworkSender();
            sender.Dispose();

            // Act & Assert
            await Assert.ThrowsExceptionAsync<ObjectDisposedException>(
                () => sender.SendAsync(TestData, "localhost", 80));
        }

        [TestMethod]
        public void HttpNetworkSender_Constructor_TlsOverrideProviderException_FallsBackGracefully()
        {
            // This test simulates what happens if TlsOverrideProvider fails to instantiate
            // The enhanced safeguards should handle this gracefully

            // Arrange
            var config = UsdpConfiguration.Instance;
            var originalEnableManualTlsOverride = config.EnableManualTlsOverride;
            var originalUseHttps = config.UseHttps;

            try
            {
                config.EnableManualTlsOverride = true;
                config.UseHttps = true;

                // Create an invalid TLS configuration that might cause TlsOverrideProvider to fail
                var invalidTlsConfig = new TlsOverrideConfiguration
                {
                    AllowedTlsVersions = new() { "InvalidVersion" }, // Invalid TLS version
                    StrictCertificateValidation = true
                };

                // Act & Assert - Should not throw, should fall back to OS-managed TLS
                using var sender = new HttpNetworkSender(config, invalidTlsConfig);
                Assert.IsNotNull(sender);
            }
            finally
            {
                // Restore original values
                config.EnableManualTlsOverride = originalEnableManualTlsOverride;
                config.UseHttps = originalUseHttps;
            }
        }

        [TestMethod]
        public async Task HttpNetworkSender_TlsFallback_WithFallbackEnabled_AttemptsMultipleTlsVersions()
        {
            // Arrange - Use default configuration which should have fallback enabled
            using var sender = new HttpNetworkSender();

            // Act & Assert - Should attempt TLS fallback if initial connection fails
            try
            {
                await sender.SendAsync(TestData, "httpbin.org", 443);
                Assert.IsTrue(true, "TLS connection succeeded");
            }
            catch (HttpRequestException)
            {
                Assert.IsTrue(true, "TLS connection failed as expected in test environment");
            }
            catch (InvalidOperationException ex) when (ex.Message.Contains("TLS fallback attempts failed"))
            {
                Assert.IsTrue(true, "TLS fallback exhausted as expected in test environment");
            }
        }

        [TestMethod]
        public async Task HttpNetworkSender_TlsFallback_WithFallbackDisabled_NoFallbackAttempts()
        {
            // Arrange - Test with fallback disabled
            var config = UsdpConfiguration.Instance;
            var originalEnableTlsFallback = config.EnableTlsFallback;

            try
            {
                config.EnableTlsFallback = false; // Disable fallback
                using var sender = new HttpNetworkSender(config);

                // Act & Assert - Should not attempt fallback
                try
                {
                    await sender.SendAsync(TestData, "httpbin.org", 443);
                    Assert.IsTrue(true, "Direct TLS connection succeeded");
                }
                catch (HttpRequestException)
                {
                    Assert.IsTrue(true, "Direct TLS connection failed as expected (no fallback)");
                }
            }
            finally
            {
                // Restore original value
                config.EnableTlsFallback = originalEnableTlsFallback;
            }
        }

        [TestMethod]
        public void HttpNetworkSender_Configuration_DefaultValues_AreSecure()
        {
            // Arrange & Act
            var config = UsdpConfiguration.Instance;

            // Assert - Verify secure defaults
            Assert.IsTrue(config.UseOSManagedTls, "Should default to OS-managed TLS");
            Assert.IsFalse(config.EnableManualTlsOverride, "Manual TLS override should be disabled by default");
            Assert.IsTrue(config.UseHttps, "Should default to HTTPS");
            Assert.IsTrue(config.EnableTlsFallback, "TLS fallback should be enabled by default");
        }
    }
}
