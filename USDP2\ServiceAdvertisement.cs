using System;
using System.Collections.Generic;
using NSec.Cryptography;

namespace USDP2
{
    /// <summary>
    /// Represents a service advertisement with serialization, signing, and verification capabilities.
    /// </summary>
    public sealed class ServiceAdvertisement
    {
        private ServiceIdentifier _serviceId;
        private TransportEndpoint _endpoint;
        private Dictionary<string, object> _metadata = new();
        private TimeSpan? _ttl;

        /// <summary>
        /// Initializes a new instance of the <see cref="ServiceAdvertisement"/> class.
        /// </summary>
        /// <param name="serviceId">The service identifier.</param>
        /// <param name="endpoint">The transport endpoint.</param>
        public ServiceAdvertisement(ServiceIdentifier serviceId, TransportEndpoint endpoint)
        {
            ArgumentNullException.ThrowIfNull(serviceId, nameof(serviceId));
            ArgumentNullException.ThrowIfNull(endpoint, nameof(endpoint));

            _serviceId = serviceId;
            _endpoint = endpoint;
            _ttl = USDP2.UsdpConfiguration.Instance.DefaultTtl;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="ServiceAdvertisement"/> class for serialization.
        /// </summary>
        public ServiceAdvertisement()
        {
            _serviceId = new ServiceIdentifier();
            _endpoint = new TransportEndpoint();
            _ttl = null; // Will be set by deserializer
        }

        /// <summary>
        /// Gets or sets the service id.
        /// </summary>
        public ServiceIdentifier ServiceId
        {
            get => _serviceId;
            set
            {
                ArgumentNullException.ThrowIfNull(value, nameof(ServiceId));
                InputValidator.ValidateServiceIdentifier(value, nameof(ServiceId));
                _serviceId = value;
            }
        }

        /// <summary>
        /// Gets or sets the endpoint.
        /// </summary>
        public TransportEndpoint Endpoint
        {
            get => _endpoint;
            set
            {
                ArgumentNullException.ThrowIfNull(value, nameof(Endpoint));
                InputValidator.ValidateTransportEndpoint(value, nameof(Endpoint));
                _endpoint = value;
            }
        }

        /// <summary>
        /// Gets or sets the metadata.
        /// </summary>
        public Dictionary<string, object> Metadata
        {
            get => _metadata;
            set
            {
                ArgumentNullException.ThrowIfNull(value, nameof(Metadata));
                InputValidator.ValidateMetadata(value, nameof(Metadata));
                _metadata = value;
            }
        }

        /// <summary>
        /// Gets or sets the timestamp.
        /// </summary>
        public DateTimeOffset Timestamp { get; set; } = DateTimeOffset.UtcNow;

        /// <summary>
        /// Gets or sets the time-to-live (TTL).
        /// </summary>
        public TimeSpan? Ttl
        {
            get => _ttl;
            set
            {
                if (value.HasValue && (value.Value.TotalMilliseconds < 0 || value.Value > UsdpConfiguration.Instance.MaxTtl))
                {
                    throw new ArgumentOutOfRangeException(nameof(Ttl),
                        $"TTL must be between 0 and {UsdpConfiguration.Instance.MaxTtl.TotalDays} days.");
                }
                _ttl = value;
            }
        }

        /// <summary>
        /// Gets or sets the signature.
        /// </summary>
        public byte[]? Signature { get; set; }

        /// <summary>
        /// Converts the advertisement to a JSON string.
        /// </summary>
        public string ToJson() => USDPSerializer.ToJson(this);

        /// <summary>
        /// Converts the advertisement to a JSON string with detailed error information.
        /// </summary>
        public SerializationResult<string> ToJsonWithResult() => USDPSerializer.ToJsonWithResult(this);

        /// <summary>
        /// Deserializes a service advertisement from a JSON string.
        /// </summary>
        public static ServiceAdvertisement? FromJson(string json) => USDPSerializer.FromJson<ServiceAdvertisement>(json);

        /// <summary>
        /// Deserializes a service advertisement from a JSON string with detailed error information.
        /// </summary>
        public static SerializationResult<ServiceAdvertisement> FromJsonWithResult(string json) => USDPSerializer.FromJsonWithResult<ServiceAdvertisement>(json);

        /// <summary>
        /// Converts the advertisement to CBOR binary format.
        /// </summary>
        public byte[] ToCbor() => USDPSerializer.ToCbor(this);

        /// <summary>
        /// Converts the advertisement to CBOR binary format with detailed error information.
        /// </summary>
        public SerializationResult<byte[]> ToCborWithResult() => USDPSerializer.ToCborWithResult(this);

        /// <summary>
        /// Deserializes a service advertisement from CBOR binary data.
        /// </summary>
        public static ServiceAdvertisement? FromCbor(byte[] cbor) => USDPSerializer.FromCbor<ServiceAdvertisement>(cbor);

        /// <summary>
        /// Deserializes a service advertisement from CBOR binary data with detailed error information.
        /// </summary>
        public static SerializationResult<ServiceAdvertisement> FromCborWithResult(byte[] cbor) => USDPSerializer.FromCborWithResult<ServiceAdvertisement>(cbor);

        /// <summary>
        /// Signs the advertisement using the provided Ed25519 private key.
        /// </summary>
        public void Sign(Key privateKey)
        {
            ArgumentNullException.ThrowIfNull(privateKey, nameof(privateKey));

            if (privateKey.Algorithm != SignatureAlgorithm.Ed25519)
                throw new ArgumentException("Key must be an Ed25519 key.", nameof(privateKey));

            var data = GetSignableData();
            Signature = Ed25519Helper.Sign(data, privateKey);
        }

        /// <summary>
        /// Verifies the signature of the advertisement using the provided Ed25519 public key.
        /// </summary>
        public bool Verify(PublicKey publicKey)
        {
            ArgumentNullException.ThrowIfNull(publicKey, nameof(publicKey));

            if (publicKey.Algorithm != SignatureAlgorithm.Ed25519)
                throw new ArgumentException("Key must be an Ed25519 key.", nameof(publicKey));

            if (Signature == null)
            {
                Diagnostics.Log("VerificationWarning", new { message = "Cannot verify advertisement: Signature is null" });
                return false;
            }

            try
            {
                var data = GetSignableData();
                return Ed25519Helper.Verify(data, Signature, publicKey);
            }
            catch (Exception ex)
            {
                Diagnostics.Log("VerificationError", new { message = $"Signature verification failed: {ex.Message}" });
                return false;
            }
        }

        /// <summary>
        /// Creates a serializable representation of the advertisement for signing.
        /// </summary>
        private byte[] GetSignableData()
        {
            var signableObject = new
            {
                ServiceId,
                Endpoint,
                Metadata,
                Timestamp,
                Ttl
            };

            return USDPSerializer.ToCbor(signableObject);
        }
    }
}
