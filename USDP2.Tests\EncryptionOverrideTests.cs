using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.IO;
using System.Text;
using System.Threading.Tasks;
using USDP2;

namespace USDP2.Tests
{
    /// <summary>
    /// Tests for the EncryptionOverride removable class functionality.
    /// </summary>
    [TestClass]
    public class EncryptionOverrideTests
    {
        private string _testFilePath = string.Empty;

        [TestInitialize]
        public void Setup()
        {
            _testFilePath = Path.Combine(Path.GetTempPath(), $"test_encryption_{Guid.NewGuid()}.txt");
        }

        [TestCleanup]
        public void Cleanup()
        {
            if (File.Exists(_testFilePath))
            {
                try
                {
                    File.Delete(_testFilePath);
                }
                catch
                {
                    // Ignore cleanup errors in tests
                }
            }
        }

        [TestMethod]
        public async Task EncryptionOverride_EncryptAndDecryptData_AES128_WorksCorrectly()
        {
            // Arrange
            var originalData = Encoding.UTF8.GetBytes("This is test data for AES-128-GCM encryption");

            // Act
            var encryptedData = await EncryptionOverride.EncryptDataAsync(originalData, EncryptionAlgorithm.AES128GCM);
            var decryptedData = await EncryptionOverride.DecryptDataAsync(encryptedData);

            // Assert
            Assert.IsNotNull(encryptedData);
            Assert.IsNotNull(decryptedData);
            Assert.AreNotEqual(originalData.Length, encryptedData.Length, "Encrypted data should be different size");
            CollectionAssert.AreEqual(originalData, decryptedData, "Decrypted data should match original");
        }

        [TestMethod]
        public async Task EncryptionOverride_EncryptAndDecryptData_AES256_WorksCorrectly()
        {
            // Arrange
            var originalData = Encoding.UTF8.GetBytes("This is test data for AES-256-GCM encryption");

            // Act
            var encryptedData = await EncryptionOverride.EncryptDataAsync(originalData, EncryptionAlgorithm.AES256GCM);
            var decryptedData = await EncryptionOverride.DecryptDataAsync(encryptedData);

            // Assert
            Assert.IsNotNull(encryptedData);
            Assert.IsNotNull(decryptedData);
            Assert.AreNotEqual(originalData.Length, encryptedData.Length, "Encrypted data should be different size");
            CollectionAssert.AreEqual(originalData, decryptedData, "Decrypted data should match original");
        }

        [TestMethod]
        public async Task EncryptionOverride_EncryptAndDecryptString_WorksCorrectly()
        {
            // Arrange
            var originalString = "This is a test string for encryption";

            // Act
            var encryptedData = await EncryptionOverride.EncryptStringAsync(originalString);
            var decryptedString = await EncryptionOverride.DecryptStringAsync(encryptedData);

            // Assert
            Assert.IsNotNull(encryptedData);
            Assert.IsNotNull(decryptedString);
            Assert.AreEqual(originalString, decryptedString);
        }

        [TestMethod]
        public async Task EncryptionOverride_EncryptAndDecryptFile_WorksCorrectly()
        {
            // Arrange
            var originalContent = "This is test file content for encryption testing";
            await File.WriteAllTextAsync(_testFilePath, originalContent);

            // Act
            await EncryptionOverride.EncryptFileAsync(_testFilePath);
            var encryptedContent = await File.ReadAllTextAsync(_testFilePath);

            await EncryptionOverride.DecryptFileAsync(_testFilePath);
            var decryptedContent = await File.ReadAllTextAsync(_testFilePath);

            // Assert
            Assert.AreNotEqual(originalContent, encryptedContent, "File should be encrypted");
            Assert.AreEqual(originalContent, decryptedContent, "Decrypted content should match original");
        }

        [TestMethod]
        public async Task EncryptionOverride_IsEncryptionAvailable_ReturnsTrue()
        {
            // Act
            var isAvailable = await EncryptionOverride.IsEncryptionAvailableAsync();

            // Assert
            Assert.IsTrue(isAvailable, "Encryption should be available with default settings");
        }

        [TestMethod]
        public async Task EncryptionOverride_EncryptEmptyData_WorksCorrectly()
        {
            // Arrange
            var emptyData = Array.Empty<byte>();

            // Act
            var encryptedData = await EncryptionOverride.EncryptDataAsync(emptyData);
            var decryptedData = await EncryptionOverride.DecryptDataAsync(encryptedData);

            // Assert
            Assert.IsNotNull(encryptedData);
            Assert.IsNotNull(decryptedData);
            Assert.AreEqual(0, decryptedData.Length, "Decrypted empty data should be empty");
        }

        [TestMethod]
        public async Task EncryptionOverride_EncryptLargeData_WorksCorrectly()
        {
            // Arrange
            var largeData = new byte[1024 * 1024]; // 1MB
            new Random().NextBytes(largeData);

            // Act
            var encryptedData = await EncryptionOverride.EncryptDataAsync(largeData);
            var decryptedData = await EncryptionOverride.DecryptDataAsync(encryptedData);

            // Assert
            Assert.IsNotNull(encryptedData);
            Assert.IsNotNull(decryptedData);
            CollectionAssert.AreEqual(largeData, decryptedData, "Large data should decrypt correctly");
        }

        [TestMethod]
        public async Task EncryptionOverride_DecryptWithWrongData_ThrowsException()
        {
            // Arrange
            var invalidData = new byte[] { 1, 2, 3, 4, 5 }; // Too short to be valid encrypted data

            // Act & Assert
            await Assert.ThrowsExceptionAsync<ArgumentException>(
                async () => await EncryptionOverride.DecryptDataAsync(invalidData),
                "Should throw exception for invalid encrypted data");
        }

        [TestMethod]
        public async Task EncryptionOverride_EncryptNullData_ThrowsException()
        {
            // Act & Assert
            await Assert.ThrowsExceptionAsync<ArgumentNullException>(
                async () => await EncryptionOverride.EncryptDataAsync(null!),
                "Should throw exception for null data");
        }

        [TestMethod]
        public async Task EncryptionOverride_EncryptNullString_ThrowsException()
        {
            // Act & Assert
            await Assert.ThrowsExceptionAsync<ArgumentNullException>(
                async () => await EncryptionOverride.EncryptStringAsync(null!),
                "Should throw exception for null string");
        }

        [TestMethod]
        public async Task EncryptionOverride_EncryptNonExistentFile_ThrowsException()
        {
            // Arrange
            var nonExistentFile = Path.Combine(Path.GetTempPath(), "non_existent_file.txt");

            // Act & Assert
            await Assert.ThrowsExceptionAsync<FileNotFoundException>(
                async () => await EncryptionOverride.EncryptFileAsync(nonExistentFile),
                "Should throw exception for non-existent file");
        }

        [TestMethod]
        public async Task EncryptionOverride_MultipleEncryptionsProduceDifferentResults()
        {
            // Arrange
            var originalData = Encoding.UTF8.GetBytes("Same data for multiple encryptions");

            // Act
            var encrypted1 = await EncryptionOverride.EncryptDataAsync(originalData);
            var encrypted2 = await EncryptionOverride.EncryptDataAsync(originalData);

            // Assert
            CollectionAssert.AreNotEqual(encrypted1, encrypted2,
                "Multiple encryptions of same data should produce different results due to random nonce");

            // But both should decrypt to the same original data
            var decrypted1 = await EncryptionOverride.DecryptDataAsync(encrypted1);
            var decrypted2 = await EncryptionOverride.DecryptDataAsync(encrypted2);

            CollectionAssert.AreEqual(originalData, decrypted1);
            CollectionAssert.AreEqual(originalData, decrypted2);
        }

        [TestMethod]
        public async Task EncryptionOverride_KeyRotation_WorksCorrectly()
        {
            // Arrange
            var testData = Encoding.UTF8.GetBytes("Test data for key rotation");

            // Encrypt with original key
            var encrypted1 = await EncryptionOverride.EncryptDataAsync(testData);

            // Act - Rotate the key
            await EncryptionOverride.RotateEncryptionKeyAsync();

            // Encrypt with new key
            var encrypted2 = await EncryptionOverride.EncryptDataAsync(testData);

            // Assert
            CollectionAssert.AreNotEqual(encrypted1, encrypted2,
                "Encryption with rotated key should produce different results");

            // New key should work for new encryptions
            var decrypted2 = await EncryptionOverride.DecryptDataAsync(encrypted2);
            CollectionAssert.AreEqual(testData, decrypted2);

            // Note: Old encrypted data won't decrypt with new key (this is expected behavior)
        }

        [TestMethod]
        public async Task EncryptionOverride_DifferentAlgorithms_ProduceDifferentResults()
        {
            // Arrange
            var testData = Encoding.UTF8.GetBytes("Same data encrypted with different algorithms");

            // Act
            var encrypted128 = await EncryptionOverride.EncryptDataAsync(testData, EncryptionAlgorithm.AES128GCM);
            var encrypted256 = await EncryptionOverride.EncryptDataAsync(testData, EncryptionAlgorithm.AES256GCM);

            // Assert
            CollectionAssert.AreNotEqual(encrypted128, encrypted256,
                "Different algorithms should produce different encrypted results");

            // Both should decrypt correctly
            var decrypted128 = await EncryptionOverride.DecryptDataAsync(encrypted128);
            var decrypted256 = await EncryptionOverride.DecryptDataAsync(encrypted256);

            CollectionAssert.AreEqual(testData, decrypted128, "AES-128 decryption should work");
            CollectionAssert.AreEqual(testData, decrypted256, "AES-256 decryption should work");
        }

        [TestMethod]
        public async Task EncryptionOverride_AlgorithmDetection_WorksCorrectly()
        {
            // Arrange
            var testData = Encoding.UTF8.GetBytes("Test data for algorithm detection");

            // Act - Encrypt with AES-256
            var encrypted256 = await EncryptionOverride.EncryptDataAsync(testData, EncryptionAlgorithm.AES256GCM);

            // Decrypt should automatically detect AES-256 and use correct key
            var decrypted = await EncryptionOverride.DecryptDataAsync(encrypted256);

            // Assert
            CollectionAssert.AreEqual(testData, decrypted, "Algorithm detection should work correctly");
        }
    }
}
