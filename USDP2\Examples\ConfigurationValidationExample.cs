using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace USDP2.Examples
{
    /// <summary>
    /// Demonstrates how to integrate configuration validation into USDP2 application startup.
    /// 
    /// This example shows:
    /// - Basic configuration validation at startup
    /// - Different validation behaviors (strict, lenient, log-only)
    /// - Custom validation rules
    /// - Validation reporting and recommendations
    /// - Integration with application lifecycle
    /// </summary>
    public class ConfigurationValidationExample
    {
        /// <summary>
        /// Demonstrates basic configuration validation at application startup.
        /// </summary>
        public static async Task BasicStartupValidationExampleAsync()
        {
            Console.WriteLine("=== Basic Startup Validation Example ===");

            try
            {
                // Validate configuration with strict behavior (default)
                Console.WriteLine("Validating configuration with strict behavior...");
                var isValid = await StartupConfigurationValidator.ValidateAtStartupAsync();

                if (isValid)
                {
                    Console.WriteLine("✓ Configuration validation passed - application can start safely");
                }
                else
                {
                    Console.WriteLine("✗ Configuration validation failed");
                }
            }
            catch (InvalidOperationException ex)
            {
                Console.WriteLine($"✗ Configuration validation failed: {ex.Message}");
                Console.WriteLine("Application startup should be aborted.");
            }
        }

        /// <summary>
        /// Demonstrates different validation behaviors.
        /// </summary>
        public static async Task ValidationBehaviorExampleAsync()
        {
            Console.WriteLine("\n=== Validation Behavior Example ===");

            // Create a configuration with some issues for demonstration
            // Note: Since UsdpConfiguration is a singleton, we'll modify the instance temporarily
            var problematicConfig = UsdpConfiguration.Instance;
            var originalSecurity = problematicConfig.DefaultSecurity;
            var originalHttpPort = problematicConfig.DefaultHttpPort;
            var originalBufferSize = problematicConfig.DefaultBufferSize;

            // Temporarily modify for demonstration
            problematicConfig.DefaultSecurity = "none";        // Warning: security disabled
            problematicConfig.DefaultHttpPort = 80;            // Warning: well-known port
            problematicConfig.DefaultBufferSize = 512;         // Info: small buffer size

            // Test different validation behaviors
            var behaviors = new[]
            {
                StartupConfigurationValidator.ValidationBehavior.Strict,
                StartupConfigurationValidator.ValidationBehavior.Lenient,
                StartupConfigurationValidator.ValidationBehavior.LogOnly
            };

            foreach (var behavior in behaviors)
            {
                Console.WriteLine($"\nTesting {behavior} validation behavior:");

                try
                {
                    var result = await StartupConfigurationValidator.ValidateAtStartupAsync(
                        problematicConfig, behavior);

                    Console.WriteLine($"✓ Validation completed with {behavior} behavior - Result: {result}");
                }
                catch (InvalidOperationException ex)
                {
                    Console.WriteLine($"✗ Validation failed with {behavior} behavior: {ex.Message}");
                }
            }

            // Restore original values
            problematicConfig.DefaultSecurity = originalSecurity;
            problematicConfig.DefaultHttpPort = originalHttpPort;
            problematicConfig.DefaultBufferSize = originalBufferSize;
        }

        /// <summary>
        /// Demonstrates custom validation rules.
        /// </summary>
        public static async Task CustomValidationExampleAsync()
        {
            Console.WriteLine("\n=== Custom Validation Example ===");

            // Define custom validation rules
            Func<UsdpConfiguration, List<ValidationResult>> customBusinessRules = config =>
            {
                var results = new List<ValidationResult>();

                // Custom rule: Production environments should use HTTPS
                if (!config.UseHttps)
                {
                    results.Add(new ValidationResult
                    {
                        Severity = ValidationSeverity.Warning,
                        PropertyName = nameof(config.UseHttps),
                        Message = "Production environments should use HTTPS",
                        CurrentValue = config.UseHttps,
                        RecommendedAction = "Enable HTTPS for production deployments"
                    });
                }

                // Custom rule: High-performance environments should use larger buffers
                if (config.DefaultBufferSize < 8192)
                {
                    results.Add(new ValidationResult
                    {
                        Severity = ValidationSeverity.Info,
                        PropertyName = nameof(config.DefaultBufferSize),
                        Message = "Consider larger buffer sizes for high-performance scenarios",
                        CurrentValue = config.DefaultBufferSize,
                        RecommendedAction = "Set DefaultBufferSize to 8192 or higher for better performance"
                    });
                }

                // Custom rule: Validate environment-specific settings
                var environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Development";
                if (environment == "Production" && config.DefaultSecurity == "none")
                {
                    results.Add(new ValidationResult
                    {
                        Severity = ValidationSeverity.Error,
                        PropertyName = nameof(config.DefaultSecurity),
                        Message = "Production environment must not use 'none' security",
                        CurrentValue = config.DefaultSecurity,
                        RecommendedAction = "Set DefaultSecurity to 'psk-tls1.3' or 'cert-tls1.3' for production"
                    });
                }

                return results;
            };

            try
            {
                Console.WriteLine("Running validation with custom business rules...");
                var result = await StartupConfigurationValidator.ValidateAtStartupAsync(
                    null, // Use default configuration
                    StartupConfigurationValidator.ValidationBehavior.Lenient,
                    customBusinessRules);

                Console.WriteLine($"✓ Custom validation completed - Result: {result}");
            }
            catch (InvalidOperationException ex)
            {
                Console.WriteLine($"✗ Custom validation failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Demonstrates validation reporting and recommendations.
        /// </summary>
        public static void ValidationReportingExample()
        {
            Console.WriteLine("\n=== Validation Reporting Example ===");

            // Generate validation report
            var report = StartupConfigurationValidator.CreateValidationReport();

            Console.WriteLine("Configuration Validation Report:");
            Console.WriteLine($"  Validation Time: {report.ValidationTimestamp:yyyy-MM-dd HH:mm:ss} UTC");
            Console.WriteLine($"  Overall Status: {(report.IsValid ? "VALID" : "INVALID")}");
            Console.WriteLine($"  Total Issues: {report.TotalIssues}");
            Console.WriteLine($"    Critical: {report.CriticalIssues}");
            Console.WriteLine($"    Errors: {report.ErrorIssues}");
            Console.WriteLine($"    Warnings: {report.WarningIssues}");
            Console.WriteLine($"    Info: {report.InfoIssues}");

            if (report.ValidationResults.Count > 0)
            {
                Console.WriteLine("\nDetailed Issues:");
                foreach (var result in report.ValidationResults)
                {
                    Console.WriteLine($"  [{result.Severity}] {result.PropertyName}: {result.Message}");
                    if (!string.IsNullOrEmpty(result.RecommendedAction))
                    {
                        Console.WriteLine($"    → Recommendation: {result.RecommendedAction}");
                    }
                }
            }

            // Get startup recommendations
            var recommendations = StartupConfigurationValidator.GetStartupRecommendations();

            Console.WriteLine("\nStartup Recommendations:");

            if (recommendations.SecurityRecommendations.Count > 0)
            {
                Console.WriteLine("  Security:");
                foreach (var rec in recommendations.SecurityRecommendations)
                {
                    Console.WriteLine($"    • {rec}");
                }
            }

            if (recommendations.PerformanceRecommendations.Count > 0)
            {
                Console.WriteLine("  Performance:");
                foreach (var rec in recommendations.PerformanceRecommendations)
                {
                    Console.WriteLine($"    • {rec}");
                }
            }

            if (recommendations.ReliabilityRecommendations.Count > 0)
            {
                Console.WriteLine("  Reliability:");
                foreach (var rec in recommendations.ReliabilityRecommendations)
                {
                    Console.WriteLine($"    • {rec}");
                }
            }

            if (recommendations.MonitoringRecommendations.Count > 0)
            {
                Console.WriteLine("  Monitoring:");
                foreach (var rec in recommendations.MonitoringRecommendations)
                {
                    Console.WriteLine($"    • {rec}");
                }
            }
        }

        /// <summary>
        /// Demonstrates integration with application startup lifecycle.
        /// </summary>
        public static async Task ApplicationStartupIntegrationExampleAsync()
        {
            Console.WriteLine("\n=== Application Startup Integration Example ===");

            try
            {
                Console.WriteLine("Starting USDP2 application with configuration validation...");

                // Step 1: Validate configuration before any other initialization
                Console.WriteLine("1. Validating configuration...");
                await StartupConfigurationValidator.ValidateAtStartupAsync(
                    null,
                    StartupConfigurationValidator.ValidationBehavior.Strict);

                // Step 2: Initialize core services (would be actual initialization in real app)
                Console.WriteLine("2. Initializing core services...");
                await Task.Delay(100); // Simulate initialization

                // Step 3: Initialize network components
                Console.WriteLine("3. Initializing network components...");
                await Task.Delay(100); // Simulate initialization

                // Step 4: Start services
                Console.WriteLine("4. Starting services...");
                await Task.Delay(100); // Simulate startup

                Console.WriteLine("✓ Application started successfully with validated configuration");
            }
            catch (InvalidOperationException ex)
            {
                Console.WriteLine($"✗ Application startup failed due to configuration issues:");
                Console.WriteLine($"   {ex.Message}");
                Console.WriteLine("   Please fix configuration issues and restart the application.");

                // In a real application, you would:
                // - Log the error
                // - Exit gracefully
                // - Possibly send alerts to monitoring systems
            }
        }

        /// <summary>
        /// Demonstrates configuration validation in different environments.
        /// </summary>
        public static async Task EnvironmentSpecificValidationExampleAsync()
        {
            Console.WriteLine("\n=== Environment-Specific Validation Example ===");

            var environments = new[] { "Development", "Staging", "Production" };

            foreach (var env in environments)
            {
                Console.WriteLine($"\nValidating configuration for {env} environment:");

                // Set environment variable for demonstration
                Environment.SetEnvironmentVariable("ASPNETCORE_ENVIRONMENT", env);

                // Use the singleton configuration instance
                var config = UsdpConfiguration.Instance;

                // Adjust configuration based on environment
                switch (env)
                {
                    case "Development":
                        config.DefaultSecurity = "none"; // OK for development
                        config.LogMode = UsdpConfiguration.LoggingMode.Console;
                        break;
                    case "Staging":
                        config.DefaultSecurity = "psk-tls1.3"; // Secure for staging
                        config.LogMode = UsdpConfiguration.LoggingMode.Both;
                        break;
                    case "Production":
                        config.DefaultSecurity = "cert-tls1.3"; // Most secure for production
                        config.UseHttps = true;
                        config.LogMode = UsdpConfiguration.LoggingMode.EventLog;
                        config.EnableCircuitBreakers = true;
                        break;
                }

                try
                {
                    var result = await StartupConfigurationValidator.ValidateAtStartupAsync(
                        config,
                        StartupConfigurationValidator.ValidationBehavior.Lenient);

                    Console.WriteLine($"  ✓ {env} configuration validation passed");
                }
                catch (InvalidOperationException ex)
                {
                    Console.WriteLine($"  ✗ {env} configuration validation failed: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// Main example runner that demonstrates all configuration validation features.
        /// </summary>
        public static async Task RunAllExamplesAsync()
        {
            Console.WriteLine("USDP2 Configuration Validation Examples");
            Console.WriteLine("======================================");

            try
            {
                await BasicStartupValidationExampleAsync();
                await ValidationBehaviorExampleAsync();
                await CustomValidationExampleAsync();
                ValidationReportingExample();
                await ApplicationStartupIntegrationExampleAsync();
                await EnvironmentSpecificValidationExampleAsync();

                Console.WriteLine("\n✓ All configuration validation examples completed successfully!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"\n✗ Example execution failed: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }
    }
}
