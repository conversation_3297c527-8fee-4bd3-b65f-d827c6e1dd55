using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;

namespace USDP2
{
    /// <summary>
    /// Benchmarking utility for comparing serialization performance between different implementations.
    /// 
    /// This class provides:
    /// - Performance comparison between optimized and default serializers
    /// - Memory allocation tracking
    /// - Throughput measurements
    /// - Cache effectiveness analysis
    /// </summary>
    public static class SerializationBenchmark
    {
        /// <summary>
        /// Results from a serialization benchmark run.
        /// </summary>
        public class BenchmarkResult
        {
            /// <summary>
            /// Name of the benchmark test.
            /// </summary>
            public string TestName { get; set; } = string.Empty;

            /// <summary>
            /// Number of operations performed.
            /// </summary>
            public int OperationCount { get; set; }

            /// <summary>
            /// Total elapsed time for all operations.
            /// </summary>
            public TimeSpan TotalElapsed { get; set; }

            /// <summary>
            /// Average time per operation.
            /// </summary>
            public TimeSpan AveragePerOperation => OperationCount > 0 ? TimeSpan.FromTicks(TotalElapsed.Ticks / OperationCount) : TimeSpan.Zero;

            /// <summary>
            /// Operations per second.
            /// </summary>
            public double OperationsPerSecond => TotalElapsed.TotalSeconds > 0 ? OperationCount / TotalElapsed.TotalSeconds : 0;

            /// <summary>
            /// Memory allocated during the benchmark (in bytes).
            /// </summary>
            public long MemoryAllocated { get; set; }

            /// <summary>
            /// Memory allocated per operation (in bytes).
            /// </summary>
            public long MemoryPerOperation => OperationCount > 0 ? MemoryAllocated / OperationCount : 0;

            /// <summary>
            /// Cache statistics if applicable.
            /// </summary>
            public CacheStatistics? CacheStats { get; set; }
        }

        /// <summary>
        /// Runs a comprehensive benchmark comparing optimized vs default serialization.
        /// </summary>
        /// <param name="iterations">Number of iterations to run for each test.</param>
        /// <returns>Benchmark results for analysis.</returns>
        public static async Task<List<BenchmarkResult>> RunComprehensiveBenchmarkAsync(int iterations = 10000)
        {
            var results = new List<BenchmarkResult>();

            // Create test data
            var testAdvertisements = CreateTestServiceAdvertisements(100);
            var optimizedSerializer = new OptimizedUSDPSerializer();
            var defaultSerializer = new DefaultUSDPSerializer();

            UsdpLogger.Log("SerializationBenchmark.Start", new
            {
                Iterations = iterations,
                TestDataCount = testAdvertisements.Count,
                Message = "Starting comprehensive serialization benchmark"
            });

            // Benchmark 1: JSON Serialization - Optimized
            results.Add(await BenchmarkJsonSerialization("Optimized JSON Serialization", optimizedSerializer, testAdvertisements, iterations));

            // Benchmark 2: JSON Serialization - Default
            results.Add(await BenchmarkJsonSerialization("Default JSON Serialization", defaultSerializer, testAdvertisements, iterations));

            // Benchmark 3: CBOR Serialization - Optimized
            results.Add(await BenchmarkCborSerialization("Optimized CBOR Serialization", optimizedSerializer, testAdvertisements, iterations));

            // Benchmark 4: CBOR Serialization - Default
            results.Add(await BenchmarkCborSerialization("Default CBOR Serialization", defaultSerializer, testAdvertisements, iterations));

            // Benchmark 5: Cache effectiveness test
            results.Add(await BenchmarkCacheEffectiveness("Cache Effectiveness Test", optimizedSerializer, testAdvertisements, iterations));

            UsdpLogger.Log("SerializationBenchmark.Complete", new
            {
                TotalTests = results.Count,
                Message = "Serialization benchmark completed",
                Results = results.Select(r => new
                {
                    r.TestName,
                    r.OperationsPerSecond,
                    r.MemoryPerOperation,
                    CacheHitRate = r.CacheStats?.JsonHitRate ?? 0
                })
            });

            return results;
        }

        /// <summary>
        /// Benchmarks JSON serialization performance.
        /// </summary>
        private static async Task<BenchmarkResult> BenchmarkJsonSerialization(string testName, IUSDPSerializer serializer, List<ServiceAdvertisement> testData, int iterations)
        {
            // Warm up
            for (int i = 0; i < Math.Min(100, iterations / 10); i++)
            {
                var warmupAd = testData[i % testData.Count];
                serializer.SerializeToJson(warmupAd);
            }

            // Force GC before measurement
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();

            var initialMemory = GC.GetTotalMemory(false);
            var stopwatch = Stopwatch.StartNew();

            // Run benchmark
            for (int i = 0; i < iterations; i++)
            {
                var ad = testData[i % testData.Count];
                var result = serializer.SerializeToJson(ad);

                // Simulate some processing to prevent optimization
                if (!result.IsSuccess)
                {
                    throw new InvalidOperationException($"Serialization failed: {result.ErrorMessage}");
                }
            }

            stopwatch.Stop();
            var finalMemory = GC.GetTotalMemory(false);

            return new BenchmarkResult
            {
                TestName = testName,
                OperationCount = iterations,
                TotalElapsed = stopwatch.Elapsed,
                MemoryAllocated = Math.Max(0, finalMemory - initialMemory),
                CacheStats = serializer is OptimizedUSDPSerializer ? SerializationCacheManager.GetStatistics() : null
            };
        }

        /// <summary>
        /// Benchmarks CBOR serialization performance.
        /// </summary>
        private static async Task<BenchmarkResult> BenchmarkCborSerialization(string testName, IUSDPSerializer serializer, List<ServiceAdvertisement> testData, int iterations)
        {
            // Warm up
            for (int i = 0; i < Math.Min(100, iterations / 10); i++)
            {
                var warmupAd = testData[i % testData.Count];
                serializer.SerializeToCbor(warmupAd);
            }

            // Force GC before measurement
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();

            var initialMemory = GC.GetTotalMemory(false);
            var stopwatch = Stopwatch.StartNew();

            // Run benchmark
            for (int i = 0; i < iterations; i++)
            {
                var ad = testData[i % testData.Count];
                var result = serializer.SerializeToCbor(ad);

                // Simulate some processing to prevent optimization
                if (!result.IsSuccess)
                {
                    throw new InvalidOperationException($"Serialization failed: {result.ErrorMessage}");
                }
            }

            stopwatch.Stop();
            var finalMemory = GC.GetTotalMemory(false);

            return new BenchmarkResult
            {
                TestName = testName,
                OperationCount = iterations,
                TotalElapsed = stopwatch.Elapsed,
                MemoryAllocated = Math.Max(0, finalMemory - initialMemory),
                CacheStats = serializer is OptimizedUSDPSerializer ? SerializationCacheManager.GetStatistics() : null
            };
        }

        /// <summary>
        /// Benchmarks cache effectiveness by serializing the same objects multiple times.
        /// </summary>
        private static async Task<BenchmarkResult> BenchmarkCacheEffectiveness(string testName, OptimizedUSDPSerializer serializer, List<ServiceAdvertisement> testData, int iterations)
        {
            // Clear cache before test
            SerializationCacheManager.ClearCache();

            // Use a smaller subset for cache testing
            var cacheTestData = testData.Take(10).ToList();

            // Force GC before measurement
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();

            var initialMemory = GC.GetTotalMemory(false);
            var stopwatch = Stopwatch.StartNew();

            // Run benchmark - repeatedly serialize the same objects to test cache effectiveness
            for (int i = 0; i < iterations; i++)
            {
                var ad = cacheTestData[i % cacheTestData.Count];
                var result = serializer.SerializeToJson(ad);

                if (!result.IsSuccess)
                {
                    throw new InvalidOperationException($"Serialization failed: {result.ErrorMessage}");
                }
            }

            stopwatch.Stop();
            var finalMemory = GC.GetTotalMemory(false);

            return new BenchmarkResult
            {
                TestName = testName,
                OperationCount = iterations,
                TotalElapsed = stopwatch.Elapsed,
                MemoryAllocated = Math.Max(0, finalMemory - initialMemory),
                CacheStats = SerializationCacheManager.GetStatistics()
            };
        }

        /// <summary>
        /// Creates test ServiceAdvertisement objects for benchmarking.
        /// </summary>
        private static List<ServiceAdvertisement> CreateTestServiceAdvertisements(int count)
        {
            var advertisements = new List<ServiceAdvertisement>();
            var random = new Random(42); // Fixed seed for reproducible results

            for (int i = 0; i < count; i++)
            {
                var serviceId = new ServiceIdentifier
                {
                    Namespace = i % 2 == 0 ? "local" : "global",
                    Name = $"TestService{i}"
                };

                var endpoint = new TransportEndpoint
                {
                    Address = $"192.168.1.{100 + (i % 155)}",
                    Port = 8000 + (i % 1000),
                    Protocol = i % 3 == 0 ? "tcp" : i % 3 == 1 ? "udp" : "http"
                };

                var advertisement = new ServiceAdvertisement(serviceId, endpoint)
                {
                    Metadata = new Dictionary<string, object>
                    {
                        ["description"] = $"Test service number {i}",
                        ["priority"] = random.Next(1, 10),
                        ["tags"] = new[] { "test", $"category{i % 5}", $"group{i % 3}" },
                        ["enabled"] = i % 4 != 0,
                        ["lastUpdated"] = DateTimeOffset.UtcNow.AddMinutes(-random.Next(0, 1440))
                    },
                    Timestamp = DateTimeOffset.UtcNow.AddSeconds(-random.Next(0, 3600)),
                    Ttl = TimeSpan.FromMinutes(30 + random.Next(0, 120))
                };

                advertisements.Add(advertisement);
            }

            return advertisements;
        }

        /// <summary>
        /// Prints benchmark results in a formatted table.
        /// </summary>
        /// <param name="results">The benchmark results to display.</param>
        public static void PrintResults(List<BenchmarkResult> results)
        {
            Console.WriteLine("\n=== Serialization Benchmark Results ===");
            Console.WriteLine();
            Console.WriteLine($"{"Test Name",-35} {"Ops/Sec",-12} {"Avg Time",-12} {"Memory/Op",-12} {"Cache Hit%",-12}");
            Console.WriteLine(new string('-', 85));

            foreach (var result in results)
            {
                var avgTimeMs = result.AveragePerOperation.TotalMilliseconds;
                var memoryKB = result.MemoryPerOperation / 1024.0;
                var cacheHitRate = result.CacheStats?.JsonHitRate ?? 0;

                Console.WriteLine($"{result.TestName,-35} {result.OperationsPerSecond:N0,-12} {avgTimeMs:F3}ms{"",-6} {memoryKB:F1}KB{"",-7} {cacheHitRate:F1}%{"",-8}");
            }

            Console.WriteLine();
        }
    }
}
