using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Numerics;
using System.Threading;
using System.Threading.Tasks;

namespace USDP2.HealthCheck
{
    /// <summary>
    /// Health check for ChordNode that verifies finger table consistency, successor reachability,
    /// and overall node health in the Chord distributed hash table.
    /// </summary>
    public class ChordNodeHealthCheck : HealthCheckBase
    {
        private readonly ChordNode _chordNode;
        private readonly HealthCheckOptions _options;

        /// <summary>
        /// Initializes a new instance of the <see cref="ChordNodeHealthCheck"/> class.
        /// </summary>
        /// <param name="chordNode">The ChordNode to monitor.</param>
        /// <param name="options">The health check options.</param>
        public ChordNodeHealthCheck(ChordNode chordNode, HealthCheckOptions? options = null)
            : base("ChordNode", "Monitors ChordNode finger table consistency and successor reachability", options?.Timeout)
        {
            _chordNode = chordNode ?? throw new ArgumentNullException(nameof(chordNode));
            _options = options ?? new HealthCheckOptions();
        }

        /// <summary>
        /// Performs the ChordNode health check.
        /// </summary>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>A <see cref="Task{HealthCheckResult}"/> representing the health check result.</returns>
        protected override async Task<HealthCheckResult> PerformHealthCheckAsync(CancellationToken cancellationToken)
        {
            var stopwatch = Stopwatch.StartNew();
            var healthData = new Dictionary<string, object>();
            var issues = new List<string>();

            try
            {
                // Check 1: Verify node is initialized and has valid ID
                await CheckNodeInitializationAsync(healthData, issues, cancellationToken).ConfigureAwait(false);

                // Check 2: Test basic storage and retrieval functionality
                await CheckStorageOperationsAsync(healthData, issues, cancellationToken).ConfigureAwait(false);

                // Check 3: Verify lookup operations are working
                await CheckLookupOperationsAsync(healthData, issues, cancellationToken).ConfigureAwait(false);

                // Check 4: Monitor data consistency
                await CheckDataConsistencyAsync(healthData, issues, cancellationToken).ConfigureAwait(false);

                // Check 5: Performance metrics
                await CheckPerformanceMetricsAsync(healthData, issues, cancellationToken).ConfigureAwait(false);

                stopwatch.Stop();

                // Determine overall health status
                var status = DetermineHealthStatus(issues, healthData);
                var description = CreateHealthDescription(status, issues);

                var result = new HealthCheckResult(status, description, stopwatch.Elapsed, null, healthData);

                if (_options.LogResults)
                {
                    LogResult(result);
                }

                return result;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                healthData["exception"] = ex.Message;
                healthData["stackTrace"] = ex.StackTrace ?? string.Empty;

                var result = HealthCheckResult.Unhealthy($"ChordNode health check failed: {ex.Message}", stopwatch.Elapsed, ex, healthData);

                if (_options.LogResults)
                {
                    LogResult(result);
                }

                return result;
            }
        }

        /// <summary>
        /// Checks if the ChordNode is properly initialized.
        /// </summary>
        private async Task CheckNodeInitializationAsync(Dictionary<string, object> healthData, List<string> issues, CancellationToken cancellationToken)
        {
            try
            {
                // Since ChordNode properties are private, we'll test through public interface
                // Test that the node can handle basic operations
                var testKey = new BigInteger(12345);
                var testValue = "health_check_test_value";

                // Try to store a test value
                await _chordNode.StoreAsync(testKey, testValue).ConfigureAwait(false);
                
                // Try to retrieve it
                var retrievedValue = await _chordNode.LookupAsync(testKey).ConfigureAwait(false);

                healthData["nodeInitialized"] = true;
                healthData["testStoreSuccessful"] = retrievedValue == testValue;

                if (retrievedValue != testValue)
                {
                    issues.Add("Node initialization test failed - stored and retrieved values don't match");
                }
            }
            catch (Exception ex)
            {
                healthData["nodeInitialized"] = false;
                healthData["initializationError"] = ex.Message;
                issues.Add($"Node initialization check failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Checks storage operations performance and reliability.
        /// </summary>
        private async Task CheckStorageOperationsAsync(Dictionary<string, object> healthData, List<string> issues, CancellationToken cancellationToken)
        {
            var stopwatch = Stopwatch.StartNew();
            var successfulStores = 0;
            var failedStores = 0;
            const int testOperations = 10;

            try
            {
                for (int i = 0; i < testOperations; i++)
                {
                    try
                    {
                        var key = new BigInteger(DateTime.UtcNow.Ticks + i);
                        var value = $"health_check_storage_test_{i}_{DateTime.UtcNow:yyyy-MM-dd_HH-mm-ss}";
                        
                        await _chordNode.StoreAsync(key, value).ConfigureAwait(false);
                        successfulStores++;
                    }
                    catch
                    {
                        failedStores++;
                    }

                    if (cancellationToken.IsCancellationRequested)
                        break;
                }

                stopwatch.Stop();

                healthData["storageOperationsTotal"] = testOperations;
                healthData["storageOperationsSuccessful"] = successfulStores;
                healthData["storageOperationsFailed"] = failedStores;
                healthData["storageOperationsDuration"] = stopwatch.ElapsedMilliseconds;
                healthData["storageSuccessRate"] = testOperations > 0 ? (double)successfulStores / testOperations * 100 : 0;

                if (failedStores > testOperations * 0.1) // More than 10% failure rate
                {
                    issues.Add($"High storage operation failure rate: {failedStores}/{testOperations} failed");
                }

                if (stopwatch.ElapsedMilliseconds > 5000) // More than 5 seconds for 10 operations
                {
                    issues.Add($"Storage operations are slow: {stopwatch.ElapsedMilliseconds}ms for {testOperations} operations");
                }
            }
            catch (Exception ex)
            {
                healthData["storageOperationsError"] = ex.Message;
                issues.Add($"Storage operations check failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Checks lookup operations performance and reliability.
        /// </summary>
        private async Task CheckLookupOperationsAsync(Dictionary<string, object> healthData, List<string> issues, CancellationToken cancellationToken)
        {
            var stopwatch = Stopwatch.StartNew();
            var successfulLookups = 0;
            var failedLookups = 0;
            const int testOperations = 10;

            try
            {
                // First, store some test data
                var testKeys = new List<BigInteger>();
                for (int i = 0; i < testOperations; i++)
                {
                    var key = new BigInteger(DateTime.UtcNow.Ticks + i + 1000);
                    var value = $"lookup_test_{i}";
                    testKeys.Add(key);
                    await _chordNode.StoreAsync(key, value).ConfigureAwait(false);
                }

                // Now test lookups
                stopwatch.Restart();
                foreach (var key in testKeys)
                {
                    try
                    {
                        var result = await _chordNode.LookupAsync(key).ConfigureAwait(false);
                        if (result != null)
                            successfulLookups++;
                        else
                            failedLookups++;
                    }
                    catch
                    {
                        failedLookups++;
                    }

                    if (cancellationToken.IsCancellationRequested)
                        break;
                }

                stopwatch.Stop();

                healthData["lookupOperationsTotal"] = testOperations;
                healthData["lookupOperationsSuccessful"] = successfulLookups;
                healthData["lookupOperationsFailed"] = failedLookups;
                healthData["lookupOperationsDuration"] = stopwatch.ElapsedMilliseconds;
                healthData["lookupSuccessRate"] = testOperations > 0 ? (double)successfulLookups / testOperations * 100 : 0;

                if (failedLookups > testOperations * 0.1) // More than 10% failure rate
                {
                    issues.Add($"High lookup operation failure rate: {failedLookups}/{testOperations} failed");
                }

                if (stopwatch.ElapsedMilliseconds > 3000) // More than 3 seconds for 10 lookups
                {
                    issues.Add($"Lookup operations are slow: {stopwatch.ElapsedMilliseconds}ms for {testOperations} operations");
                }
            }
            catch (Exception ex)
            {
                healthData["lookupOperationsError"] = ex.Message;
                issues.Add($"Lookup operations check failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Checks data consistency and integrity.
        /// </summary>
        private async Task CheckDataConsistencyAsync(Dictionary<string, object> healthData, List<string> issues, CancellationToken cancellationToken)
        {
            try
            {
                var consistencyTestKey = new BigInteger(DateTime.UtcNow.Ticks + 9999);
                var originalValue = $"consistency_test_{DateTime.UtcNow:yyyy-MM-dd_HH-mm-ss-fff}";

                // Store a value
                await _chordNode.StoreAsync(consistencyTestKey, originalValue).ConfigureAwait(false);

                // Immediately retrieve it
                var retrievedValue = await _chordNode.LookupAsync(consistencyTestKey).ConfigureAwait(false);

                healthData["dataConsistencyTest"] = retrievedValue == originalValue;

                if (retrievedValue != originalValue)
                {
                    issues.Add("Data consistency test failed - stored and retrieved values don't match");
                    healthData["expectedValue"] = originalValue;
                    healthData["actualValue"] = retrievedValue ?? "null";
                }
            }
            catch (Exception ex)
            {
                healthData["dataConsistencyError"] = ex.Message;
                issues.Add($"Data consistency check failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Checks performance metrics and resource usage.
        /// </summary>
        private async Task CheckPerformanceMetricsAsync(Dictionary<string, object> healthData, List<string> issues, CancellationToken cancellationToken)
        {
            try
            {
                var process = Process.GetCurrentProcess();
                
                healthData["memoryUsageMB"] = process.WorkingSet64 / (1024 * 1024);
                healthData["cpuTimeMs"] = process.TotalProcessorTime.TotalMilliseconds;
                healthData["threadCount"] = process.Threads.Count;

                // Check if memory usage is excessive (more than 500MB for this component)
                var memoryUsageMB = process.WorkingSet64 / (1024 * 1024);
                if (memoryUsageMB > 500)
                {
                    issues.Add($"High memory usage detected: {memoryUsageMB}MB");
                }

                // Check thread count (more than 100 threads might indicate issues)
                if (process.Threads.Count > 100)
                {
                    issues.Add($"High thread count detected: {process.Threads.Count} threads");
                }

                await Task.Delay(1, cancellationToken).ConfigureAwait(false); // Minimal async operation
            }
            catch (Exception ex)
            {
                healthData["performanceMetricsError"] = ex.Message;
                issues.Add($"Performance metrics check failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Determines the overall health status based on issues found.
        /// </summary>
        private static HealthStatus DetermineHealthStatus(List<string> issues, Dictionary<string, object> healthData)
        {
            if (issues.Count == 0)
                return HealthStatus.Healthy;

            // Check for critical issues
            var criticalIssues = issues.FindAll(issue => 
                issue.Contains("initialization") || 
                issue.Contains("consistency") ||
                issue.Contains("High") && issue.Contains("failure rate"));

            if (criticalIssues.Count > 0)
                return HealthStatus.Unhealthy;

            // Check for performance issues
            var performanceIssues = issues.FindAll(issue => 
                issue.Contains("slow") || 
                issue.Contains("High memory") ||
                issue.Contains("High thread"));

            if (performanceIssues.Count > 0)
                return HealthStatus.Degraded;

            return issues.Count > 2 ? HealthStatus.Degraded : HealthStatus.Healthy;
        }

        /// <summary>
        /// Creates a health description based on the status and issues.
        /// </summary>
        private static string CreateHealthDescription(HealthStatus status, List<string> issues)
        {
            return status switch
            {
                HealthStatus.Healthy => "ChordNode is healthy and functioning normally",
                HealthStatus.Degraded => $"ChordNode is degraded with {issues.Count} issue(s): {string.Join("; ", issues)}",
                HealthStatus.Unhealthy => $"ChordNode is unhealthy with {issues.Count} issue(s): {string.Join("; ", issues)}",
                _ => $"ChordNode health status is unknown with {issues.Count} issue(s): {string.Join("; ", issues)}"
            };
        }
    }
}
