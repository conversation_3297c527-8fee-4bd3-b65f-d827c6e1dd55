using System;
using USDP2;

namespace USDP2
{
    /// <summary>
    /// The networks scopes.
    /// </summary>
    public enum NetworkScope
    {
        /// <summary>
        /// For local networks.
        /// </summary>
        Local,
        /// <summary>
        /// For global  networks.
        /// </summary>
        Global
    }

    /// <summary>
    /// The network sender factory.
    /// </summary>
    public static class NetworkSenderFactory
    {
        /// <summary>
        /// Creates the sender.
        /// </summary>
        /// <param name="scope">The scope.</param>
        /// <param name="config">The USDP configuration instance. If null, uses UsdpConfiguration.Instance.</param>
        /// <exception cref="ArgumentOutOfRangeException"></exception>
        /// <returns>An <see cref="INetworkSender"/></returns>
        public static INetworkSender CreateSender(NetworkScope scope, UsdpConfiguration? config = null)
        {
            return scope switch
            {
                NetworkScope.Local => new UdpNetworkSender(config),
                NetworkScope.Global => new HttpNetworkSender(config), // or new TcpNetworkSender()
                _ => throw new ArgumentOutOfRangeException(nameof(scope), scope, null)
            };
        }
    }
}