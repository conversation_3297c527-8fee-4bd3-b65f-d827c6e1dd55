using System;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;

namespace USDP2
{
    /// <summary>
    /// Tests the configuration validation system with intentionally invalid configurations
    /// to demonstrate error detection and reporting capabilities.
    /// </summary>
    public static class InvalidConfigurationTest
    {
        /// <summary>
        /// Creates a test configuration instance using reflection.
        /// </summary>
        private static UsdpConfiguration CreateTestConfiguration()
        {
            var constructor = typeof(UsdpConfiguration).GetConstructor(
                BindingFlags.NonPublic | BindingFlags.Instance,
                null,
                Type.EmptyTypes,
                null);

            if (constructor == null)
                throw new InvalidOperationException("Cannot create test configuration instance");

            return (UsdpConfiguration)constructor.Invoke(null);
        }

        /// <summary>
        /// Runs comprehensive tests with various invalid configurations.
        /// </summary>
        public static async Task RunInvalidConfigurationTestsAsync()
        {
            Console.WriteLine("=".PadRight(70, '='));
            Console.WriteLine("USDP2 Configuration Validation - Invalid Configuration Tests");
            Console.WriteLine("=".PadRight(70, '='));
            Console.WriteLine();

            await TestCriticalErrorsAsync();
            await TestConfigurationErrorsAsync();
            await TestWarningScenariosAsync();
            await TestCrossPropertyConflictsAsync();
            await TestStartupValidationFailuresAsync();

            Console.WriteLine();
            Console.WriteLine("✅ All invalid configuration tests completed!");
        }

        /// <summary>
        /// Tests configurations that should produce critical errors.
        /// </summary>
        private static async Task TestCriticalErrorsAsync()
        {
            Console.WriteLine("🚨 Test 1: Critical Errors (Application Cannot Start)");
            Console.WriteLine("-".PadRight(60, '-'));

            var testCases = new (string Name, Action<UsdpConfiguration> Action)[]
            {
                ("Invalid Multicast Address", config =>
                    config.DefaultMulticastAddress = "invalid-ip-address"),

                ("Port Out of Range", config =>
                    config.DefaultHttpPort = 70000),

                ("Negative Port", config =>
                    config.DefaultServicePort = -1),

                ("Negative Timeout", config =>
                    config.NetworkTimeout = TimeSpan.FromSeconds(-10)),

                ("Empty Multicast Address", config =>
                    config.DefaultMulticastAddress = "")
            };

            foreach (var testCase in testCases)
            {
                var config = CreateTestConfiguration();
                testCase.Action(config);

                var results = ValidateConfiguration.ValidateAll(config);
                var criticalErrors = results.Where(r => r.Severity == ValidationSeverity.Critical).ToList();

                Console.WriteLine($"   {testCase.Name}:");
                Console.WriteLine($"      Critical Errors Found: {criticalErrors.Count}");

                if (criticalErrors.Count > 0)
                {
                    var error = criticalErrors.First();
                    Console.WriteLine($"      ❌ {error.Message}");
                    Console.WriteLine($"      💡 {error.RecommendedAction}");
                }
                Console.WriteLine();
            }
        }

        /// <summary>
        /// Tests configurations that should produce errors.
        /// </summary>
        private static async Task TestConfigurationErrorsAsync()
        {
            Console.WriteLine("❌ Test 2: Configuration Errors (Runtime Failure Risk)");
            Console.WriteLine("-".PadRight(60, '-'));

            var testCases = new (string Name, Action<UsdpConfiguration> Action)[]
            {
                ("Port Conflicts", config => {
                    config.DefaultHttpPort = 8080;
                    config.DefaultHttpsPort = 8080;
                }),

                ("Authentication Without Security", config => {
                    config.RequireAuthentication = true;
                    config.DefaultSecurity = "none";
                }),

                ("Buffer Size Conflict", config => {
                    config.DefaultBufferSize = 64 * 1024;
                    config.MaxBufferSize = 32 * 1024;
                }),

                ("TTL Relationship Violation", config => {
                    config.DefaultTtl = TimeSpan.FromDays(10);
                    config.MaxTtl = TimeSpan.FromDays(5);
                }),

                ("Non-Multicast Address", config =>
                    config.DefaultMulticastAddress = "***********")
            };

            foreach (var testCase in testCases)
            {
                var config = CreateTestConfiguration();
                testCase.Action(config);

                var results = ValidateConfiguration.ValidateAll(config);
                var errors = results.Where(r => r.Severity == ValidationSeverity.Error).ToList();

                Console.WriteLine($"   {testCase.Name}:");
                Console.WriteLine($"      Errors Found: {errors.Count}");

                if (errors.Count > 0)
                {
                    var error = errors.First();
                    Console.WriteLine($"      ❌ {error.Message}");
                    Console.WriteLine($"      💡 {error.RecommendedAction}");
                }
                Console.WriteLine();
            }
        }

        /// <summary>
        /// Tests configurations that should produce warnings.
        /// </summary>
        private static async Task TestWarningScenariosAsync()
        {
            Console.WriteLine("⚠️  Test 3: Warning Scenarios (Suboptimal Configuration)");
            Console.WriteLine("-".PadRight(60, '-'));

            var testCases = new (string Name, Action<UsdpConfiguration> Action)[]
            {
                ("Well-Known Port Usage", config =>
                    config.DefaultHttpPort = 80),

                ("Security Disabled", config =>
                    config.DefaultSecurity = "none"),

                ("HTTPS with No Security", config => {
                    config.UseHttps = true;
                    config.DefaultSecurity = "none";
                }),

                ("Query Timeout > Network Timeout", config => {
                    config.NetworkTimeout = TimeSpan.FromSeconds(5);
                    config.QueryResponseTimeout = TimeSpan.FromSeconds(10);
                }),

                ("Reserved Multicast Range", config =>
                    config.DefaultMulticastAddress = "***********")
            };

            foreach (var testCase in testCases)
            {
                var config = CreateTestConfiguration();
                testCase.Action(config);

                var results = ValidateConfiguration.ValidateAll(config);
                var warnings = results.Where(r => r.Severity == ValidationSeverity.Warning).ToList();

                Console.WriteLine($"   {testCase.Name}:");
                Console.WriteLine($"      Warnings Found: {warnings.Count}");

                if (warnings.Count > 0)
                {
                    var warning = warnings.First();
                    Console.WriteLine($"      ⚠️  {warning.Message}");
                    Console.WriteLine($"      💡 {warning.RecommendedAction}");
                }
                Console.WriteLine();
            }
        }

        /// <summary>
        /// Tests cross-property conflicts and dependencies.
        /// </summary>
        private static async Task TestCrossPropertyConflictsAsync()
        {
            Console.WriteLine("🔗 Test 4: Cross-Property Conflicts");
            Console.WriteLine("-".PadRight(60, '-'));

            // Test multiple conflicts in one configuration
            var config = CreateTestConfiguration();

            // Create multiple issues
            config.DefaultHttpPort = 8080;
            config.DefaultHttpsPort = 8080;  // Port conflict
            config.RequireAuthentication = true;
            config.DefaultSecurity = "none";  // Auth without security
            config.UseHttps = true;  // HTTPS with no security
            config.DefaultBufferSize = 64 * 1024;
            config.MaxBufferSize = 32 * 1024;  // Buffer size conflict

            var results = ValidateConfiguration.ValidateAll(config);

            Console.WriteLine($"   Multiple Conflicts Configuration:");
            Console.WriteLine($"      Total Issues: {results.Count}");
            Console.WriteLine($"      Critical: {results.Count(r => r.Severity == ValidationSeverity.Critical)}");
            Console.WriteLine($"      Errors: {results.Count(r => r.Severity == ValidationSeverity.Error)}");
            Console.WriteLine($"      Warnings: {results.Count(r => r.Severity == ValidationSeverity.Warning)}");
            Console.WriteLine();

            Console.WriteLine("   Top Issues Found:");
            var topIssues = results
                .Where(r => r.Severity == ValidationSeverity.Error || r.Severity == ValidationSeverity.Critical)
                .Take(3)
                .ToList();

            foreach (var issue in topIssues)
            {
                Console.WriteLine($"      [{issue.Severity}] {issue.PropertyName}: {issue.Message}");
            }
            Console.WriteLine();
        }

        /// <summary>
        /// Tests startup validation failures with different behaviors.
        /// </summary>
        private static async Task TestStartupValidationFailuresAsync()
        {
            Console.WriteLine("🚀 Test 5: Startup Validation Failures");
            Console.WriteLine("-".PadRight(60, '-'));

            // Create a configuration with critical errors
            var badConfig = CreateTestConfiguration();
            badConfig.DefaultMulticastAddress = "invalid-address";
            badConfig.DefaultHttpPort = -1;
            badConfig.RequireAuthentication = true;
            badConfig.DefaultSecurity = "none";

            var behaviors = new[]
            {
                StartupConfigurationValidator.ValidationBehavior.Strict,
                StartupConfigurationValidator.ValidationBehavior.Lenient,
                StartupConfigurationValidator.ValidationBehavior.LogOnly
            };

            foreach (var behavior in behaviors)
            {
                Console.WriteLine($"   Testing {behavior} behavior with invalid config:");

                try
                {
                    var result = await StartupConfigurationValidator.ValidateAtStartupAsync(
                        badConfig, behavior);

                    Console.WriteLine($"      ✅ Validation completed: {result}");
                }
                catch (InvalidOperationException ex)
                {
                    Console.WriteLine($"      ❌ Validation failed (as expected)");
                    Console.WriteLine($"      📝 Error: {ex.Message.Split('\n')[0]}");

                    // Count the number of issues mentioned
                    var errorLines = ex.Message.Split('\n');
                    var issueCount = errorLines.Where(line => line.Contains("CRITICAL:") || line.Contains("Error:")).Count();
                    Console.WriteLine($"      📊 Issues reported: {issueCount}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"      ⚠️  Unexpected error: {ex.Message}");
                }

                Console.WriteLine();
            }
        }

        /// <summary>
        /// Entry point for running invalid configuration tests.
        /// </summary>
        public static async Task Main(string[] args)
        {
            if (args.Length > 0 && args[0] == "--test-invalid")
            {
                await RunInvalidConfigurationTestsAsync();
                return;
            }

            Console.WriteLine("Use --test-invalid to run invalid configuration tests");
        }
    }
}
