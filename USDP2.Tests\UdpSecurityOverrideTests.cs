using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Runtime.InteropServices;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace USDP2.Tests
{
    [TestClass]
    public class UdpSecurityOverrideTests
    {
        private const string TestKeyName = "TestUdpAuthKey";
        private readonly byte[] _testData = Encoding.UTF8.GetBytes("Test UDP message data");

        /// <summary>
        /// Checks if DPAPI is available on the current platform.
        /// </summary>
        private static bool IsDpapiAvailable => RuntimeInformation.IsOSPlatform(OSPlatform.Windows);



        [TestInitialize]
        public void TestInitialize()
        {
            // Clean up any existing test keys
            try
            {
                var testKeyPath = TestKeyName;
                if (System.IO.File.Exists(testKeyPath))
                {
                    System.IO.File.Delete(testKeyPath);
                }
            }
            catch
            {
                // Ignore cleanup errors
            }
        }

        [TestCleanup]
        public void TestCleanup()
        {
            // Clean up test keys
            try
            {
                var testKeyPath = TestKeyName;
                if (System.IO.File.Exists(testKeyPath))
                {
                    System.IO.File.Delete(testKeyPath);
                }
            }
            catch
            {
                // Ignore cleanup errors
            }
        }

        [TestMethod]
        public async Task UdpSecurityOverride_CreateAsync_ValidParameters_Success()
        {
            // Skip test if DPAPI is not available
            if (!IsDpapiAvailable)
            {
                Assert.Inconclusive("This test requires Windows DPAPI which is not available on the current platform.");
                return;
            }

            // Arrange & Act
            var udpSecurity = await UdpSecurityOverride.CreateAsync(
                keyNameOrPath: TestKeyName,
                replayWindowMinutes: 5,
                enableTimestampValidation: true);

            // Assert
            Assert.IsNotNull(udpSecurity);
        }

        [TestMethod]
        public async Task UdpSecurityOverride_CreateAsync_InvalidReplayWindow_ThrowsException()
        {
            // Skip test if DPAPI is not available
            if (!IsDpapiAvailable)
            {
                Assert.Inconclusive("This test requires Windows DPAPI which is not available on the current platform.");
                return;
            }

            // Arrange & Act & Assert
            await Assert.ThrowsExceptionAsync<ArgumentException>(async () =>
            {
                await UdpSecurityOverride.CreateAsync(
                    keyNameOrPath: TestKeyName,
                    replayWindowMinutes: 0); // Invalid replay window
            });
        }

        [TestMethod]
        public async Task UdpSecurityOverride_CreateAsync_EmptyKeyName_ThrowsException()
        {
            // Skip test if DPAPI is not available
            if (!IsDpapiAvailable)
            {
                Assert.Inconclusive("This test requires Windows DPAPI which is not available on the current platform.");
                return;
            }

            // Arrange & Act & Assert
            await Assert.ThrowsExceptionAsync<ArgumentException>(async () =>
            {
                await UdpSecurityOverride.CreateAsync(keyNameOrPath: ""); // Empty key name
            });
        }

        [TestMethod]
        public async Task UdpSecurityOverride_SecureData_ValidData_Success()
        {
            // Skip test if DPAPI is not available
            if (!IsDpapiAvailable)
            {
                Assert.Inconclusive("This test requires Windows DPAPI which is not available on the current platform.");
                return;
            }

            // Arrange
            var udpSecurity = await UdpSecurityOverride.CreateAsync(keyNameOrPath: TestKeyName);

            // Act
            var securedData = udpSecurity.SecureData(_testData);

            // Assert
            Assert.IsNotNull(securedData);
            Assert.IsTrue(securedData.Length > _testData.Length, "Secured data should be larger than original");
            Assert.IsTrue(securedData.Length >= _testData.Length + 40, "Secured data should include timestamp and MAC");
        }

        [TestMethod]
        public async Task UdpSecurityOverride_SecureData_NullData_ThrowsException()
        {
            // Skip test if DPAPI is not available
            if (!IsDpapiAvailable)
            {
                Assert.Inconclusive("This test requires Windows DPAPI which is not available on the current platform.");
                return;
            }

            // Arrange
            var udpSecurity = await UdpSecurityOverride.CreateAsync(keyNameOrPath: TestKeyName);

            // Act & Assert
            Assert.ThrowsException<ArgumentNullException>(() =>
            {
                udpSecurity.SecureData(null!);
            });
        }

        [TestMethod]
        public async Task UdpSecurityOverride_VerifyAndExtractData_ValidSecuredData_Success()
        {
            // Skip test if DPAPI is not available
            if (!IsDpapiAvailable)
            {
                Assert.Inconclusive("This test requires Windows DPAPI which is not available on the current platform.");
                return;
            }

            // Arrange
            var udpSecurity = await UdpSecurityOverride.CreateAsync(keyNameOrPath: TestKeyName);
            var securedData = udpSecurity.SecureData(_testData);

            // Act
            var extractedData = udpSecurity.VerifyAndExtractData(securedData);

            // Assert
            Assert.IsNotNull(extractedData);
            CollectionAssert.AreEqual(_testData, extractedData);
        }

        [TestMethod]
        public async Task UdpSecurityOverride_VerifyAndExtractData_NullData_ThrowsException()
        {
            // Skip test if DPAPI is not available
            if (!IsDpapiAvailable)
            {
                Assert.Inconclusive("This test requires Windows DPAPI which is not available on the current platform.");
                return;
            }

            // Arrange
            var udpSecurity = await UdpSecurityOverride.CreateAsync(keyNameOrPath: TestKeyName);

            // Act & Assert
            Assert.ThrowsException<ArgumentNullException>(() =>
            {
                udpSecurity.VerifyAndExtractData(null!);
            });
        }

        [TestMethod]
        public async Task UdpSecurityOverride_VerifyAndExtractData_TooSmallData_ThrowsException()
        {
            // Skip test if DPAPI is not available
            if (!IsDpapiAvailable)
            {
                Assert.Inconclusive("This test requires Windows DPAPI which is not available on the current platform.");
                return;
            }

            // Arrange
            var udpSecurity = await UdpSecurityOverride.CreateAsync(keyNameOrPath: TestKeyName);
            var tooSmallData = new byte[10]; // Less than minimum 40 bytes

            // Act & Assert
            Assert.ThrowsException<CryptographicException>(() =>
            {
                udpSecurity.VerifyAndExtractData(tooSmallData);
            });
        }

        [TestMethod]
        public async Task UdpSecurityOverride_VerifyAndExtractData_TamperedData_ThrowsException()
        {
            // Skip test if DPAPI is not available
            if (!IsDpapiAvailable)
            {
                Assert.Inconclusive("This test requires Windows DPAPI which is not available on the current platform.");
                return;
            }

            // Arrange
            var udpSecurity = await UdpSecurityOverride.CreateAsync(keyNameOrPath: TestKeyName);
            var securedData = udpSecurity.SecureData(_testData);

            // Tamper with the data
            securedData[0] = (byte)(securedData[0] ^ 0xFF);

            // Act & Assert
            Assert.ThrowsException<CryptographicException>(() =>
            {
                udpSecurity.VerifyAndExtractData(securedData);
            });
        }

        [TestMethod]
        public async Task UdpSecurityOverride_VerifyAndExtractData_ExpiredTimestamp_ThrowsException()
        {
            // Skip test if DPAPI is not available
            if (!IsDpapiAvailable)
            {
                Assert.Inconclusive("This test requires Windows DPAPI which is not available on the current platform.");
                return;
            }

            // Arrange
            var udpSecurity = await UdpSecurityOverride.CreateAsync(
                keyNameOrPath: TestKeyName,
                replayWindowMinutes: 1, // Very short window
                enableTimestampValidation: true);

            var securedData = udpSecurity.SecureData(_testData);

            // FIXED: Correctly calculate timestamp position in secured data
            // Secured data format: [original data][timestamp][HMAC]
            var originalDataSize = _testData.Length;
            var timestampOffset = originalDataSize; // Timestamp comes right after original data

            var oldTimestamp = DateTimeOffset.UtcNow.AddMinutes(-10).ToUnixTimeSeconds(); // 10 minutes ago
            var timestampBytes = BitConverter.GetBytes(oldTimestamp);

            // Verify we have the correct offset and size
            if (timestampOffset + 8 > securedData.Length)
            {
                Assert.Fail($"Invalid timestamp offset calculation. Offset: {timestampOffset}, SecuredData length: {securedData.Length}");
            }

            Array.Copy(timestampBytes, 0, securedData, timestampOffset, 8);

            // Act & Assert
            Assert.ThrowsException<CryptographicException>(() =>
            {
                udpSecurity.VerifyAndExtractData(securedData);
            });
        }

        [TestMethod]
        public async Task UdpSecurityOverride_VerifyAndExtractData_DisabledTimestampValidation_Success()
        {
            // Skip test if DPAPI is not available
            if (!IsDpapiAvailable)
            {
                Assert.Inconclusive("This test requires Windows DPAPI which is not available on the current platform.");
                return;
            }

            // Arrange
            var udpSecurity = await UdpSecurityOverride.CreateAsync(
                keyNameOrPath: TestKeyName,
                enableTimestampValidation: false); // Disable timestamp validation

            var securedData = udpSecurity.SecureData(_testData);

            // Act
            var extractedData = udpSecurity.VerifyAndExtractData(securedData);

            // Assert
            Assert.IsNotNull(extractedData);
            CollectionAssert.AreEqual(_testData, extractedData);
        }

        [TestMethod]
        public async Task UdpSecurityOverride_RoundTrip_MultipleMessages_Success()
        {
            // Skip test if DPAPI is not available
            if (!IsDpapiAvailable)
            {
                Assert.Inconclusive("This test requires Windows DPAPI which is not available on the current platform.");
                return;
            }

            // Arrange
            var udpSecurity = await UdpSecurityOverride.CreateAsync(keyNameOrPath: TestKeyName);
            var messages = new[]
            {
                Encoding.UTF8.GetBytes("Message 1"),
                Encoding.UTF8.GetBytes("Message 2 with more content"),
                Encoding.UTF8.GetBytes(""),
                new byte[] { 0x01, 0x02, 0x03, 0xFF, 0xFE }
            };

            // Act & Assert
            foreach (var message in messages)
            {
                var secured = udpSecurity.SecureData(message);
                var extracted = udpSecurity.VerifyAndExtractData(secured);
                CollectionAssert.AreEqual(message, extracted, $"Round trip failed for message: {Convert.ToHexString(message)}");
            }
        }

        [TestMethod]
        public async Task UdpSecurityOverride_DifferentInstances_SameKey_Success()
        {
            // Skip test if DPAPI is not available
            if (!IsDpapiAvailable)
            {
                Assert.Inconclusive("This test requires Windows DPAPI which is not available on the current platform.");
                return;
            }

            // Arrange
            var udpSecurity1 = await UdpSecurityOverride.CreateAsync(keyNameOrPath: TestKeyName);
            var udpSecurity2 = await UdpSecurityOverride.CreateAsync(keyNameOrPath: TestKeyName);

            // Act
            var securedData = udpSecurity1.SecureData(_testData);
            var extractedData = udpSecurity2.VerifyAndExtractData(securedData);

            // Assert
            CollectionAssert.AreEqual(_testData, extractedData);
        }

        [TestMethod]
        public async Task UdpSecurityOverride_DifferentKeys_VerificationFails()
        {
            // Skip test if DPAPI is not available
            if (!IsDpapiAvailable)
            {
                Assert.Inconclusive("This test requires Windows DPAPI which is not available on the current platform.");
                return;
            }

            // Arrange
            var testKeyName2 = TestKeyName + "2";
            var udpSecurity1 = await UdpSecurityOverride.CreateAsync(keyNameOrPath: TestKeyName);
            var udpSecurity2 = await UdpSecurityOverride.CreateAsync(keyNameOrPath: testKeyName2);

            try
            {
                // Act
                var securedData = udpSecurity1.SecureData(_testData);

                // Assert
                Assert.ThrowsException<CryptographicException>(() =>
                {
                    udpSecurity2.VerifyAndExtractData(securedData);
                });
            }
            finally
            {
                // Cleanup
                try
                {
                    if (System.IO.File.Exists(testKeyName2))
                    {
                        System.IO.File.Delete(testKeyName2);
                    }
                }
                catch { }
            }
        }
    }
}
