using System;
using System.Collections.Generic;
using System.Security.Cryptography;
using System.Threading.Tasks;
using NSec.Cryptography;

namespace USDP2
{
    /// <summary>
    /// Manages key rotation for cryptographic keys used in the USDP2 protocol.
    /// </summary>
    public class KeyRotationManager
    {
        private readonly ConfigurationProvider _configProvider;
        private readonly Dictionary<string, DateTime> _keyExpirations;
        private readonly TimeSpan _defaultRotationPeriod;

        /// <summary>
        /// Initializes a new instance of the <see cref="KeyRotationManager"/> class.
        /// </summary>
        /// <param name="configProvider">The configuration provider.</param>
        /// <param name="defaultRotationPeriod">The default key rotation period.</param>
        public KeyRotationManager(
            ConfigurationProvider configProvider,
            TimeSpan defaultRotationPeriod = default)
        {
            _configProvider = configProvider ?? throw new ArgumentNullException(nameof(configProvider));
            _defaultRotationPeriod = defaultRotationPeriod == default ? TimeSpan.FromDays(90) : defaultRotationPeriod;
            _keyExpirations = new Dictionary<string, DateTime>();
        }

        /// <summary>
        /// Checks if a key needs rotation based on its age.
        /// </summary>
        /// <param name="keyName">The name of the key.</param>
        /// <returns>True if the key needs rotation, false otherwise.</returns>
        public bool NeedsRotation(string keyName)
        {
            if (_keyExpirations.TryGetValue(keyName, out DateTime expiration))
            {
                return DateTime.UtcNow >= expiration;
            }

            // If we don't have expiration info, assume it needs rotation
            return true;
        }

        /// <summary>
        /// Rotates a key and updates its expiration date.
        /// </summary>
        /// <param name="keyName">The name of the key.</param>
        /// <param name="keyType">The type of key to generate.</param>
        /// <param name="rotationPeriod">The rotation period for this key.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        public async Task RotateKeyAsync(
            string keyName,
            KeyType keyType,
            TimeSpan? rotationPeriod = null)
        {
            // Generate a new key based on the key type
            byte[] newKey = GenerateKey(keyType);

            // Store the key using the configuration provider
            await StoreKeyAsync(keyName, newKey);

            // Update expiration time
            var period = rotationPeriod ?? _defaultRotationPeriod;
            _keyExpirations[keyName] = DateTime.UtcNow.Add(period);

            // Log the rotation
            Diagnostics.Log("KeyRotated", new { keyName, nextRotation = _keyExpirations[keyName] });
        }

        /// <summary>
        /// Stores a key using the configuration provider.
        /// </summary>
        /// <param name="keyName">The name of the key.</param>
        /// <param name="keyData">The key data.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        private async Task StoreKeyAsync(string keyName, byte[] keyData)
        {
            // Implementation depends on the configuration provider
            // This is a simplified example
            _configProvider.SetValue($"Keys:{keyName}", Convert.ToBase64String(keyData));
            await _configProvider.SaveAsync();
        }

        /// <summary>
        /// Generates a new cryptographic key of the specified type.
        /// </summary>
        /// <param name="keyType">The type of key to generate.</param>
        /// <returns>The generated key as a byte array.</returns>
        private static byte[] GenerateKey(KeyType keyType)
        {
            switch (keyType)
            {
                case KeyType.Ed25519:
                    // Ed25519Helper is static - no need for using statement
                    var ed25519Key = Ed25519Helper.CreatePrivateKey();
                    return ed25519Key.Export(KeyBlobFormat.RawPrivateKey);

                case KeyType.AES256:
                    using (var aes = Aes.Create())
                    {
                        aes.KeySize = 256;
                        aes.GenerateKey();
                        return aes.Key;
                    }

                case KeyType.PSK:
                    // Generate a random 32-byte PSK
                    var psk = new byte[32];
                    using (var rng = RandomNumberGenerator.Create())
                    {
                        rng.GetBytes(psk);
                    }
                    return psk;

                default:
                    throw new ArgumentException($"Unsupported key type: {keyType}");
            }
        }
    }

    /// <summary>
    /// Represents the type of cryptographic key.
    /// </summary>
    public enum KeyType
    {
        /// <summary>
        /// Ed25519 key for digital signatures.
        /// </summary>
        Ed25519,

        /// <summary>
        /// AES-256 key for symmetric encryption.
        /// </summary>
        AES256,

        /// <summary>
        /// Pre-shared key for authentication.
        /// </summary>
        PSK,
        
    }
}