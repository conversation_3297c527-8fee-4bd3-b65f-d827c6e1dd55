using System;
using System.Text.Json;

namespace USDP2
{
    /// <summary>
    /// Extension methods for handling serialization exceptions.
    /// </summary>
    public static class SerializationExceptionExtensions
    {
        // Cached JsonSerializerOptions for better performance in error logging
        private static readonly JsonSerializerOptions _errorSerializerOptions = new JsonSerializerOptions
        {
            WriteIndented = false
        };

        /// <summary>
        /// Creates a detailed error message for serialization exceptions.
        /// </summary>
        /// <param name="ex">The exception that occurred.</param>
        /// <param name="context">Additional context information.</param>
        /// <returns>A detailed error message.</returns>
        public static string CreateDetailedErrorMessage(this Exception ex, object? context = null)
        {
            var errorDetails = new
            {
                Message = ex.Message,
                ExceptionType = ex.GetType().Name,
                StackTrace = ex.StackTrace,
                InnerException = ex.InnerException?.Message,
                Context = context
            };

            try
            {
                return JsonSerializer.Serialize(errorDetails, _errorSerializerOptions);
            }
            catch
            {
                // Fallback if JSON serialization fails
                return $"Error: {ex.Message} ({ex.GetType().Name})";
            }
        }

        /// <summary>
        /// Logs a serialization exception with detailed information.
        /// </summary>
        /// <param name="ex">The exception that occurred.</param>
        /// <param name="operation">The serialization operation being performed.</param>
        /// <param name="typeName">The name of the type being serialized/deserialized.</param>
        /// <param name="additionalContext">Additional context information.</param>
        public static void LogSerializationException(
            this Exception ex, 
            string operation, 
            string typeName, 
            object? additionalContext = null)
        {
            var context = new
            {
                Operation = operation,
                Type = typeName,
                AdditionalInfo = additionalContext
            };

            UsdpLogger.Log($"SerializationError.{operation}", new
            {
                Type = typeName,
                Error = ex.Message,
                Exception = ex.GetType().Name,
                Details = ex.CreateDetailedErrorMessage(context)
            });
        }
    }
}